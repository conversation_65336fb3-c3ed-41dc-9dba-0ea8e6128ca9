<?php

namespace TF\Engine\APIClasses\Owners;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Kernel\MTRpcException;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\UserDbOwners\UserDbOwnersController;
use TF\Engine\Plugins\Core\UserDbPayments\UserDbPaymentsController;
use TF\Engine\Plugins\Core\Users\UsersController;

/**
 * Owner heritors.
 *
 * @rpc-module Owners
 *
 * @rpc-service-id owners-heritors-tree
 *
 * @property UserDbOwnersController $UserDbOwnersController
 * @property UsersController $UsersController
 * @property UserDbController $UserDbController
 */
class OwnersHeritorsTree extends TRpcApiProvider
{
    private $module = 'Owners';
    private $service_id = 'owners-heritors-tree';

    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'readOwnersHeritorsTree']],
            'addOwnerHeritor' => ['method' => [$this, 'addOwnerHeritor'],
                'validators' => [
                    'ownerId' => 'validateInteger, validateRequired, validateNotNull',
                    'parent' => 'validateInteger, validateRequired, validateNotNull',
                ],
            ],
            'deleteOwnerHeritor' => ['method' => [$this, 'deleteOwnerHeritor'],
                'validators' => [
                    'path' => 'validateText , validateRequired , validateNotNull ',
                ],
            ],
        ];
    }

    /**
     * Read Owners Heritors Tree.
     *
     * @api-method read
     *
     * @param string $path the heritor path
     *
     * @return array {
     *               #item string iconCls
     *               #item integer id
     *               #item string state
     *               #item string text
     *               #item array attributes {
     *               #item string iconCls
     *               #item integer id
     *               #item boolean is_dead
     *               #item integer owner_id
     *               #item string owner_names
     *               #item string path
     *               }
     *               }
     */
    public function readOwnersHeritorsTree($path)
    {
        $UserDbOwnersController = new UserDbOwnersController($this->User->Database);
        $return = [];
        $options = [
            'return' => [
                'h.id', "name || ' ' || surname || ' ' || lastname as owner_names", 'owner_id', 'is_dead', 'path',
            ],
            'where' => [
                'path' => ['column' => 'path', 'compare' => '~', 'value' => $path['path']],
            ],
        ];

        $counter = $UserDbOwnersController->getOwnersHeritors($options, true);

        if (0 == $counter[0]['count']) {
            return [
                'rows' => [],
                'total' => 0,
            ];
        }

        $results = $UserDbOwnersController->getOwnersHeritors($options, false, false);
        $resultsCount = count($results);
        for ($i = 0; $i < $resultsCount; $i++) {
            if ($results[$i]['is_dead']) {
                $results[$i]['iconCls'] = 'icon-tree-user-rip';
            } else {
                $results[$i]['iconCls'] = 'icon-tree-user';
            }

            $return[] = [
                'text' => $results[$i]['owner_names'],
                'id' => $results[$i]['id'],
                'state' => 'closed',
                'attributes' => $results[$i],
                'iconCls' => $results[$i]['iconCls'],
            ];
        }

        return $return;
    }

    /**
     * Add Owner Heritor.
     *
     * @api-method addOwnerHeritor
     *
     * @param int $ownerId
     * @param int $parent
     *
     * @throws MTRpcException
     */
    public function addOwnerHeritor($ownerId, $parent)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $UsersController = new UsersController('Users');
        $UserDbOwnersController = new UserDbOwnersController($this->User->Database);

        $this->checkIfOwnerIsDead($parent);

        $options = [
            'tablename' => $UserDbController->DbHandler->tableHeritors,
            'mainData' => [
                'owner_id' => $ownerId,
                'path' => $parent . '.' . $ownerId,
            ],
        ];

        $recordID = $UserDbController->addItem($options);
        $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, ['parent_id' => $parent], ['heritor_id' => $ownerId], 'Adding owner heritor');

        $options = [
            'tablename' => $UserDbController->DbHandler->tableHeritors,
            'return' => [
                '*',
            ],
            'where' => [
                'path' => ['column' => 'path', 'compare' => '~', 'value' => $ownerId . '.*'],
            ],
        ];
        $results = $UserDbController->getItemsByParams($options);
        $resultsCount = count($results);
        $options = [
            'tablename' => $UserDbController->DbHandler->tableHeritors,
            'return' => [
                '*',
            ],
            'where' => [
                'path' => ['column' => 'path', 'compare' => '~', 'value' => '*.' . $parent],
            ],
        ];
        $parent_results = $UserDbController->getItemsByParams($options);
        $parent_results[] = ['path' => $parent];
        $parentResCount = count($parent_results);
        for ($j = 0; $j < $parentResCount; $j++) {
            for ($i = 0; $i < $resultsCount; $i++) {
                $options = [
                    'tablename' => $UserDbController->DbHandler->tableHeritors,
                    'mainData' => [
                        'owner_id' => $results[$i]['owner_id'],
                        'path' => $parent_results[$j]['path'] . '.' . $results[$i]['path'],
                    ],
                ];

                $UserDbController->addItem($options);
            }
        }

        $options = [
            'tablename' => $UserDbController->DbHandler->tableHeritors,
            'return' => [
                '*',
            ],
            'where' => [
                'path' => ['column' => 'path', 'compare' => '~', 'value' => '*.' . $parent],
            ],
        ];
        $results = $UserDbController->getItemsByParams($options);
        $resultsCount = count($results);
        for ($i = 0; $i < $resultsCount; $i++) {
            $options = [
                'tablename' => $UserDbController->DbHandler->tableHeritors,
                'mainData' => [
                    'owner_id' => $ownerId,
                    'path' => $results[$i]['path'] . '.' . $ownerId,
                ],
            ];

            $UserDbController->addItem($options);
        }

        $UserDbController->disableRentaMatViewTriggers();
        $UserDbOwnersController->createPlotsOwnersRelation($parent);
        $UserDbController->enableRentaMatViewTriggers();
        $UserDbController->refreshRentaViews();
    }

    /**
     * Deletes owner heritor.
     *
     * @api-method deleteOwnerHeritor
     *
     * @param string $path
     */
    public function deleteOwnerHeritor($path)
    {
        $UserDbControllerr = new UserDbController($this->User->Database);
        $UsersController = new UsersController('Users');
        $UserDbOwnersController = new UserDbOwnersController($this->User->Database);
        $UserDbPaymentsController = new UserDbPaymentsController($this->User->Database);

        $options = [
            'tablename' => $UserDbControllerr->DbHandler->tableHeritors,
            'return' => [
                'owner_id',
                'id',
            ],
            'where' => [
                'path' => ['column' => 'path', 'compare' => '~', 'value' => '*.' . $path . '.*'],
            ],
        ];
        $results = $UserDbControllerr->getItemsByParams($options);

        $options = [
            'custom_counter' => 'count(DISTINCT(p.id))',
            'return' => [
                'p.id',
            ],

            'where' => [
                'owner_id' => ['column' => 'path', 'compare' => '~', 'prefix' => 'p', 'value' => '*.' . $path . '.*'],
            ],
            'group' => 'p.id, c.id, o.id',
        ];

        $payments = $UserDbPaymentsController->getPaymentsByParams($options, false, false);

        if (count($payments) > 0) {
            throw new MTRpcException('CANNOT_REMOVE_HERRITOR_WITH_PAYMENTS', -34151);
        }

        $id_array = [];
        $resultsCount = count($results);
        for ($i = 0; $i < $resultsCount; $i++) {
            $id_array[] = $results[$i]['owner_id'];
        }
        $id_string = implode(',', $id_array);

        $options = [
            'tablename' => $UserDbControllerr->DbHandler->tableHeritors,
            'id_name' => 'owner_id',
            'id_string' => $id_string,
            'where' => [
                'path' => ['column' => 'path', 'compare' => '~', 'value' => '*.' . $path . '.*'],
            ],
        ];

        $UserDbControllerr->deleteItemsByParams($options);
        $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, [], ['deleted_owner_path' => $path], 'Deleting owner heritor');

        $UserDbControllerr->disableRentaMatViewTriggers();
        $this->deleteOwnerHeritorFromPlotsOwnersRel($path);
        $UserDbOwnersController->createPlotsOwnersRelation($path);
        $UserDbControllerr->enableRentaMatViewTriggers();
        $UserDbControllerr->refreshRentaViews();
    }

    /**
     * Deletes the relation between the heritor and the plot.
     *
     * @param string $path
     */
    public function deleteOwnerHeritorFromPlotsOwnersRel($path)
    {
        $UserDbControllerr = new UserDbController($this->User->Database);
        $UsersController = new UsersController('Users');

        $options = [
            'tablename' => $UserDbControllerr->DbHandler->plotsOwnersRelTable,
            'return' => [
                'id',
            ],
            'where' => [
                'path' => ['column' => 'path', 'compare' => '~', 'value' => '*.' . $path . '.*'],
            ],
        ];
        $results = $UserDbControllerr->getItemsByParams($options);
        $id_array = [];
        $resultsCount = count($results);
        if (!empty($results)) {
            for ($i = 0; $i < $resultsCount; $i++) {
                $id_array[] = $results[$i]['id'];
            }
            $id_string = implode(',', $id_array);

            $options = [
                'tablename' => $UserDbControllerr->DbHandler->plotsOwnersRelTable,
                'id_name' => 'id',
                'id_string' => $id_string,
                'where' => [
                    'path' => ['column' => 'path', 'compare' => '~', 'value' => '*.' . $path . '.*'],
                ],
            ];

            $UserDbControllerr->deleteItemsByParams($options);
            $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, ['requested_path' => $path], ['affected_owners' => $id_string], 'Deleting owner heritor path');
        }
    }

    /**
     * @throws MTRpcException
     */
    private function checkIfOwnerIsDead($parent)
    {
        $UserDbControllerr = new UserDbController($this->User->Database);

        $options = [
            'tablename' => $UserDbControllerr->DbHandler->tableOwners,
            'return' => [
                '*',
            ],
            'where' => [
                'id' => ['column' => 'id', 'compare' => '=', 'value' => $parent],
            ],
        ];
        $results = $UserDbControllerr->getItemsByParams($options);

        if (0 == count($results)) {
            throw new MTRpcException('INVALID_OWNER_ID', -33301);
        }

        if (!$results[0]['is_dead']) {
            throw new MTRpcException('CANNOT_ADD_HERITOR_TO_ALIVE_OWNER', -33303);
        }
    }
}
