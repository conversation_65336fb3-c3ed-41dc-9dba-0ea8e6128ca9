<?php

namespace TF\Engine\APIClasses\Common;

use Prado\Web\Services\TRpcApiProvider;
use TF\Engine\Plugins\Core\UserDb\UserDbController;

/**
 * Returns all possible renta types for this user.
 *
 * @rpc-module Common
 *
 * @rpc-service-id renta-types-combobox
 */
class RentaTypesOptionsCombobox extends TRpcApiProvider
{
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'readRentaTypesOptionsCombobox']],
        ];
    }

    /**
     * @api-method read
     *
     * @param array $rpcParams
     *                         {
     *                         #item boolean selected
     *                         }
     *
     * @return array
     *               {
     *               #item int id
     *               #item string name
     *               #item int unit
     *               #item string unit_value
     *               #item boolean selected
     *               }
     */
    public function readRentaTypesOptionsCombobox($rpcParams = [])
    {
        // init controllers
        $UserDbController = new UserDbController($this->User->Database);

        $options = [
            'tablename' => $UserDbController->DbHandler->tableRentaTypesOptions,
        ];
        $results = $UserDbController->getItemsByParams($options);

        if (!empty($rpcParams['allValuesOption'])) {
            array_unshift($results, [
                'id' => -1,
                'name' => 'Всички типове рента',
                'unit' => 0,
                'selected' => true,
            ]);
        }

        if (isset($rpcParams['selected']) && true == $rpcParams['selected']) {
            $results[0]['selected'] = true;
        }

        if (isset($rpcParams['as_list']) && true == $rpcParams['as_list']) {
            return $results;
        }

        return $results;
    }
}
