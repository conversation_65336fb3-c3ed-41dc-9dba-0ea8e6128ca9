<?php

namespace TF\Engine\APIClasses\Common;

use Prado\Web\Services\TRpcApiProvider;
use TF\Application\Entity\ObjectPermissions;
use TF\Application\Entity\UserFarmings;

/**
 * Get all EKATTE areas, associated with the current user.
 *
 * @rpc-module Common
 *
 * @rpc-service-id ekate-combobox
 */
class CombinedComboboxData extends TRpcApiProvider
{
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getCombinedComboboxData']],
        ];
    }

    /**
     * @api-method read
     *
     * @param bool $withVPS
     * @param null|array $filter
     *
     * @return array
     *
     * @note TS-3278: added $filter param to filter which query to return.
     */
    public function getCombinedComboboxData($withVPS = false, $filter = null, $configParams = [])
    {
        // if not nul $rpcParams
        $return = [];
        $rpcParams = [];
        if ($this->checkFilter($filter, 'ContractAggTypesCombobox')) {
            $return['ContractAggTypesCombobox'] = $this->getContractAggTypesCombobox($configParams);
        }
        if ($this->checkFilter($filter, 'ContractStatusCombobox')) {
            $return['ContractStatusCombobox'] = $this->getContractStatusCombobox($configParams);
        }
        if ($this->checkFilter($filter, 'ContractTemplateVariables')) {
            $return['ContractTemplateVariables'] = $this->getContractTemplateVariables($configParams);
        }
        if ($this->checkFilter($filter, 'ContractTypeCombobox')) {
            $return['ContractTypeCombobox'] = $this->getContractTypeCombobox($configParams);
        }
        if ($this->checkFilter($filter, 'DSTypeCombobox')) {
            $return['DSTypeCombobox'] = $this->getDSTypeCombobox($configParams);
        }
        if ($this->checkFilter($filter, 'EkateCombobox')) {
            $return['EkateCombobox'] = $this->getEkateCombobox($configParams);
        }
        if ($this->checkFilter($filter, 'FarmingCombobox')) {
            $return['FarmingCombobox'] = $this->getFarmingCombobox($configParams);
        }
        if ($this->checkFilter($filter, 'FarmNameCombobox')) {
            $return['FarmNameCombobox'] = $this->getFarmNameCombobox($configParams);
        }
        if ($this->checkFilter($filter, 'IrrigatedAreaCombobox')) {
            $return['IrrigatedAreaCombobox'] = $this->getIrrigatedAreaCombobox($configParams);
        }
        if ($this->checkFilter($filter, 'IsakEkatteCombobox')) {
            $return['IsakEkatteCombobox'] = $this->getAllIsakEkatteCombobox($configParams);
        }
        if ($this->checkFilter($filter, 'ForIsakEkateCombobox')) {
            $return['ForIsakEkateCombobox'] = $this->getAllForIsakEkatteCombobox($configParams);
        }
        if ($this->checkFilter($filter, 'MestnostCombobox')) {
            $return['MestnostCombobox'] = $this->getMestnostCombobox($configParams);
        }
        if ($this->checkFilter($filter, 'CultureCombobox')) {
            $return['CultureCombobox'] = $this->getCultureCombobox($configParams);
        }
        if ($this->checkFilter($filter, 'OSZEkateCombobox')) {
            $return['OSZEkateCombobox'] = $this->getOSZEkateCombobox($configParams);
        }
        if ($this->checkFilter($filter, 'PlotCategoryCombobox')) {
            $return['PlotCategoryCombobox'] = $this->getPlotCategoryCombobox($configParams);
        }
        if ($this->checkFilter($filter, 'PlotNTPCombobox')) {
            $return['PlotNTPCombobox'] = $this->getPlotNTPCombobox($configParams);
        }
        if ($this->checkFilter($filter, 'RentaTypesCombobox')) {
            $return['RentaTypesCombobox'] = $this->getRentaTypesCombobox($configParams);
        }
        if ($this->checkFilter($filter, 'RentaTypesOptionsCombobox')) {
            $return['RentaTypesOptionsCombobox'] = $this->getRentaTypesOptionsCombobox($configParams);
        }
        if ($this->checkFilter($filter, 'RentaUnitsCombobox')) {
            $return['RentaUnitsCombobox'] = $this->getRentaUnitsCombobox($configParams);
        }
        if ($this->checkFilter($filter, 'SubleasedContractType')) {
            $return['SubleasedContractType'] = $this->getSubleasedContractType($configParams);
        }
        if ($this->checkFilter($filter, 'SubleaseTypeCombobox')) {
            $return['SubleaseTypeCombobox'] = $this->getSubleaseTypeCombobox($configParams);
        }
        if ($this->checkFilter($filter, 'TopicLayerStylerCombobox')) {
            $return['TopicLayerStylerCombobox'] = $this->getTopicLayerStylerCombobox($configParams);
        }
        if ($this->checkFilter($filter, 'TransactionTypesCombobox')) {
            $return['TransactionTypesCombobox'] = $this->getTransactionTypesCombobox($configParams);
        }
        if ($this->checkFilter($filter, 'FarmingYearCombobox')) {
            $return['FarmingYearCombobox'] = $this->getFarmingYearCombobox($configParams);
        }
        if ($this->checkFilter($filter, 'getFarmingYearWithCalendarYearSelectedCombobox')) {
            $return['getFarmingYearWithCalendarYearSelectedCombobox'] = $this->getFarmingYearWithCalendarYearSelectedCombobox($configParams);
        }
        if ($this->checkFilter($filter, 'ModemsCombobox')) {
            $return['ModemsCombobox'] = $this->getModemsCombobox($configParams);
        }
        if ($this->checkFilter($filter, 'OwnerTypesCombobox')) {
            $return['OwnerTypesCombobox'] = $this->getOwnerTypesCombobox($configParams);
        }
        if ($this->checkFilter($filter, 'ConsolidationDoDogKodCombobox')) {
            $return['ConsolidationDoDogKodCombobox'] = $this->********************************();
        }
        if ($this->checkFilter($filter, 'ConsolidationZdDogKodCombobox')) {
            $return['ConsolidationZdDogKodCombobox'] = $this->********************************();
        }
        if ($this->checkFilter($filter, 'ConsolidationZdJelanieCombobox')) {
            $return['ConsolidationZdJelanieCombobox'] = $this->getConsolidationZdJelanieCombobox();
        }
        if ($this->checkFilter($filter, 'ConsolidationZdKodCombobox')) {
            $return['ConsolidationZdKodCombobox'] = $this->getConsolidationZdKodCombobox();
        }
        if ($this->checkFilter($filter, 'CountriesCombobox')) {
            $return['CountriesCombobox'] = $this->getCountriesCombobox();
        }
        if ($this->checkFilter($filter, 'ContractGroupsCombobox')) {
            $return['ContractGroupsCombobox'] = $this->getContractGroupsCombobox();
        }

        $return['CurrentFarmingYear'] = getCurrentFarmingYear();

        $return['UserFarmingPermissions'] = $this->User->getPermissionObjectIds(ObjectPermissions::PERMISSION_READ, UserFarmings::class);

        if ($withVPS) {
            $return['VPSZimniGaskiEkateCombobox'] = $this->getVPSZimniGaskiEkateCombobox($configParams);
            $return['VPSOrliLeshoyadiEkateCombobox'] = $this->********************************($configParams);
            $return['VPSLivadenBlatarNameCombobox'] = $this->getVPSLivadenBlatarNameCombobox($configParams);
            $return['VPSChervenogushiGaskiEkateCombobox'] = $this->getVPSChervenogushiGaskiEkateCombobox($configParams);
            $return['AllowableEkateCombobox'] = $this->getAllowableEkateCombobox($configParams);
            $return['PZPEkateCombobox'] = $this->getPZPEkateCombobox($configParams);
            $return['LFAEkateCombobox'] = $this->getLFAEkateCombobox($configParams);
            $return['Natura2000NameCombobox'] = $this->getNatura2000NameCombobox($configParams);
            $return['MapTypesCombobox'] = $this->getMapTypesCombobox($configParams);
        }

        return $return;
    }

    /**
     * @return bool
     */
    private function checkFilter($filter, $key)
    {
        return (!isset($filter) || (is_array($filter) && in_array($key, $filter)));
    }

    private function getContractAggTypesCombobox($configParams = [])
    {
        $combobox = new ContractAggTypesCombobox($this->rpcServer);
        $rpcParams['selected'] = $configParams['contract_agg_types_selected'] ?? false;
        $rpcParams['record_all'] = $configParams['contract_agg_record_all'] ?? true;

        return $combobox->getContractAggTypesCombobox($rpcParams);
    }

    private function getContractStatusCombobox($configParams = [])
    {
        $combobox = new ContractStatusCombobox($this->rpcServer);
        $rpcParams['selected'] = $configParams['contract_status_selected'] ?? false;
        $rpcParams['record_all'] = $configParams['contract_status_record_all'] ?? true;

        return $combobox->getContractStatus($rpcParams);
    }

    private function getContractTemplateVariables($configParams = [])
    {
        $combobox = new ContractTemplateVariables($this->rpcServer);
        $rpcParams['selected'] = $configParams['contract_template_variables_selected'] ?? false;
        $rpcParams['record_all'] = $configParams['contract_template_variables_record_all'] ?? true;

        return $combobox->readContractTemplateVariables($rpcParams);
    }

    private function getContractTypeCombobox($configParams = [])
    {
        $combobox = new ContractTypeCombobox($this->rpcServer);
        $rpcParams['selected'] = $configParams['contract_type_selected'] ?? false;
        $rpcParams['record_all'] = $configParams['contract_type_record_all'] ?? true;

        return $combobox->getContractType($rpcParams);
    }

    private function getDSTypeCombobox($configParams = [])
    {
        $combobox = new DSTypeCombobox($this->rpcServer);
        $rpcParams['selected'] = $configParams['ds_type_selected'] ?? false;
        $rpcParams['record_all'] = $configParams['ds_type_record_all'] ?? true;

        return $combobox->getDSTypeCombobox($rpcParams);
    }

    private function getEkateCombobox($configParams = [])
    {
        $combobox = new EkateCombobox($this->rpcServer);
        $rpcParams['selected'] = $configParams['ekate_selected'] ?? false;
        $rpcParams['record_all'] = $configParams['ekate_record_all'] ?? true;

        return $combobox->getEKATTECombox($rpcParams);
    }

    private function getFarmingCombobox($configParams = [])
    {
        $combobox = new FarmingCombobox($this->rpcServer);
        $rpcParams['selected'] = $configParams['farming_selected'] ?? false;
        $rpcParams['record_all'] = $configParams['farming_record_all'] ?? true;

        return $combobox->getFarmingType($rpcParams);
    }

    private function getFarmNameCombobox($configParams = [])
    {
        $combobox = new FarmNameCombobox($this->rpcServer);
        $rpcParams['selected'] = $configParams['farm_name_selected'] ?? false;
        $rpcParams['record_all'] = $configParams['farm_name_record_all'] ?? true;

        return $combobox->getFarmNameCombobox($rpcParams);
    }

    private function getIrrigatedAreaCombobox($configParams = [])
    {
        $combobox = new IrrigatedAreaCombobox($this->rpcServer);
        $rpcParams['selected'] = $configParams['irrigated_area_selected'] ?? false;
        $rpcParams['include_all'] = $configParams['irrigated_area_record_all'] ?? true;

        return $combobox->getIrrigatedAreaCombobox($rpcParams);
    }

    private function getAllIsakEkatteCombobox($configParams = [])
    {
        $rpcParams = $configParams;
        $combobox = new IsakEkatteCombobox($this->rpcServer);

        return $combobox->getAllIsakEkatte($rpcParams);
    }

    private function getAllForIsakEkatteCombobox($configParams = [])
    {
        $rpcParams = $configParams;
        $combobox = new ForIsakEkateCombobox($this->rpcServer);

        return $combobox->getAllForIsakEkatte($rpcParams);
    }

    private function getMestnostCombobox($configParams = [])
    {
        $combobox = new MestnostCombobox($this->rpcServer);
        $rpcParams['selected'] = $configParams['mestnost_selected'] ?? false;
        $rpcParams['record_all'] = $configParams['mestnost_record_all'] ?? true;
        $rpcParams['ekattes'] = $configParams['ekattes'] ?? true;

        return $combobox->getMestnostCombobox($rpcParams);
    }

    private function getCultureCombobox($configParams = [])
    {
        $combobox = new CultureCombobox($this->rpcServer);
        $rpcParams['record_all'] = $configParams['culture_record_all'] ?? true;

        return $combobox->getCultures($rpcParams);
    }

    private function getOSZEkateCombobox($configParams = [])
    {
        $combobox = new OSZEkateCombobox($this->rpcServer);
        $rpcParams['selected'] = $configParams['osz_ekate_selected'] ?? false;
        $rpcParams['record_all'] = $configParams['osz_ekate_record_all'] ?? true;

        return $combobox->getOSZEkateCombobox($rpcParams);
    }

    private function getPlotCategoryCombobox($configParams = [])
    {
        $combobox = new PlotCategoryCombobox($this->rpcServer);
        $rpcParams['selected'] = $configParams['plot_category_selected'] ?? false;
        $rpcParams['record_all'] = $configParams['plot_category_record_all'] ?? true;
        $rpcParams['without_cat_ntp'] = $configParams['plot_category_without_cat_ntp'] ?? true;

        return $combobox->getPlotCategory($rpcParams);
    }

    private function getPlotNTPCombobox($configParams = [])
    {
        $combobox = new PlotNTPCombobox($this->rpcServer);
        $rpcParams['selected'] = $configParams['plot_ntp_selected'] ?? false;
        $rpcParams['record_all'] = $configParams['plot_ntp_record_all'] ?? true;
        $rpcParams['without_cat_ntp'] = $configParams['plot_ntp_without_cat_ntp'] ?? true;

        return $combobox->getPlotNTPCombobox($rpcParams);
    }

    private function getRentaTypesCombobox($configParams = [])
    {
        $combobox = new RentaTypesCombobox($this->rpcServer);
        $rpcParams['selected'] = $configParams['renta_types_selected'] ?? false;
        $rpcParams['record_all'] = $configParams['renta_types_record_all'] ?? true;

        return $combobox->readRentaTypesCombobox($rpcParams);
    }

    private function getRentaTypesOptionsCombobox($configParams = [])
    {
        $combobox = new RentaTypesOptionsCombobox($this->rpcServer);
        $rpcParams['selected'] = $configParams['renta_types_selected'] ?? false;
        $rpcParams['record_all'] = $configParams['renta_types_record_all'] ?? true;

        return $combobox->readRentaTypesOptionsCombobox($rpcParams);
    }

    private function getRentaUnitsCombobox($configParams = [])
    {
        $combobox = new RentaUnitsCombobox($this->rpcServer);
        $rpcParams['selected'] = $configParams['renta_units_selected'] ?? false;
        $rpcParams['record_all'] = $configParams['renta_units_record_all'] ?? true;

        return $combobox->readRentaUnitsCombobox($rpcParams);
    }

    private function getSubleasedContractType($configParams = [])
    {
        $combobox = new SubleasedContractType($this->rpcServer);
        $rpcParams['selected'] = $configParams['subleased_contract_selected'] ?? false;
        $rpcParams['record_all'] = $configParams['subleased_contract_record_all'] ?? true;

        return $combobox->getSubleasedContractType($rpcParams);
    }

    private function getSubleaseTypeCombobox($configParams = [])
    {
        $combobox = new SubleaseTypeCombobox($this->rpcServer);
        $rpcParams['selected'] = $configParams['sublease_type_selected'] ?? false;
        $rpcParams['record_all'] = $configParams['sublease_type_record_all'] ?? true;

        return $combobox->getSubleaseTypeCombobox($rpcParams);
    }

    private function getTopicLayerStylerCombobox($configParams = [])
    {
        $combobox = new TopicLayerStylerCombobox($this->rpcServer);
        $rpcParams['selected'] = $configParams['topic_layer_styler_selected'] ?? false;
        $rpcParams['record_all'] = $configParams['topic_layer_styler_record_all'] ?? true;

        return $combobox->getTopicLayerStylerCombobox($rpcParams);
    }

    private function getTransactionTypesCombobox($configParams = [])
    {
        $combobox = new TransactionTypesCombobox($this->rpcServer);
        $rpcParams['selected'] = $configParams['transaction_types_selected'] ?? false;
        $rpcParams['record_all'] = $configParams['transaction_types_record_all'] ?? true;

        return $combobox->getTransactionTypesCombobox($rpcParams);
    }

    private function getFarmingYearCombobox($configParams = [])
    {
        $combobox = new FarmingYearCombobox($this->rpcServer);
        $rpcParams['selected'] = $configParams['selected'] ?? false;
        $rpcParams['record_all'] = $configParams['farming_year_record_all'] ?? true;

        return $combobox->getFarmingYear($rpcParams);
    }

    private function getFarmingYearWithCalendarYearSelectedCombobox($configParams = [])
    {
        $combobox = new FarmingYearCombobox($this->rpcServer);
        $rpcParams['selected'] = $configParams['selected'] ?? false;
        $rpcParams['record_all'] = $configParams['farming_year_record_all'] ?? true;
        $rpcParams['calendar_year_selected'] = true;

        return $combobox->getFarmingYear($rpcParams);
    }

    private function getVPSZimniGaskiEkateCombobox($configParams = [])
    {
        $combobox = new VPSZimniGaskiEkateCombobox($this->rpcServer);
        $rpcParams['record_all'] = $configParams['vps_zimni_gaski_ekate_record_all'] ?? true;

        return $combobox->getVPSZimniGaskiEkateCombobox($rpcParams);
    }

    private function ********************************($configParams = [])
    {
        $combobox = new VPSOrliLeshoyadiEkateCombobox($this->rpcServer);
        $rpcParams['record_all'] = $configParams['vps_orli_leshoyadi_ekate_record_all'] ?? true;

        return $combobox->********************************($rpcParams);
    }

    private function getVPSLivadenBlatarNameCombobox($configParams = [])
    {
        $combobox = new VPSLivadenBlatarNameCombobox($this->rpcServer);
        $rpcParams['record_all'] = $configParams['vps_livaden_blatar_name_record_all'] ?? true;

        return $combobox->getVPSLivadenBlatarNameCombobox($rpcParams);
    }

    private function getVPSChervenogushiGaskiEkateCombobox($configParams = [])
    {
        $combobox = new VPSChervenogushiGaskiEkateCombobox($this->rpcServer);
        $rpcParams['record_all'] = $configParams['vps_chervenogushi_gaski_ekate_record_all'] ?? true;

        return $combobox->getVPSChervenogushiGaskiEkateCombobox($rpcParams);
    }

    private function getAllowableEkateCombobox($configParams = [])
    {
        $combobox = new AllowableEkateCombobox($this->rpcServer);
        $rpcParams['record_all'] = $configParams['allowable_ekate_record_all'] ?? true;

        return $combobox->getAllowableEkateCombobox($rpcParams);
    }

    private function getPZPEkateCombobox($configParams = [])
    {
        $combobox = new PZPEkateCombobox($this->rpcServer);
        $rpcParams['record_all'] = $configParams['pzp_ekate_record_all'] ?? true;

        return $combobox->getPZPEkateCombobox($rpcParams);
    }

    private function getLFAEkateCombobox($configParams = [])
    {
        $combobox = new LFAEkateCombobox($this->rpcServer);
        $rpcParams['record_all'] = $configParams['lfa_ekate_record_all'] ?? true;

        return $combobox->getLFAEkateCombobox($rpcParams);
    }

    private function getNatura2000NameCombobox($configParams = [])
    {
        $combobox = new Natura2000NameCombobox($this->rpcServer);
        $rpcParams['record_all'] = $configParams['natura_2000_name_record_all'] ?? true;

        return $combobox->getNatura2000NameCombobox($rpcParams);
    }

    private function getMapTypesCombobox($configParams = [])
    {
        $rpcParams = [];
        $combobox = new MapTypesCombobox($this->rpcServer);

        return $combobox->getMapTypesCombobox($rpcParams);
    }

    private function getModemsCombobox($configParams = [])
    {
        $combobox = new ModemsCombobox($this->rpcServer);
        $rpcParams['selected'] = $configParams['modems_selected'] ?? true;

        return $combobox->getModemsCombobox($rpcParams);
    }

    private function getOwnerTypesCombobox($configParams = [])
    {
        $combobox = new OwnerTypesCombobox($this->rpcServer);

        return $combobox->getOwnerTypes($configParams);
    }

    private function ********************************()
    {
        $combobox = new ConsolidationZdDogKodCombobox($this->rpcServer);

        return $combobox->********************************();
    }

    private function ********************************()
    {
        $combobox = new ConsolidationDoDogKodCombobox($this->rpcServer);

        return $combobox->********************************();
    }

    private function getConsolidationZdJelanieCombobox()
    {
        $combobox = new ConsolidationZdJelanieCombobox($this->rpcServer);

        return $combobox->getConsolidationZdJelanieCombobox();
    }

    private function getConsolidationZdKodCombobox()
    {
        $combobox = new ConsolidationZdKodCombobox($this->rpcServer);

        return $combobox->getConsolidationZdKodCombobox();
    }

    private function getCountriesCombobox()
    {
        $combobox = new CountriesCombobox($this->rpcServer);

        return $combobox->getCountriesCombobox();
    }

    private function getContractGroupsCombobox()
    {
        $combobox = new ContractGroupsCombobox($this->rpcServer);

        return $combobox->getContractGroupsCombobox();
    }
}
