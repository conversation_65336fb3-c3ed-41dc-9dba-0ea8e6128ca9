<?php

namespace TF\Engine\APIClasses\Collections;

use Prado\Web\Services\TRpcServer;
use TF\Engine\APIClasses\Exports\PaymentExports;
use TF\Engine\Kernel\PrintPdf;
use TF\Engine\Plugins\Core\Farming\FarmingController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\UserDbCollections\UserDbCollectionsController;

/**
 * Class ExportCollection.
 *
 * @rpc-module Collections
 *
 * @rpc-service-id export-collection
 */
class ExportCollection extends PaymentExports
{
    private $UserDbController;
    private $FarmingController;

    public function __construct(TRpcServer $rpcServer)
    {
        parent::__construct($rpcServer);

        $this->FarmingController = new FarmingController('Farming');
        $this->UserDbController = new UserDbController($this->User->Database);
    }

    /**
     * Register the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'exportToPdfBankCollectionOrder' => ['method' => [$this, 'exportToPdfBankCollectionOrder']],
            'exportToPdfCollectionOrder' => ['method' => [$this, 'exportToPdfCollectionOrder']],
        ];
    }

    public function exportToPdfCollectionOrder($collectionId, $collectionDate = null, $paymentSubject = null, $paymentSubjectText = null, $withoutRkoNumbering = false, $returnAsText = false)
    {
        $CollectionController = new UserDbCollectionsController($this->User->database);
        $finaltext = $CollectionController->exportCollectionHtml($collectionId, $collectionDate = null, $paymentSubject = null, $paymentSubjectText = null, $withoutRkoNumbering = false);

        if ($returnAsText) {
            return $finaltext;
        }

        $date = date('Y-m-d-H-i-s');
        $name = 'platejno_' . $collectionId . '_' . $date . '.pdf';

        $userExportPath = PUBLIC_UPLOAD_EXPORT . DIRECTORY_SEPARATOR . $this->User->UserID;

        if (!is_dir($userExportPath)) {
            mkdir($userExportPath);
        }

        $pdfExpPath = $userExportPath . DIRECTORY_SEPARATOR . $name;
        $pdfDlPath = PUBLIC_UPLOAD_RELATIVE_PATH . DIRECTORY_SEPARATOR . 'export' . DIRECTORY_SEPARATOR . $this->User->UserID . DIRECTORY_SEPARATOR . $name;

        $printPdf = new PrintPdf();
        $printPdf->generateFromHtml($finaltext, $pdfExpPath, ['orientation' => 'Landscape'], true);

        return ['file_path' => $pdfDlPath, 'file_name' => $name];
    }

    /**
     * @param bool $collectionDate
     * @param string $bank
     * @param null $paymentSubject
     * @param null $paymentSubjectText
     * @param false $returnAsText
     *
     * @return array
     */
    public function exportToPdfBankCollectionOrder($collectionId, $collectionDate = true, $bank = '', $paymentSubject = null, $paymentSubjectText = null, $returnAsText = false)
    {
        if (!$collectionId) {
            return [];
        }

        $results = $this->getCollectionToExport($collectionId);
        if (0 == count($results)) {
            return [];
        }

        $result = array_shift($results);
        $paymentData = json_decode($result['payment_data']);

        if ($paymentData->owner_names) {
            $orderer = $paymentData->owner_names;
        } else {
            $orderer = $result['recieved_from'];
        }

        $amount = number_format($result['amount'], 2, '.', '');

        if ($paymentSubject > 0) {
            $template = $paymentSubject ? $this->getPaymentSubject($paymentSubject) : null;
            $farmYear = $GLOBALS['Farming']['years'][$result['farming_year']]['farming_year'];
            $template = str_replace('[[nomer_na_dogovor]]', $result['c_num'], $template);
            $template = str_replace('[[stopanska_godina]]', $farmYear, $template);
        } else {
            $template = null;
            if (null != $paymentSubjectText) {
                $template = $paymentSubjectText;
            }
        }
        $template = html_entity_decode($template);
        $details = mb_substr($template, 0, 35);
        if (strlen($template) > 35) {
            $additionalDetails = mb_substr($template, 35, 70);
        }

        $printData['recipient'] = $this->FarmingController->StringHelper->strToBlankFormat($paymentData->recipient_company ? $paymentData->recipient_company : $paymentData->recipient, 36);
        $printData['bank_acc'] = $this->FarmingController->StringHelper->strToBlankFormat(str_replace(' ', '', $paymentData->subleasing_farm_iban->iban ?? ' '), 22);
        $printData['bic'] = $this->FarmingController->StringHelper->strToBlankFormat(substr($paymentData->subleasing_farm_iban->bic ?? ' ', 0, 8), 8);
        $printData['bank'] = $this->FarmingController->StringHelper->strToBlankFormat(substr($paymentData->subleasing_farm_iban->name ?? ' ', 0, 32), 32);
        $printData['amount'] = $this->FarmingController->StringHelper->strToBlankFormat(number_format($amount, 2, '.', ''), 13, 'right');
        $printData['amount'] = str_replace('|&nbsp;.&nbsp;|', '<span style="font-size: 20px;vertical-align:text-bottom;">|</span>', $amount);
        $printData['details'] = $this->FarmingController->StringHelper->strToBlankFormat($details, 36);
        $printData['additionalDetails'] = $additionalDetails ? $this->FarmingController->StringHelper->strToBlankFormat($additionalDetails, 36) : ('&emsp;' . str_repeat('|&emsp;', 36));
        $printData['orderer'] = $this->FarmingController->StringHelper->strToBlankFormat($orderer, 36);
        $printData['orderer_bank_acc'] = $this->FarmingController->StringHelper->strToBlankFormat(substr($paymentData->orderer_iban->iban ?? ' ', 0, 22), 22);
        $printData['orderer_bic'] = $this->FarmingController->StringHelper->strToBlankFormat(substr($paymentData->orderer_iban->bic ?? ' ', 0, 8), 8);
        $printData['payment_system'] = '&emsp;' . str_repeat('|&emsp;', 16);
        $printData['charges'] = '&emsp;' . str_repeat('|&emsp;', 2);

        if ($collectionDate) {
            if (is_string($collectionDate)) {
                $newDate = explode('-', strftime('%d-%m-%y', strtotime($collectionDate)));
                $newDate = implode('', $newDate);
                $printData['date'] = $this->FarmingController->StringHelper->strToBlankFormat($newDate, 6) . 'г.';
            } else {
                $newDate = explode('-', strftime('%d-%m-%y', strtotime($result['date'])));
                $newDate = implode('', $newDate);
                $printData['date'] = $this->FarmingController->StringHelper->strToBlankFormat($newDate, 6) . 'г.';
            }
        } else {
            $printData['date'] = '&emsp;' . str_repeat('|&emsp;', 5);
        }

        $pages[] = $this->FarmingController->StringHelper->loadTemplate($GLOBALS['Templates'][15]['template'], $printData);

        $finaltext = '';
        foreach ($pages as $page) {
            $finaltext .= '<page style="font-family: freeserif"><br />' . $page . '</page>';
        }

        $date = date('Y-m-d-H-i-s');
        $name = 'platejno_iban_' . $date . '.pdf';

        $userExportPath = PUBLIC_UPLOAD_EXPORT . DIRECTORY_SEPARATOR . $this->User->UserID;
        if (!is_dir($userExportPath)) {
            mkdir($userExportPath);
        }
        $pdfExpPath = $userExportPath . DIRECTORY_SEPARATOR . $name;
        $pdfDlPath = PUBLIC_UPLOAD_RELATIVE_PATH . DIRECTORY_SEPARATOR . 'export' . DIRECTORY_SEPARATOR . $this->User->UserID . DIRECTORY_SEPARATOR . $name;
        if ($returnAsText) {
            return $finaltext;
        }

        $printPdf = new PrintPdf();
        $printPdf->generateFromHtml($finaltext, $pdfExpPath, [], true);

        return ['file_path' => $pdfDlPath, 'file_name' => $name];
    }

    /**
     * @return array|mixed|string|string[]
     */
    private function getCollectionToExport($collectionId)
    {
        $options = [
            'tablename' => 'su_collections',
            'return' => ["
                id,
                contract_id,
                round(cast(amount as numeric), 2) as amount,
                to_char(date, 'dd-mm-YYYY') as date,                
                recieved_from,
                user_name,
                bank_payment,
                payment_order,
                farming_year,
                payment_data
            "],
            'where' => [
                'contract_id' => ['column' => 'id', 'compare' => '=', 'value' => $collectionId],
            ],
        ];

        return $this->UserDbController->getItemsByParams($options, false, false);
    }
}
