<?php

namespace TF\Engine\APIClasses\Contracts;

use Exception;
use Prado\Web\Services\TRpcApiProvider;
use TF\Application\Common\Config;
use TF\Engine\Kernel\MTRpcException;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\UserDbContracts\UserDbContractsController;
use TF\Engine\Plugins\Core\UserDbPayments\UserDbPaymentsController;
use TF\Engine\Plugins\Core\Users\UsersController;

/**
 * Редакция на имот към договор
 *
 * @rpc-module Contracts
 *
 * @rpc-service-id contracts-edit-plot-areas
 */
class EditPlotAreas extends TRpcApiProvider
{
    private $module = 'Contracts';
    private $service_id = 'contracts-edit-plot-areas';

    /**
     * Registers the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'markForEditPlotAreas' => ['method' => [$this, 'markForEditPlotAreas']],
            'saveEditPlotAreas' => ['method' => [$this, 'saveEditPlotAreas'],
                'validators' => [
                    'param' => [
                        'contract_id' => 'validateInteger',
                        'plot_id' => 'validateInteger',
                        'ntp' => 'validateInteger',
                        'mestnost' => 'validateText',
                        'contract_area' => 'validateNumber',
                        'document_area' => 'validateNumber',
                    ],
                ]],
        ];
    }

    /**
     * Returns plot data.
     *
     * @api-method markForEditPlotAreas
     *
     * @param int $contractId
     * @param int $plotId
     *
     * @throws MTRpcException
     *
     * @return array
     */
    public function markForEditPlotAreas($contractId, $plotId)
    {
        $UserDbContractsController = new UserDbContractsController($this->User->Database);
        $UserDbController = new UserDbController($this->User->Database);

        // get contractType
        $contractType = $UserDbContractsController->getContractType($contractId);

        // No Rights to operate with "Договори за собственост"
        if (Config::CONTRACT_TYPE_OWN == $contractType && !$this->User->HasContractsOwnWriteRights) {
            return $UserDbContractsController->getResponseDataContracts($contractType, $this->User->HasContractsOwnWriteRights);
        }

        $isFromSublease = $UserDbContractsController->isContractFromSublease($contractId);
        if ($isFromSublease) {
            throw new MTRpcException('CANNOT_MODIFY_FROM_SUBLEASE_CONTRACT', -33752);
        }

        $options = [
            'return' => [
                'c.start_date', 'c.farming_id',
                'MAX(CASE WHEN a.due_date IS NULL THEN c.due_date ELSE a.due_date END) as due_date',
            ],
            'where' => [
                'contract_id' => ['column' => 'id', 'compare' => '=', 'prefix' => 'c', 'value' => $contractId],
            ],
            'group' => 'c.id',
        ];

        $results = $UserDbContractsController->getContractDataByPCRel($options, false, false);

        if (!$results[0]['due_date']) {
            $results[0]['due_date'] = '9999-12-31 00:00:00';
        }
        $options = [
            'return' => [
                'plot_id', 'array_agg(pc.contract_area) as contract_area_array', 'array_agg(c.id) as contract_id_array',
            ],
            'where' => [
                'plot_id' => ['column' => 'plot_id', 'compare' => '=', 'prefix' => 'pc', 'value' => $plotId],
                'contract_id' => ['column' => 'id', 'compare' => '<>', 'prefix' => 'c', 'value' => $contractId],
                'farming_id' => ['column' => 'farming_id', 'compare' => '=', 'prefix' => 'c', 'value' => $results[0]['farming_id']],
                'active' => ['column' => 'active', 'compare' => '=', 'prefix' => 'c', 'value' => 'TRUE'],
                'is_annex' => ['column' => 'is_annex', 'compare' => '=', 'prefix' => 'c', 'value' => 'FALSE'],
                'is_sublease' => ['column' => 'is_sublease', 'compare' => '=', 'prefix' => 'c', 'value' => 'FALSE'],
                'start_date' => ['column' => 'start_date', 'compare' => '<=', 'prefix' => 'c', 'value' => $results[0]['due_date']],
                'due_date' => ['column' => "CASE WHEN c.due_date IS NULL OR (CASE WHEN a.due_date IS NULL THEN c.due_date ELSE a.due_date END) >= '{$results[0]['start_date']}' THEN true ELSE false END", 'compare' => '=', 'value' => 'TRUE'],
            ],
            'group' => 'plot_id',
        ];

        $results = $UserDbContractsController->getContractDataByPCRel($options, false, false);

        $contract_id_array = explode(',', trim($results[0]['contract_id_array'], '{}'));
        $contract_area_array = explode(',', trim($results[0]['contract_area_array'], '{}'));
        $tempArray = [];
        $contractsCount = count($contract_id_array);
        for ($i = 0; $i < $contractsCount; $i++) {
            $tempArray[$contract_id_array[$i]] = $contract_area_array[$i];
        }
        $total_contract_area = 0;
        foreach ($tempArray as $value) {
            $total_contract_area += $value;
        }

        // load data from tableKVS
        $options = [
            'tablename' => $UserDbController->DbHandler->tableKVS,
            'return' => [
                'ekate', 'category', 'area_type', 'mestnost',
            ],
        ];
        $options['where'] = ['id' => ['column' => 'gid', 'compare' => '=', 'value' => $plotId]];

        $results = $UserDbController->getItemsByParams($options, false, false);
        $result = $results[0];

        return [
            'total_contract_area' => $total_contract_area,
            'ekate' => $result['ekate'],
            'category' => $result['category'],
            'area_type' => $result['area_type'],
            'mestnost' => $result['mestnost'],
        ];
    }

    /**
     * Saves the plot data.
     *
     * @api-method saveEditPlotAreas
     *
     * @param array $param {
     *                     #item integer contract_id
     *                     #item integer c_type
     *                     #item integer plot_id
     *                     #item integer category
     *                     #item integer ntp
     *                     #item string mestnost
     *                     #item float contract_area
     *                     #item float document_area
     *                     #item float price_per_acre
     *                     #item float rent_per_plot
     *                     #item float kvs_allowable_area
     *                     }
     *
     * @throws MTRpcException
     */
    public function saveEditPlotAreas($param)
    {
        $UserDbController = new UserDbController($this->User->Database);
        $UsersController = new UsersController('Users');
        $UserDbContractsController = new UserDbContractsController($this->User->Database);
        $UserDbPaymentsController = new UserDbPaymentsController($this->User->Database);

        $isFromSublease = $UserDbContractsController->isContractFromSublease($param['contract_id']);
        if ($isFromSublease) {
            throw new MTRpcException('CANNOT_MODIFY_FROM_SUBLEASE_CONTRACT', -33752);
        }

        if (is_numeric($param['rent_per_plot']) && $param['rent_per_plot'] >= 0 && !empty($param['plot_rents'])) {
            throw new MTRpcException('CANNOT_HAVE_BOTH_INDIVIDUAL_AND_SPECIFIC_RENT_FOR_SAME_PLOT', -34071);
        }

        $options = [
            'tablename' => $UserDbController->DbHandler->contractsPlotsRelTable,
            'mainData' => [
                'contract_area' => $param['contract_area'],
                'comment' => $param['comment'],
                'kvs_allowable_area' => $param['kvs_allowable_area'],
            ],
            'where' => [
                'contract_id' => $param['contract_id'],
                'plot_id' => $param['plot_id'],
            ],
        ];
        if (Config::CONTRACT_TYPE_OWN == $param['c_type'] && isset($param['price_per_acre'])) {
            if (null == $param['price_per_acre']) {
                $param['price_per_acre'] = 0;
            }
            $options['mainData']['price_per_acre'] = $param['price_per_acre'];
        }

        if (
            (Config::CONTRACT_TYPE_LEASE == $param['c_type'] || Config::CONTRACT_TYPE_RENT == $param['c_type'] || Config::CONTRACT_TYPE_JOINT_PROCESSING == $param['c_type'])
            && isset($param['area_for_rent'])
        ) {
            if (null == $param['area_for_rent']) {
                $param['area_for_rent'] = 0;
            }
            $options['mainData']['area_for_rent'] = $param['area_for_rent'];

            if ('' === $param['rent_per_plot']) {
                $param['rent_per_plot'] = null;
            }
            $options['mainData']['rent_per_plot'] = $param['rent_per_plot'];
        }

        // validate that the sum of rent type areas is equal to area_for_rent
        if ($param['plot_rents']) {
            $allowedRentTypeArea = $param['area_for_rent'];
            $rentsTypeArea = 0;
            foreach ($param['plot_rents'] as $key => $value) {
                $rentsTypeArea += $value['area'];
            }

            $rentsTypeArea = round($rentsTypeArea, 3);
            $allowedRentTypeArea = round($allowedRentTypeArea, 3);

            if (0 != $rentsTypeArea && ($allowedRentTypeArea - $rentsTypeArea) < 0) {
                throw new MTRpcException('BIGGER_RENT_TYPE_AREAS_THAN_AREA_FOR_RENT', -34068);
            }

            if (0 != $rentsTypeArea && ($allowedRentTypeArea - $rentsTypeArea) > 0) {
                throw new MTRpcException('SMALLER_RENT_TYPE_AREAS_THAN_AREA_FOR_RENT', -34069);
            }

            if ($rentsTypeArea > 0 && $UserDbContractsController->hasContractChargedRent($param['contract_id'], $param['plot_id'])) {
                throw new MTRpcException('CANNOT_EDIT_CONTRACT_WITH_CHARGED_RENT', -33760);
            }
        }

        $oldOptions = [
            'tablename' => "{$UserDbController->DbHandler->contractsPlotsRelTable} as cpr",
            'return' => [
                'cpr.*',
                'c.parent_id',
                'kvs.document_area',
                'c.start_date',
                'c.due_date',
                '
                (SELECT 
                    id 
                FROM 
                    su_contracts as a
                WHERE 
                    a.parent_id = c.id -- annex action check is needed (if we have overlapping periods just throw exception to work with annexes)
                    AND 
                    contract_id = ' . $options['where']['contract_id'] . '
                    AND (
                        (a.start_date <= c.due_date AND a.due_date >= c.start_date)
                    )
                ) as period_overlap_with_annex_id',
                "json_agg(
                    json_build_object(
                        'id', spr.id,
                        'rent_type_id', spr.rent_type_id,
                        'area', spr.area 
                    ) 
                ) as plot_rents",
            ],
            'where' => [
                'contract_id' => ['column' => 'contract_id', 'compare' => '=', 'value' => $options['where']['contract_id']],
                'plot_id' => ['column' => 'plot_id', 'compare' => '=', 'value' => $options['where']['plot_id']],
            ],
            'joins' => [
                'inner join su_contracts c on c.id = cpr.contract_id',
                'left join su_plots_rents spr on spr.pc_rel_id = cpr.id',
                'left join layer_kvs kvs on kvs.gid = cpr.plot_id',
            ],
            'group' => 'cpr.id, c.id, kvs.document_area',
        ];
        $oldValues = $UserDbController->getItemsByParams($oldOptions, false, false);

        $additionalParams = [];
        if (!empty($oldValues[0]['parent_id'])) {
            $additionalParams = [
                'parent_id' => $oldValues[0]['parent_id'],
                'start_date' => $oldValues[0]['start_date'],
                'due_date' => $oldValues[0]['due_date'],
            ];
        }

        // Check if plot rents areas have changed
        $hasPlotRentsChanges = false;
        $oldPlotRents = json_decode($oldValues[0]['plot_rents'], true) ?? [];
        $newPlotRents = $param['plot_rents'] ?? [];

        // Remove null entries from old plot rents
        $oldPlotRents = array_filter($oldPlotRents, function ($rent) {
            return !is_null($rent['id']);
        });

        if (count($oldPlotRents) != count($newPlotRents)) {
            $hasPlotRentsChanges = true;
        } else {
            // Compare each plot rent for area changes
            foreach ($oldPlotRents as $oldRent) {
                $found = false;
                foreach ($newPlotRents as $newRent) {
                    if ($oldRent['id'] == $newRent['plot_rent_id']) {
                        $found = true;
                        if (round($oldRent['area'], 3) != round($newRent['area'], 3)) {
                            $hasPlotRentsChanges = true;

                            break 2;
                        }

                        break;
                    }
                }
                if (!$found) {
                    $hasPlotRentsChanges = true;

                    break;
                }
            }

            // Check for new plot rents (plot_rent_id is empty)
            if (!$hasPlotRentsChanges) {
                foreach ($newPlotRents as $newRent) {
                    if (empty($newRent['plot_rent_id'])) {
                        $hasPlotRentsChanges = true;

                        break;
                    }
                }
            }
        }

        if (
            round($oldValues[0]['contract_area'], 3) != round($param['contract_area'], 3)
            || round($oldValues[0]['rent_per_plot'], 2) != round($param['rent_per_plot'], 2)
            || round($oldValues[0]['document_area'], 3) != round($param['document_area'], 3)
            || round($oldValues[0]['kvs_allowable_area'], 3) != round($param['kvs_allowable_area'], 3)
            || round($oldValues[0]['area_for_rent'], 3) != round($param['area_for_rent'], 3)
            || $oldValues[0]['parent_id']
            || $hasPlotRentsChanges
        ) {
            // Check if contract has charged rent before allowing plot area edits
            if ($UserDbContractsController->hasContractChargedRent($param['contract_id'], $param['plot_id'])) {
                throw new MTRpcException('CANNOT_EDIT_CONTRACT_WITH_CHARGED_RENT', -33760);
            }

            $UserDbPaymentsController->hasPaymentRestriction($param['contract_id'], $additionalParams);
        }

        if (count($oldValues) && in_array($param['c_type'], [
            Config::CONTRACT_TYPE_LEASE,
            Config::CONTRACT_TYPE_RENT,
            Config::CONTRACT_TYPE_JOINT_PROCESSING,
        ])) {
            $newContractArea = round(floatval($param['contract_area']), 3);
            $newAreaForRent = round(floatval($param['area_for_rent']), 3);
            if ($oldValues[0]['period_overlap_with_annex_id']
                && ($oldValues[0]['contract_area'] != $newContractArea || $oldValues[0]['area_for_rent'] != $newAreaForRent)
            ) {
                throw new MTRpcException('CONTRACT_PERIOD_OVERLAP_WITH_ANNEX', -34060);
            }
            if ($oldValues[0]['contract_area'] != $newContractArea && $newContractArea > $oldValues[0]['contract_area']) {
                $plotsWithActiveContracts = $UserDbContractsController->getNotAvailableContractsPlots(
                    $oldValues[0]['start_date'],
                    $oldValues[0]['due_date'],
                    [$param['contract_id']],
                    [],
                    [$param['plot_id']],
                    isset($oldValues[0]['parent_id']) && 0 != $oldValues[0]['parent_id'] ? [$oldValues[0]['parent_id']] : null,
                    null
                );

                if (count($plotsWithActiveContracts) > 0 && $plotsWithActiveContracts[0]['available_plot_area'] < $newContractArea) {
                    throw new MTRpcException(UserDbContractsController::buildDuplicatePlotErrorMsg($plotsWithActiveContracts), UserDbContractsController::CONTRACT_PLOT_DUPLICATION_ERROR_CODE);
                }
            }
        }

        if (count($oldValues) && in_array($param['c_type'], [
            Config::CONTRACT_TYPE_OWN,
        ])) {
            $newContractArea = round(floatval($param['contract_area']), 3);

            if ($oldValues[0]['contract_area'] != $newContractArea && $newContractArea < $oldValues[0]['contract_area']) {
                $soldContractPlots = $UserDbContractsController->getSoldContractsPlots(
                    $oldValues[0]['start_date'],
                    $param['contract_id'],
                    $param['plot_id']
                );

                if (count($soldContractPlots) > 0 && $soldContractPlots[0]['total_contract_area_for_sale'] > $newContractArea) {
                    throw new MTRpcException($soldContractPlots, UserDbContractsController::CONTRACT_SOLD_PLOT_ERROR_CODE);
                }
            }
        }

        $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, $param, $oldValues, 'Edited contract plot');

        $transaction = $UserDbController->DbHandler->DbModule->beginTransaction();

        try {
            $UserDbController->editItem($options);

            if ('-1' == $param['category'] || '' == $param['category']) {
                $param['category'] = null;
            }

            if ('-1' == $param['ntp'] || '' == $param['ntp']) {
                $param['ntp'] = null;
            }

            if (isset($param['plot_rents'])) {
                $deletedPlotRents = array_diff(array_column($oldPlotRents, 'id'), array_column($newPlotRents, 'plot_rent_id'));
                if (!empty($deletedPlotRents)) {
                    $UserDbController->deleteItemsByParams([
                        'tablename' => 'su_plots_rents',
                        'id_name' => 'id',
                        'id_string' => implode(',', $deletedPlotRents),
                    ]);
                }
                foreach ($param['plot_rents'] as $key => $plotRent) {
                    if (empty($plotRent['plot_rent_id'])) {
                        // add plot rents data
                        $UserDbController->addItem([
                            'tablename' => 'su_plots_rents',
                            'mainData' => [
                                'pc_rel_id' => $plotRent['pc_rel_id'],
                                'rent_type_id' => $plotRent['rent_type_id'],
                                'area' => $plotRent['area'],
                            ],
                        ]);
                    } else {
                        // edit plot rents data
                        $UserDbController->editItem([
                            'tablename' => 'su_plots_rents',
                            'mainData' => [
                                'area' => $plotRent['area'],
                            ],
                            'where' => ['id' => $plotRent['plot_rent_id']],
                        ]);
                    }
                }
            }

            $options = [
                'tablename' => $UserDbController->DbHandler->tableKVS,
                'mainData' => [
                    'document_area' => $param['document_area'],
                    'category' => $param['category'],
                    'area_type' => '-1' == $param['ntp'] ? null : $param['ntp'],
                    'mestnost' => $param['mestnost'],
                ],
                'where' => [
                    'gid' => $param['plot_id'],
                ],
            ];

            $oldOptions = [
                'tablename' => $UserDbController->DbHandler->tableKVS,
                'return' => [
                    'gid, document_area, category, area_type, mestnost',
                ],
                'where' => [
                    'gid' => ['column' => 'gid', 'compare' => '=', 'value' => $options['where']['gid']],
                ],
            ];
            $oldValues = $UserDbController->getItemsByParams($oldOptions, false, false);

            $UserDbController->editItem($options);

            $transaction->commit();

            $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, $param, $oldValues, 'Edited plot info in kvs');
        } catch (Exception $e) {
            $transaction->rollBack();

            if (false !== strpos($e->getMessage(), 'restriction_area_for_rent_su_contracts_plots_rel()')) {
                throw new MTRpcException('Plot rents area is bigger than the new area for rent value', -34067);
            }

            throw $e;
        }
    }
}
