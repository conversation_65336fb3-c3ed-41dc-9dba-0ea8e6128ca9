<?php

namespace TF\Engine\APIClasses\Contracts;

use DateTime;
use Exception;
use Prado\Web\Services\TRpcApiProvider;
use TF\Application\Common\Config;
use TF\Engine\APIClasses\KVSContractsUpdate\KVSContractsUpdate;
use TF\Engine\Kernel\MTRpcException;
use TF\Engine\Plugins\Core\Contracts\SalesContractValidationTrait;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\UserDbContracts\UserDbContractsController;
use TF\Engine\Plugins\Core\UserDbPayments\UserDbPaymentsController;
use TF\Engine\Plugins\Core\UserDbSubleases\UserDbSubleasesController;
use TF\Engine\Plugins\Core\Users\UsersController;

/**
 * Грид "Имоти".
 *
 * @rpc-module Contracts
 *
 * @rpc-service-id contracts-plots-datagrid
 *
 * @property UserDbController $UserDbController
 * @property UserDbContractsController $UserDbContractsController
 * @property UsersController $UsersController
 */
class ContractsPlotsGrid extends TRpcApiProvider
{
    use KVSContractsUpdate;
    use SalesContractValidationTrait;
    public const SORT_EKATTE_NAME_COLUMN = 'ekatte_name';
    public $UserDbController;
    public $UserDbContractsController;
    public $UsersController = false;

    protected $plotGridDefaultReturn = [
        'rows' => [],
        'total' => 0,
        'footer' => [
            [
                'kad_ident' => '<b>ОБЩО</b>',
                'used_area' => '',
                'area' => '',
            ],
        ],
    ];

    private $module = 'Contracts';
    private $service_id = 'contracts-plots-datagrid';

    /**
     * Registers the available methods.
     *
     * @return array the list of implemented methods
     */
    public function registerMethods()
    {
        return [
            'read' => ['method' => [$this, 'getContractsPlotData'],
                'validators' => [
                    'filterObj' => [
                        'contract_id' => 'validateInteger',
                    ],
                    'page' => 'validateInteger',
                    'rows' => 'validateInteger',
                    'sort' => 'validateSort',
                    'order' => 'validateOrder',
                ],
            ],
            'addContractPlotRelation' => ['method' => [$this, 'addContractPlotRelation'],
                'validators' => [
                    'contract_id' => 'validateNumber, validateRequired',
                    'plot_data_array' => [
                        'validateComplexArray|plot_data_array' => [
                            'plot_id' => 'validateNumber, validateRequired',
                            'contract_area' => 'validateNumber',
                            'price_per_acre' => 'validateNumber',
                            'price_sum' => 'validateNumber',
                            'area_for_rent' => 'validateNumber',
                        ],
                    ],
                ]],
            'addConfirmedContractPlotRelation' => ['method' => [$this, 'addConfirmedContractPlotRelation']],
            'deleteContractPlotRelation' => ['method' => [$this, 'deleteContractPlotRelation']],
            'saveContractPrice' => ['method' => [$this, 'saveContractPrice'],
                'validators' => [
                    'contract' => 'validateRequired, validateInteger',
                    'price' => 'validateRequired, validateNumber',
                ],
            ],
            'getContractPrice' => ['method' => [$this, 'getContractPrice'],
                'validators' => [
                    'contract' => 'validateRequired, validateInteger',
                ],
            ],
            'getPlotsRents' => ['method' => [$this, 'getPlotsRents']],
            'getPlotsRentsCombobox' => ['method' => [$this, 'getPlotsRentsCombobox']],
            'clearPlotRentsTypes' => ['method' => [$this, 'clearPlotRentsTypes']],
        ];
    }

    /**
     * Gets all plots, associated with selected contract.
     *
     * @api-method read
     *
     * @param array $filterObj
     *                         {
     *                         #item string type
     *                         #item int    contract_id
     *                         #item string ekate
     *                         #item string masiv
     *                         #item string number
     *                         }
     * @param int $page pagination parameters
     * @param int $rows pagination parameters
     * @param string $sort pagination parameters
     * @param string $order pagination parameters
     *
     * @return array
     */
    public function getContractsPlotData(array $filterObj, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        if ($this->User->isGuest) {
            return [];
        }
        $this->UserDbController = new UserDbController($this->User->Database);
        $this->UserDbContractsController = new UserDbContractsController($this->User->Database);
        $this->UsersController = new UsersController('Users');

        // check if default grid is not loaded with contract_id = 0
        if (!$filterObj['contract_id'] || 0 == $filterObj['contract_id']) {
            return $this->plotGridDefaultReturn;
        }
        $sort = ('kad_ident' == $sort) ? 'kad_ident COLLATE "alpha_numeric_bg"' : $sort;

        $options = [
            'tablename' => $this->UserDbController->DbHandler->tableKVS,
            'offset' => ($page - 1) * $rows,
            'limit' => $rows,
            'sort' => $sort,
            'order' => $order,
            'return' => [
                'virtual_ntp_title as area_type', 'virtual_category_title as category', 'ekate', 'virtual_ekatte_name as ekatte_name', 'gid', 'include', 'kad_ident', 'masiv', 'mestnost',
                'number', 'participate', 'white_spots', 'used_area', 'St_Area(geom) as area', 'document_area',
                'CASE 
                    WHEN is_edited = true AND edit_active_from <= NOW() THEN TRUE
                    ELSE FALSE
                END AS is_edited',
                'edit_active_from',
                'allowable_area',
            ],
            'where' => [
                'ekate' => ['column' => 'ekate', 'compare' => '=', 'value' => $filterObj['ekate']],
                'masiv' => ['column' => 'masiv', 'compare' => '=', 'value' => $filterObj['masiv']],
                'number' => ['column' => 'number', 'compare' => '=', 'value' => $filterObj['number']],
            ],
        ];

        if (!empty($filterObj['kad_idents'])) {
            $options['where']['kad_idents'] = [
                'column' => 'kad_ident',
                'compare' => 'IN',
                'value' => $this->UsersController->ArrayHelper->filterEmptyStringArr($filterObj['kad_idents']),
            ];
        }

        if (!$filterObj['type'] || '' == $filterObj['type']) {
            return $this->plotGridDefaultReturn;
        }
        // get plot contract relations data
        $results = $this->UserDbContractsController->getContractsPlotsRelations($filterObj['contract_id']);
        $resultsCount = count($results);
        // clear old data
        $id_array = [];
        $contract_area_array = [];

        // get contract rent in money
        $contractDataOptions = [
            'where' => [
                'id' => ['column' => 'id', 'compare' => '=', 'value' => $filterObj['contract_id']],
            ],
        ];

        $contractDataResults = $this->UserDbContractsController->getContractsData($contractDataOptions, false, false);

        if (0 == $resultsCount) {
            $id_string = 0;
        } else {
            for ($i = 0; $i < $resultsCount; $i++) {
                $id_array[] = $results[$i]['plot_id'];
                $contract_area_array[$results[$i]['plot_id']] = $results[$i]['contract_area'];
                $contract_plot_data[$results[$i]['plot_id']]['price_per_acre'] = $results[$i]['price_per_acre'];
                $contract_plot_data[$results[$i]['plot_id']]['price_sum'] = $results[$i]['price_sum'];
                $contract_plot_data[$results[$i]['plot_id']]['rent_per_plot'] = $results[$i]['rent_per_plot'];

                $contract_area_for_rent_array[$results[$i]['plot_id']] = is_numeric($results[$i]['area_for_rent']) ? $results[$i]['area_for_rent'] : 0;
            }
            $id_string = implode(', ', $id_array);
        }
        // end of plot - contracts relations data

        $contract_options = [
            'return' => [
                'start_date',
            ],
            'where' => [
                'id' => ['column' => 'id', 'compare' => '=', 'value' => $filterObj['contract_id']],
            ],
        ];

        $contract_results = $this->UserDbContractsController->getContractsData($contract_options, false, false);
        $edited_plots_options = [
            'tablename' => $this->UserDbController->DbHandler->tableKVS,
            'return' => [
                'array_agg(kvs.gid) as gids',
            ],
            'where' => [
                'is_edited' => ['column' => 'is_edited', 'compare' => '=', 'prefix' => 'kvs', 'value' => 'TRUE'],
                'edit_active' => ['column' => 'edit_active_from', 'compare' => '<=', 'prefix' => 'kvs', 'value' => $contract_results[0]['start_date']],
                'ekate' => ['column' => 'ekate', 'compare' => '=', 'value' => $filterObj['ekate']],
                'masiv' => ['column' => 'masiv', 'compare' => '=', 'value' => $filterObj['masiv']],
                'number' => ['column' => 'number', 'compare' => '=', 'value' => $filterObj['number']],
            ],
        ];

        $invalid_plots = $this->UserDbContractsController->getEditedPlotsBeforeContractActiveDate($edited_plots_options, false);
        $invalid_plots = str_replace(['{', '}'], ['', ''], $invalid_plots[0]['gids']);

        $typeView = true;
        switch ($filterObj['type']) {
            case 'view': // used when displaying already added elements
                $typeView = true;
                $options['return'][] = 'contract_area';
                $options['return'][] = 'pc.comment';
                $options['return'][] = 'pc.kvs_allowable_area';
                $options['return'][] = 'pc.id as pc_rel_id';
                $options['return'][] = 'pc.annex_action';
                $options['return'][] = 'pc.virtual_non_arable_area';
                $options['return'][] = '(COUNT(pr.id) > 0) as has_plot_rents';

                $arrayHelper = $this->UsersController->ArrayHelper;

                $options['id_string'] = $id_string;
                $options['where'] = [
                    'contract_id' => ['column' => 'contract_id', 'compare' => '=', 'value' => $filterObj['contract_id']],
                    'kad_ident' => ['column' => 'kad_ident', 'compare' => '=', 'prefix' => 'kvs', 'value' => $filterObj['kad_ident']],
                    'ekate' => ['column' => 'ekate', 'compare' => '=', 'prefix' => 'kvs', 'value' => $filterObj['ekate']],
                    'masiv' => ['column' => 'masiv', 'compare' => '=', 'prefix' => 'kvs', 'value' => $filterObj['masiv']],
                    'number' => ['column' => 'number', 'compare' => '=', 'prefix' => 'kvs', 'value' => $filterObj['number']],
                ];
                $options['group'] = 'kvs.gid, virtual_ekatte_name, pc.id';
                $options['category'] = $arrayHelper->filterEmptyStringArr($filterObj['category']);
                $options['joinContractsPlotsRel'] = true;

                break;

            case 'add': // use when displaying non added elements
                $typeView = false;
                if ($invalid_plots) {
                    $id_string = $id_string . ', ' . $invalid_plots;
                }
                $options['anti_id_string'] = $id_string;

                break;
        }

        $counter = $this->UserDbContractsController->getPlotDataForContracts($options, true, false);
        if (0 === $counter[0]['count']) {
            return $this->plotGridDefaultReturn;
        }

        if ('view' === $filterObj['type']) {
            $options['return'][] = 'case when count(spor.owner_id) = 0 then \'\' else string_agg(spor.owner_id::text, \',\') end as owner_ids';
            $options['return'][] = 'case when count(spfr.farming_id) = 0 then \'\' else string_agg(spfr.farming_id::text, \',\') end as farming_ids';
            $options['joins'] = [
                'LEFT JOIN su_plots_owners_rel spor ON (spor.pc_rel_id = pc.id and spor.is_heritor = false)',
                'LEFT JOIN su_plots_farming_rel spfr ON (spfr.pc_rel_id = pc.id)',
                'LEFT JOIN su_plots_rents pr ON (pr.pc_rel_id = pc.id)',
            ];
        }

        $result = $this->UserDbContractsController->getPlotDataForContracts($options, false, false);

        $resultCount = count($result);

        $total_document_area = 0;
        $total_contract_area = 0;
        $total_area_for_rent = 0;
        $total_allowable_area = 0;
        $edited_plots = 0;
        $removed_plots = 0;
        $totalKvsAllowableArea = 0;

        $hasFilteredPlot = false;
        if ('view' == $filterObj['type']) {
            $filteredPlots = $this->applyContractFilter($filterObj);
        }

        $UserDbSubleasesController = new UserDbSubleasesController($this->User->Database);

        for ($i = 0; $i < $resultCount; $i++) {
            // kvs_allowable_area uses allowable_area for initial value TS-4623
            $kvs_allowable_area = null === $result[$i]['kvs_allowable_area'] ? $result[$i]['allowable_area'] : $result[$i]['kvs_allowable_area'];

            $result[$i]['used_area'] = number_format($result[$i]['used_area'], 3, '.', '');
            $result[$i]['allowable_area'] = number_format($result[$i]['allowable_area'], 3, '.', '');
            $result[$i]['kvs_allowable_area'] = number_format($kvs_allowable_area, 3, '.', '');
            $result[$i]['virtual_non_arable_area'] = number_format($result[$i]['virtual_non_arable_area'], 3, '.', '');
            $result[$i]['area'] = number_format($result[$i]['area'] / 1000, 3, '.', '');

            if (!isset($result[$i]['document_area'])) {
                $result[$i]['document_area'] = $result[$i]['area'];
            }

            if (isset($contract_area_array[$result[$i]['gid']])) {
                $result[$i]['contract_area'] = $contract_area_array[$result[$i]['gid']];
            } elseif ((float)$result[$i]['document_area'] > 0) {
                $result[$i]['contract_area'] = $result[$i]['document_area'];
            } else {
                $result[$i]['contract_area'] = $result[$i]['area'];
            }

            $result[$i]['area_for_rent'] = ($typeView) ? $contract_area_for_rent_array[$result[$i]['gid']] : $result[$i]['contract_area'];

            if (null === $contract_plot_data[$result[$i]['gid']]['rent_per_plot']) {
                if ('add' === $filterObj['type'] && $contractDataResults[0]['renta'] > 0) {
                    $result[$i]['rent_per_plot'] = number_format($contractDataResults[0]['renta'], 2, '.', '');
                } else {
                    $result[$i]['rent_per_plot'] = '-';
                }
            } else {
                $result[$i]['rent_per_plot'] = number_format($contract_plot_data[$result[$i]['gid']]['rent_per_plot'], 2, '.', '');
            }
            $result[$i]['rent_per_plot_txt'] = '-' == $result[$i]['rent_per_plot'] ? '-' : BGNtoEURO($result[$i]['rent_per_plot']);

            $result[$i]['price_per_acre'] = $contract_plot_data[$result[$i]['gid']]['price_per_acre'] ? $contract_plot_data[$result[$i]['gid']]['price_per_acre'] : '-';
            $result[$i]['price_sum'] = $contract_plot_data[$result[$i]['gid']]['price_per_acre'] ? $result[$i]['price_per_acre'] * $result[$i]['contract_area'] : '-';

            if ('-' != $result[$i]['price_per_acre']) {
                $result[$i]['price_per_acre'] = BGNtoEURO($result[$i]['price_per_acre']);
            }
            if ('-' != $result[$i]['price_sum']) {
                $result[$i]['price_sum'] = BGNtoEURO($result[$i]['price_sum']);
            }

            if ('' == $result[$i]['kad_ident']) {
                $result[$i]['kad_ident'] = '[Няма информация]';
            }

            if (true == $result[$i]['is_edited'] && (null !== $result[$i]['edit_active_from'])) {
                $edited_plots++;
            } else {
                if ('removed' != $result[$i]['annex_action']) {
                    $total_document_area += $result[$i]['document_area'];
                    $total_contract_area += $result[$i]['contract_area'];
                    $total_area_for_rent += $result[$i]['area_for_rent'];
                    $total_allowable_area += $result[$i]['allowable_area'];
                    $totalKvsAllowableArea += $result[$i]['kvs_allowable_area'];
                }
            }

            if ('removed' == $result[$i]['annex_action']) {
                $removed_plots++;
            }

            $result[$i]['contract_area'] = number_format($result[$i]['contract_area'], 3, '.', '');
            $result[$i]['document_area'] = number_format($result[$i]['document_area'], 3, '.', '');
            $result[$i]['area_for_rent'] = number_format($result[$i]['area_for_rent'], 3, '.', '');

            $options = [
                'return' => ['distinct(C .*)'],
                'where' => [
                    'plot_id' => ['column' => 'plot_id', 'compare' => '=', 'prefix' => 'pc', 'value' => $result[$i]['gid']],
                    'contract_id' => ['column' => 'contract_id', 'compare' => '=', 'prefix' => 'pc', 'value' => $filterObj['contract_id']],
                    'is_sublease' => ['column' => 'is_sublease', 'compare' => '=', 'prefix' => 'c', 'value' => 'TRUE'],
                ],
            ];

            $subleasedPlots = $UserDbSubleasesController->getSubleasePlotsData($options, false, false);
            $result[$i]['subleases'] = array_map(function ($value) {
                $value['is_expired'] = date_format(date_create($value['due_date']), 'Y-m-d') <= date('Y-m-d');

                return $value;
            }, $subleasedPlots);

            if (false == $hasFilteredPlot && 'view' == $filterObj['type'] && null != $filterObj['contracts_tree_filter']) {
                if (in_array($result[$i]['gid'], $filteredPlots)) {
                    $result[$i]['selected'] = true;
                    $hasFilteredPlot = true;
                }
            }
        }

        $return['rows'] = $result;
        $return['total'] = $counter[0]['count'];
        $return['footer'] = [
            [
                'kad_ident' => '<b>ОБЩО</b>',
                'contract_area' => number_format($total_contract_area, 3, '.', ''),
                'document_area' => number_format($total_document_area, 3, '.', ''),
                'area_for_rent' => number_format($total_area_for_rent, 3, '.', ''),
                'allowable_area' => number_format($total_allowable_area, 3, '.', ''),
                'kvs_allowable_area' => number_format($totalKvsAllowableArea, 3, '.', ''),
            ],
        ];
        if ('view' == $filterObj['type']) {
            $return['footer'][] = [
                'kad_ident' => '<b>Брой имоти</b>',
                'contract_area' => $counter[0]['count'] - $edited_plots - $removed_plots,
            ];
        }

        return $return;
    }

    /**
     * Creates a relation between selected contract ID and plot ID.
     *
     * @api-method addContractPlotRelation
     *
     * @param array $data - RPC Parameters
     *                    {
     *                    #item int plot_id
     *                    #item int contract_id
     *                    #item float contract_area
     *                    #item float price_per_acre
     *                    #item float price_sum
     *                    #item float document_area
     *                    #item float kvs_allowable_area
     *                    }
     *
     * @throws MTRpcException
     *
     * @return array
     *               if successful - creates the relation and returns status OK code
     *               if failed - returns MTRpcException and stores the plot relation data in the session
     */
    public function addContractPlotRelation($data)
    {
        $UserDbContractsController = new UserDbContractsController($this->User->Database);

        return $UserDbContractsController->addPlotToContractAnnex($data, $this->User, $this->module, $this->service_id, $excludeRemovedWithAnnex = true);
    }

    /**
     * When invoked creates the contract-to-plot relation from the data in the session.
     *
     * @api-method addConfirmedContractPlotRelation
     *
     * @throws MTRpcException
     *
     * @return array
     */
    public function addConfirmedContractPlotRelation()
    {
        $this->UsersController = new UsersController('Users');
        $this->UserDbController = new UserDbController($this->User->Database);
        $this->UserDbContractsController = new UserDbContractsController($this->User->Database);

        $data = $_SESSION['plots_waiting_confirmation'];

        $isFromSublease = $this->UserDbContractsController->isContractFromSublease($data['contract_id']);
        if ($isFromSublease) {
            throw new MTRpcException('CANNOT_MODIFY_FROM_SUBLEASE_CONTRACT', -33752);
        }

        $options = [
            'return' => [
                'c.farming_id',
                'c.nm_usage_rights',
                'c.start_date',
                'MAX(CASE WHEN a.due_date IS NULL THEN c.due_date WHEN a.due_date < c.due_date THEN c.due_date ELSE a.due_date END) as due_date',
            ],
            'where' => [
                'contract_id' => ['column' => 'id', 'compare' => '=', 'prefix' => 'c', 'value' => $data['contract_id']],
            ],
            'group' => 'c.id',
        ];

        $results = $this->UserDbContractsController->getContractDataByPCRel($options, false, false);

        if (!$results[0]['due_date']) {
            $results[0]['due_date'] = null;
        }

        $originalContractDueDate = $results[0]['due_date'];
        $originalContractType = $results[0]['nm_usage_rights'];
        $originalContractFarming = $results[0]['farming_id'];

        $addedRelations = [];
        $plotDataArrayCount = count($data['plot_data_array']);
        for ($i = 0; $i < $plotDataArrayCount; $i++) {
            $options = [
                'tablename' => $this->UserDbController->DbHandler->contractsPlotsRelTable,
                'mainData' => [
                    'contract_id' => $data['contract_id'],
                    'plot_id' => $data['plot_data_array'][$i]['plot_id'],
                    'contract_area' => $data['plot_data_array'][$i]['contract_area'],
                    'area_for_rent' => filter_var($data['plot_data_array'][$i]['area_for_rent'], FILTER_VALIDATE_FLOAT, FILTER_NULL_ON_FAILURE),
                    'price_per_acre' => filter_var($data['plot_data_array'][$i]['price_per_acre'], FILTER_VALIDATE_FLOAT, FILTER_NULL_ON_FAILURE),
                    'price_sum' => filter_var($data['plot_data_array'][$i]['price_sum'], FILTER_VALIDATE_FLOAT, FILTER_NULL_ON_FAILURE),
                    'contract_end_date' => $data['contract_end_date'],
                    'fraction' => $data['fraction'],
                    'percent' => $data['percent'],
                ],
            ];

            $new_rel_id = $this->UserDbController->addItem($options);

            if (1 == $originalContractType) {
                $this->UserDbController->addFarmingAsOwnerForPcRelId($new_rel_id, $originalContractFarming);
            }

            $addedRelations[] = $new_rel_id;
            $this->UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, $options['mainData'], ['contract_plot_rel_id' => $new_rel_id], 'adds contract-plot relation');

            $options = [
                'tablename' => $this->UserDbController->DbHandler->tableKVS,
                'mainData' => [
                    'document_area' => $data['plot_data_array'][$i]['document_area'],
                ],
                'where' => ['gid' => $data['plot_data_array'][$i]['plot_id']],
            ];

            $oldOptions = [
                'tablename' => $this->UserDbController->DbHandler->tableKVS,
                'return' => [
                    'document_area',
                ],
                'where' => [
                    'gid' => ['column' => 'gid', 'compare' => '=', 'value' => $data['plot_data_array'][$i]['plot_id']],
                ],
            ];
            $oldValues = $this->UserDbController->getItemsByParams($oldOptions, false, false);

            $this->UserDbController->editItem($options);

            $this->UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, $options, $oldValues, 'edit plot document area in layer_kvs');
        }
        $this->UserDbContractsController->manageOverallRenta($data['contract_id']);
        unset($_SESSION['plots_waiting_confirmation']);

        return $addedRelations;
    }

    /**
     * Deletes the relation between the contract and the plot.
     *
     * @api-method deleteContractPlotRelation
     *
     * @param int $plot_id - selected plot ID
     * @param array|int $checked - selected contract ID
     * @param bool $confirm
     *
     * @throws MTRpcException
     *
     * @return array
     */
    public function deleteContractPlotRelation($plot_id, $checked, $confirm = false)
    {
        if ($this->User->isGuest) {
            return [];
        }

        $this->UserDbContractsController = new UserDbContractsController($this->User->Database);
        $this->UserDbController = new UserDbController($this->User->Database);
        $this->UsersController = new UsersController('Users');
        $UserDbPaymentsController = new UserDbPaymentsController($this->User->Database);

        // getting parameters from callback
        $id_array = [];

        // getting all contract IDs
        if (is_array($checked)) {
            $checkedCount = count($checked);
            for ($i = 0; $i < $checkedCount; $i++) {
                $id_array[] = $checked[$i]['id'];
            }
        } else {
            $id_array[0] = $checked;
        }

        $isFromSublease = $this->UserDbContractsController->isContractFromSublease($checked);
        if ($isFromSublease) {
            throw new MTRpcException('CANNOT_MODIFY_FROM_SUBLEASE_CONTRACT', -33752);
        }

        $relations = $this->UserDbContractsController->getContractPlotRelationID($checked, $plot_id);

        $this->validateSalesContractRelations(
            [
                'pc_rel_id' => $relations,
            ],
            $this->UserDbContractsController
        );

        $UserDbPaymentsController->hasPaymentRestriction($checked);

        $subleasedPlots = $this->UserDbController->getSubleasedPlotsOfContract(['contract_id' => $checked, 'plot_id' => $plot_id]);
        $hasSubleasedPlots = count($subleasedPlots);

        $onlyActiveSubleasedPlots = array_filter($subleasedPlots, function ($item) {
            return date_format(date_create($item['sublease_contract_end_date']), 'Y-m-d') >= date('Y-m-d');
        });

        if (!$confirm && $hasSubleasedPlots && $plotParticipatesInAtLeastOneActiveSubleasedContract = count($onlyActiveSubleasedPlots) > 0) {
            $plotUniqueIdentifier = (implode(',', array_column($onlyActiveSubleasedPlots, 'res')));
            $subleaseId = (implode(',', array_column($onlyActiveSubleasedPlots, 'sublease_id')));
            $subleaseName = (implode(',', array_column($onlyActiveSubleasedPlots, 'sublease_c_num')));

            throw new MTRpcException("Имота {$plotUniqueIdentifier} не може да бъде изтрит, защото участва в договор(и) за преотдаване <a href='index.php?page=Subleases.Home&sublease_id={$subleaseId}' target='_blank'>№: {$subleaseName}</a>", -33215);
        }

        $plotData = array_pop($this->UserDbContractsController->getPlotDataForContracts([
            'tablename' => $this->UserDbController->DbHandler->tableKVS,
            'return' => [
                'gid', 'kad_ident', 'ekate', 'masiv', 'number', 'date_uploaded', 'file_id',
            ],
            'joins' => [
                "LEFT JOIN dblink('host=" . DEFAULT_DB_HOST . ' port=' . DEFAULT_DB_PORT . ' user=' . DEFAULT_DB_USERNAME . ' password=' . DEFAULT_DB_PASSWORD . ' dbname=' . DEFAULT_DB_DATABASE . "',
                'SELECT id, ekate, date_uploaded, group_id, shape_type FROM su_users_files GROUP BY id, ekate, group_id') AS files (file_id integer, ekatte_code text, date_uploaded timestamp, group_id integer,  shape_type integer) on kvs.ekate = files.ekatte_code and files.group_id = {$this->User->GroupID} AND files.shape_type = " . Config::LAYER_TYPE_KVS_OSZ,
            ],
            'where' => [
                'gid' => ['column' => 'gid', 'compare' => '=', 'value' => $plot_id],
            ],
            'sort' => 'file_id DESC',
            'limit' => 1,
            'offset' => 0,
        ], false, false));

        $transaction = $this->UserDbContractsController->startTransaction();

        try {
            /*
             * В случай, че имотът е преотдаден и е включен в автоматично създаден договор за
             * наем/аренда този имот да се изтрива и от новите автоматично създадени договори
             * */
            $this->UserDbContractsController->deleteCPRelForCPRel($relations);

            if ($hasSubleasedPlots) {
                // delete subleases plots contract rels
                $this->UserDbContractsController->deleteItemsByParams([
                    'tablename' => $this->UserDbContractsController->DbHandler->tableSubleasesPlotsContractsRel,
                    'id_name' => 'pc_rel_id',
                    'id_string' => implode(',', array_column($subleasedPlots, 'sub_rels')),
                ]);

                $this->UserDbContractsController->deleteItemsByParams([
                    'tablename' => $this->UserDbContractsController->DbHandler->tableSubleasesPlotsArea,
                    'id_name' => 'sublease_id',
                    'id_string' => implode(',', array_column($subleasedPlots, 'sublease_id')),
                    'where' => [
                        'plot_id' => ['column' => 'plot_id', 'compare' => 'IN', 'value' => array_column($subleasedPlots, 'plot_id')],
                    ],
                ]);
            }

            $options = [
                'tablename' => $this->UserDbContractsController->DbHandler->contractsPlotsRelTable,
                'id_name' => 'contract_id',
                'id_string' => implode(', ', $id_array),
                'where' => [
                    'plot_id' => ['column' => 'plot_id', 'compare' => '=', 'value' => $plot_id],
                ],
            ];

            $before_delete = [];
            $after_delete = [];
            $plot_result_before = $this->UserDbContractsController->getPlotIDsForContracts();

            $plotsResBeforeCount = count($plot_result_before);
            for ($i = 0; $i < $plotsResBeforeCount; $i++) {
                $before_delete[] = $plot_result_before[$i]['plot_id'];
            }
            $this->UserDbContractsController->deleteItemsByParams($options);

            $plot_result_after = $this->UserDbContractsController->getPlotIDsForContracts();
            $plotsResAfterCount = count($plot_result_before);
            for ($i = 0; $i < $plotsResAfterCount; $i++) {
                $after_delete[] = $plot_result_after[$i]['plot_id'];
            }

            $diff_array = array_diff($before_delete, $after_delete);
            if (0 != count($diff_array)) {
                $id_string = implode(', ', $diff_array);
                // update status contract to false;
                $this->UserDbContractsController->updatePlotContractStatus($id_string, false);
            }

            $this->UserDbContractsController->manageOverallRenta($checked);

            $transaction->commit();
        } catch (Exception $e) {
            $transaction->rollback();

            throw $e;
        }

        // outside of transaction because we need transaction to be commited when trying to resolve contract and load data for it
        if ($plotData['file_id']) {
            $this->tryResolveContracts(
                $plotData['file_id'],
                $plotData['ekate'],
                [
                    [
                        'kad_ident' => $plotData['kad_ident'],
                        'edit_active_from' => (DateTime::createFromFormat('Y-m-d H:i:s.u', $plotData['date_uploaded']))->modify('-1 day')->format('Y-m-d'),
                    ],
                ]
            );
        }

        $this->UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, ['contract_id' => $checked, 'plot_id' => $plot_id], [], 'deletes contract-plot relation');
    }

    public function saveContractPrice($contract, $price)
    {
        $this->UserDbController = new UserDbController($this->User->Database);

        $options = [
            'tablename' => $this->UserDbController->DbHandler->tableContracts,
            'mainData' => [
                'contract_price' => $price,
            ],
            'where' => ['id' => $contract],
        ];

        $this->UserDbController->editItem($options);

        $single_price = $this->UserDbController->getSinglePlotPricePerContract($contract);
        $single_price = number_format($single_price, 2, '.', '');

        $this->UserDbController->setSinglePricePerContract($contract, $single_price);

        return $single_price;
    }

    public function getContractPrice($contract)
    {
        $this->UserDbController = new UserDbController($this->User->Database);

        $options = [
            'tablename' => $this->UserDbController->DbHandler->tableContracts,
            'return' => [
                'contract_price',
            ],
            'where' => [
                'id' => ['column' => 'id', 'compare' => '=', 'value' => $contract],
            ],
        ];
        $result = $this->UserDbController->getItemsByParams($options, false, false);

        $single_price = $this->UserDbController->getSinglePlotPricePerContract($contract);

        return ['contract_price' => $result[0]['contract_price'], 'single_price' => $single_price];
    }

    public function getPlotsRents(array $filterObj, int $page = null, int $rows = null, ?string $sort = '', ?string $order = '')
    {
        $UserDbContractsController = new UserDbContractsController($this->User->Database);
        $filterObj['skip_no_area_generic'] = true;

        // get plot rents data
        $plotsRents = $UserDbContractsController->getPlotsRents($filterObj);

        foreach ($plotsRents as $plotRentKey => $plotRent) {
            $rents = json_decode($plotRent['rents_json'], true);
            $plotsRents[$plotRentKey]['rent_money'] = round($rents['money'], 2);
            $plotsRents[$plotRentKey]['rent_money_text'] = BGNtoEURO($rents['money']);
            $plotsRents[$plotRentKey]['rent_per_plot'] = round($rents['rent_per_plot'], 2);
            $plotsRents[$plotRentKey]['type_text'] = $GLOBALS['Contracts']['plots_rent_types'][$plotRent['type']];
            $plotsRents[$plotRentKey]['rent_nat'] = [];

            if ($rents['rent_nature']) {
                foreach ($rents['rent_nature'] as $rentNat) {
                    $plotsRents[$plotRentKey]['rent_nat'][] = $rentNat['value'] . ' ' . $GLOBALS['Contracts']['renta_units'][$rentNat['unit']]['name'] . '. ' . $rentNat['name'];
                }
            }

            $plotsRents[$plotRentKey]['rent_nat_text'] = implode('<br>', $plotsRents[$plotRentKey]['rent_nat']);
        }

        return $plotsRents;
    }

    public function getPlotsRentsCombobox(int $contractId)
    {
        $UserDbContractsController = new UserDbContractsController($this->User->Database);
        $options = [
            'contract_id' => $contractId,
            'rent_type' => 'generic',
        ];

        // get plot rents data
        $plotsRents = $UserDbContractsController->getPlotsRents($options);

        if (!empty($plotsRents[0])) {
            $plotsRents[0]['selected'] = true;
        }

        return $plotsRents;
    }

    public function clearPlotRentsTypes(int $pcRelId)
    {
        $UserDbContractsController = new UserDbContractsController($this->User->Database);

        $plotsRents = $UserDbContractsController->getItemsByParams([
            'return' => ['contract_id', 'plot_id'],
            'tablename' => 'su_contracts_plots_rel',
            'where' => [
                'pc_rel_id' => ['column' => 'id', 'compare' => '=', 'value' => $pcRelId],
            ],
        ]);

        if (empty($plotsRents)) {
            throw new MTRpcException('PLOT_RENTS_NOT_FOUND', -33750);
        }

        if ($UserDbContractsController->hasContractChargedRent($plotsRents[0]['contract_id'], $plotsRents[0]['plot_id'])) {
            throw new MTRpcException('CANNOT_EDIT_CONTRACT_WITH_CHARGED_RENT', -33760);
        }

        $UserDbContractsController->clearPlotRentsTypes($pcRelId);

        return true;
    }

    private function applyContractFilter($filterObj)
    {
        $arrayHelper = $this->UsersController->ArrayHelper;
        $filterOptions = [
            'return' => [
                'array_agg(kvs.gid) as gids',
            ],
            'where' => [
                'contract_id' => ['column' => 'id', 'compare' => '=', 'prefix' => 'c', 'value' => $filterObj['contract_id']],
                // plot filter
                'kad_ident' => ['column' => 'kad_ident', 'compare' => '=', 'prefix' => 'kvs', 'value' => $filterObj['contracts_tree_filter']['kad_ident']],
                'ekate' => ['column' => 'ekate', 'compare' => 'IN', 'prefix' => 'kvs', 'value' => $arrayHelper->filterEmptyStringArr($filterObj['contracts_tree_filter']['ekate'])],
                'masiv' => ['column' => 'masiv', 'compare' => '=', 'prefix' => 'kvs', 'value' => $filterObj['contracts_tree_filter']['masiv']],
                'number' => ['column' => 'number', 'compare' => '=', 'prefix' => 'kvs', 'value' => $filterObj['contracts_tree_filter']['number']],
                'category' => ['column' => 'category', 'compare' => 'IN', 'prefix' => 'kvs', 'value' => $arrayHelper->filterEmptyStringArr($filterObj['contracts_tree_filter']['category'])],
                'area_type' => ['column' => 'area_type', 'compare' => 'IN', 'prefix' => 'kvs', 'value' => $arrayHelper->filterEmptyStringArr($filterObj['contracts_tree_filter']['ntp'])],
            ],
        ];
        if ('all' != $filterObj['contracts_tree_filter']['irrigated_area']) {
            $filterOptions['where']['irrigated_area'] = ['column' => 'irrigated_area', 'compare' => '=', 'prefix' => 'kvs', 'value' => $filterObj['contracts_tree_filter']['irrigated_area']];
        }

        $filteredPlots = $this->UserDbContractsController->getContractsFilteredPlots($filterOptions, false, false);

        return explode(',', trim($filteredPlots[0]['gids'], '{}'));
    }
}
