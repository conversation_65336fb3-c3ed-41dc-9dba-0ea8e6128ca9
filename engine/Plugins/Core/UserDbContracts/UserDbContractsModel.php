<?php

namespace TF\Engine\Plugins\Core\UserDbContracts;

use PDO;
use TF\Engine\Plugins\Core\UserDb\UserDbModel;

class UserDbContractsModel extends UserDbModel
{
    public function getContractsData($options, $counter, $returnOnlySQL)
    {
        if (true == $counter) {
            $return = 'COUNT(*)';
        } elseif ($options['return']) {
            $return = implode(', ', $options['return']);
        } else {
            $return = '*';
        }

        $sql = "SELECT {$return} FROM " . $this->tableContracts . ' WHERE true';

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }

        if (!empty($options['order']) && $options['sort'] && false == $counter) {
            $sql .= ' ORDER BY ' . $options['sort'] . ' ' . $options['order'];
        }

        $limit = filter_var($options['limit'], FILTER_VALIDATE_INT);
        $offset = filter_var($options['offset'], FILTER_VALIDATE_INT);

        if (false !== $limit && false !== $offset && false === $counter) {
            $sql .= $returnOnlySQL
                ? " LIMIT {$limit} OFFSET {$offset}"
                : ' LIMIT :limit OFFSET :offset';
        }

        if ($returnOnlySQL) {
            return $sql;
        }

        $cmd = $this->DbModule->createCommand($sql);

        if (isset($options['limit'], $options['offset']) && !$counter) {
            $cmd->bindParameter(':limit', $options['limit'], PDO::PARAM_INT);
            $cmd->bindParameter(':offset', $options['offset'], PDO::PARAM_INT);
        }

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        return $cmd->query()->readAll();
    }

    public function getSalesContractsData($options, $counter, $returnOnlySQL)
    {
        if (true == $counter) {
            $return = 'COUNT(*)';
        } elseif ($options['return']) {
            $return = implode(', ', $options['return']);
        } else {
            $return = '*';
        }

        $sql = "SELECT {$return} FROM " . $this->tableSalesContracts . ' WHERE true';

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }

        if ($options['order'] && $options['sort'] && false == $counter) {
            $sql .= ' ORDER BY ' . $options['sort'] . ' ' . $options['order'];
        }

        $limit = filter_var($options['limit'], FILTER_VALIDATE_INT);
        $offset = filter_var($options['offset'], FILTER_VALIDATE_INT);

        if (false !== $limit && false !== $offset && false === $counter) {
            $sql .= $returnOnlySQL
                ? " LIMIT {$limit} OFFSET {$offset}"
                : ' LIMIT :limit OFFSET :offset';
        }

        if ($returnOnlySQL) {
            return $sql;
        }

        $cmd = $this->DbModule->createCommand($sql);

        if (isset($options['limit'], $options['offset']) && !$counter) {
            $cmd->bindParameter(':limit', $options['limit'], PDO::PARAM_INT);
            $cmd->bindParameter(':offset', $options['offset'], PDO::PARAM_INT);
        }

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        return $cmd->query()->readAll();
    }

    public function getContractsPlotsRelations($contract_id)
    {
        $table = $this->contractsPlotsRelTable;

        $sql = "SELECT * FROM {$table} WHERE true";

        if ($contract_id) {
            $sql .= ' AND contract_id = :contract_id';
        }

        $cmd = $this->DbModule->createCommand($sql);

        if ($contract_id) {
            $cmd->bindParameter(':contract_id', $contract_id);
        }

        return $cmd->query()->readAll();
    }

    public function getPlotsRentsAreas(array $options)
    {
        $sql = 'SELECT 
                    spr.id, 
                    scpr.plot_id,
                    spr.pc_rel_id, 
                    spr.rent_type_id, 
                    spr.area 
                FROM su_plots_rents spr
                left join su_contracts_plots_rel scpr on scpr.id = spr.pc_rel_id 
                WHERE true';

        if (!empty($options['pc_rel_id'])) {
            $sql .= ' AND spr.pc_rel_id in (:pc_rel_ids)';
        }

        if (isset($options['contract_id'])) {
            $sql .= ' AND scpr.contract_id = (:contract_id)';
        }

        if (!empty($options['plot_id'])) {
            $sql .= ' AND scpr.plot_id = (:plot_id)';
        }

        $cmd = $this->DbModule->createCommand($sql);

        if ($options['pc_rel_ids']) {
            $pc_rel_ids = implode(',', $options['pc_rel_ids']);
            $cmd->bindParameter(':pc_rel_ids', $pc_rel_ids);
        }

        if (isset($options['contract_id'])) {
            $cmd->bindParameter(':contract_id', $options['contract_id']);
        }

        if ($options['plot_id']) {
            $cmd->bindParameter(':plot_id', $options['plot_id']);
        }

        return $cmd->query()->readAll();
    }

    public function getPlotsRents(array $options)
    {
        $sql = "
            select 
                lk.kad_ident,
                spr.id as plot_rent_id,
                spr.pc_rel_id,
                scpr.area_for_rent,
                spr.area,
                scrt.id,
                scrt.\"type\",
                scrt.value,
                (case 
                    when scrt.\"type\" = 'category' then get_plot_category_by_id(scrt.value)
                    when scrt.\"type\" = 'ntp' then scrt.value || ': ' || get_ntp_title_by_code(scrt.value)
                    when scrt.\"type\" = 'arable' then (case when scrt.\"value\" = 'true' then 'Обработваема земя' else 'Необработваема земя' end)
                    else '-'
                end) as value_txt,
                jsonb_build_object(
                        'money', scrt.rents->>'money',
                        'rent_nature', jsonb_agg(
                            jsonb_build_object(
                                'id', rk.elem->>'id',
                                'value', rk.elem->>'value',
                                'name', r.name,
                                'unit', r.unit
                            )
                        ),
                        'overall_money', scrt.rents->>'overall_money',
                        'rent_per_plot', scrt.rents->>'rent_per_plot'
                ) AS rents_json
            from su_contracts_plots_rel scpr
            left join su_plots_rents spr on spr.pc_rel_id = scpr.id
            left join su_contracts_rents_types scrt on scrt.id = spr.rent_type_id 
            left join layer_kvs lk on lk.gid = scpr.plot_id 
            LEFT JOIN LATERAL jsonb_array_elements(scrt.rents->'rent_nature') AS rk(elem) ON true
            LEFT JOIN su_renta_types r ON (rk.elem->>'id')::int = r.id
            where true
                and scpr.annex_action = 'added'
        ";

        if (!empty($options['plot_rent_id'])) {
            $sql .= ' and spr.id = :plot_rent_id';
        }

        if (!empty($options['rent_type'])) {
            $sql .= ' and scrt.type = :rent_type';
        }

        if (!empty($options['contract_id'])) {
            $sql .= ' and scpr.contract_id = :contract_id';
        }
        if (!empty($options['pc_rel_id'])) {
            $sql .= ' and spr.pc_rel_id = :pc_rel_id';
        }

        if (!empty($options['rent_type_ids'])) {
            $placeholders = [];
            foreach ($options['rent_type_ids'] as $index => $id) {
                $placeholders[] = ":id{$index}";
            }
            $sql .= ' and scrt.id IN (' . implode(',', $placeholders) . ')';
        }

        if (!empty($options['skip_no_area_generic'])) {
            $sql .= ' and not (scrt."type" = \'generic\' and area = 0)';
        }

        $sql .= ' group by lk.kad_ident, scpr.id, spr.id, scrt.id';

        $sql .= ' order by scrt."type"';

        $cmd = $this->DbModule->createCommand($sql);

        if (!empty($options['rent_type'])) {
            $cmd->bindParameter(':rent_type', $options['rent_type']);
        }

        if (!empty($options['contract_id'])) {
            $cmd->bindParameter(':contract_id', $options['contract_id']);
        }

        if (!empty($options['pc_rel_id'])) {
            $cmd->bindParameter(':pc_rel_id', $options['pc_rel_id']);
        }

        if (!empty($options['plot_rent_id'])) {
            $cmd->bindParameter(':plot_rent_id', $options['plot_rent_id']);
        }

        if (!empty($options['rent_type_ids'])) {
            foreach ($options['rent_type_ids'] as $index => $id) {
                $cmd->bindValue(":id{$index}", (int)$id, PDO::PARAM_INT);
            }
        }

        return $cmd->query()->readAll();
    }

    /**
     * Get plots in a given contract that have plot rents with rent amount greater than 0.
     *
     * This method queries the database to find all plots associated with a specific contract
     * that have rent information where any of the rent amounts (money, overall_money, or
     * rent_per_plot) is greater than 0. Only plots with 'added' annex_action are included.
     *
     * @param int $contract_id The contract ID to search for plots
     *
     * @return array Array of plots with rent information
     */
    public function getPlotsWithSpecificRent($contract_id)
    {
        $sql = "
            SELECT DISTINCT
                lk.kad_ident,
                lk.gid as plot_id,
                scpr.id as pc_rel_id,
                scpr.contract_area,
                scpr.area_for_rent,
                spr.id as plot_rent_id,
                spr.area as rent_area,
                scrt.id as rent_type_id,
                scrt.type as rent_type,
                scrt.value as rent_value,
                (case
                    when scrt.type = 'category' then get_plot_category_by_id(scrt.value)
                    when scrt.type = 'ntp' then scrt.value || ': ' || get_ntp_title_by_code(scrt.value)
                    when scrt.type = 'arable' then (case when scrt.value = 'true' then 'Обработваема земя' else 'Необработваема земя' end)
                    else '-'
                end) as rent_type_description,
                COALESCE((scrt.rents->>'money')::numeric, 0) as rent_money,
                COALESCE((scrt.rents->>'overall_money')::numeric, 0) as overall_rent_money,
                COALESCE((scrt.rents->>'rent_per_plot')::numeric, 0) as rent_per_plot
            FROM su_contracts_plots_rel scpr
            INNER JOIN su_plots_rents spr ON spr.pc_rel_id = scpr.id
            INNER JOIN su_contracts_rents_types scrt ON scrt.id = spr.rent_type_id
            INNER JOIN layer_kvs lk ON lk.gid = scpr.plot_id
            WHERE scpr.contract_id = :contract_id
                AND scpr.annex_action = 'added'
            ORDER BY lk.kad_ident, scrt.type
        ";

        $cmd = $this->DbModule->createCommand($sql);
        $cmd->bindParameter(':contract_id', $contract_id);

        return $cmd->query()->readAll();
    }

    public function getContractAnnexRentsTypeRelation(int $contractId, int $annexId)
    {
        $sql = '
            WITH c_contract_rents_types AS (
                SELECT
                    scrt.id, 
                    scrt.contract_id, 
                    scrt.type, 
                    scrt.value,
                    scrt.rents
                FROM su_contracts_rents_types scrt
                WHERE scrt.contract_id = :contract_id
            ), a_contract_rents_types AS (
                SELECT
                    scrt.id, 
                    scrt.contract_id, 
                    scrt.type, 
                    scrt.value
                FROM su_contracts_rents_types scrt
                LEFT JOIN c_contract_rents_types c_scrt ON c_scrt.type = scrt.type and c_scrt.value = scrt.value
                WHERE  scrt.contract_id = :annex_id
            )
            SELECT 	
                c_scrt.id as contract_renta_type_id,
                a_scrt.id as annex_renta_type_id,
                c_scrt.type, 
                c_scrt.value,
                c_scrt.rents
            FROM c_contract_rents_types c_scrt
            LEFT JOIN a_contract_rents_types a_scrt ON a_scrt.type = c_scrt.type and c_scrt.value = a_scrt.value
        ';

        $cmd = $this->DbModule->createCommand($sql);

        $cmd->bindParameter(':contract_id', $contractId);
        $cmd->bindParameter(':annex_id', $annexId);

        return $cmd->query()->readAll();
    }

    public function getSalesContractsPlotsRelations($contract_id)
    {
        $table = $this->salesContractsPlotsRelTable;

        $sql = "SELECT * FROM {$table} WHERE true";

        if ($contract_id) {
            $sql .= ' AND sales_contract_id = :sales_contract_id';
        }

        $cmd = $this->DbModule->createCommand($sql);

        if ($contract_id) {
            $cmd->bindParameter(':sales_contract_id', $contract_id);
        }

        return $cmd->query()->readAll();
    }

    public function getPlotDataForContracts($params, $counter, $returnOnlySQL)
    {
        $custom_counter = false;

        if ('' != $params['custom_counter']) {
            $custom_counter = $params['custom_counter'];
        }
        $return = $this->createReturnVariable($params['return'], $counter, $custom_counter);

        if ('' != $params['custom_counter']) {
            $custom_counter = $params['custom_counter'];
        }
        $return = $this->createReturnVariable($params['return'], $counter, $custom_counter);

        $sql = "SELECT {$return} FROM {$this->tableKVS} kvs ";

        if ($params['joinContractsPlotsRel']) {
            $sql .= " LEFT JOIN {$this->contractsPlotsRelTable} pc ON (pc.plot_id = kvs.gid)";
        }

        if ($params['joins'] && is_array($params['joins'])) {
            foreach ($params['joins'] as $join) {
                $sql .= ' ' . $join . ' ';
            }
        }

        $sql .= ' WHERE true';

        if ($params['where']) {
            $sql = $this->createWhereSQL($sql, $params['where'], $returnOnlySQL);
        }
        if (is_array($params['category']) && count($params['category'])) {
            $key = array_search('-1', $params['category']);

            $withoutCategorySelected = false;

            // if selected "Без категория"
            if (false !== $key) {
                unset($params['category'][$key]);

                $withoutCategorySelected = true;
            }

            $arrCategory = array_values($params['category']);

            if (count($arrCategory)) {
                $sql .= ' AND (category IN(';

                for ($i = 0; $i < count($arrCategory); $i++) {
                    $sql .= "'" . $arrCategory[$i] . "'";

                    if ($i < count($arrCategory) - 1) {
                        $sql .= ', ';
                    }
                }

                $sql .= ')';

                if ($withoutCategorySelected) {
                    $sql .= " OR (category ='0' OR category ='' OR category isnull))";
                } else {
                    $sql .= ')';
                }
            } else {
                $sql .= " AND (category ='0' OR category ='' OR category isnull)";
            }
        }

        if (isset($params['id_string'])) {
            $id_string = $params['id_string'];
            $sql .= " AND gid IN ({$id_string})";
        }
        if (isset($params['anti_id_string'])) {
            $anti_id_string = $params['anti_id_string'];
            $sql .= " AND gid NOT IN ({$anti_id_string})";
        }

        if ($params['group'] && false == $counter) {
            $sql .= ' GROUP BY ' . $params['group'];
        }

        if ($params && false == $counter) {
            if ($params['sort']) {
                $sql .= ' ORDER BY ' . $params['sort'] . ' ' . $params['order'];
            }
            if ($params['limit']) {
                $sql .= ' LIMIT ' . $params['limit'] . ' OFFSET ' . $params['offset'];
            }
        }

        if ($returnOnlySQL) {
            return $sql;
        }

        $cmd = $this->DbModule->createCommand($sql);

        if ($params['where']) {
            $this->createWhereBinds($cmd, $params['where']);
        }

        return $cmd->query()->readAll();
    }

    public function getPlotDataForSalesContracts($params, $counter, $returnOnlySQL)
    {
        $return = $this->createReturnVariable($params['return'], $counter);

        $sql = "SELECT {$return} FROM {$this->tableKVS} kvs
				INNER JOIN {$this->contractsPlotsRelTable} cpr ON (cpr.plot_id = kvs.gid)
				INNER JOIN {$this->tableContracts} c ON (c.id = cpr.contract_id)
				LEFT JOIN {$this->tableHypothecsPlotsRel} hpr ON (hpr.plot_id = kvs.gid)
				LEFT JOIN {$this->tableSubleasesPlotsContractsRel} spcr ON (spcr.pc_rel_id = cpr.id)
				LEFT JOIN {$this->salesContractsPlotsRelTable} scpr ON (scpr.pc_rel_id = cpr.id AND kvs.gid = scpr.plot_id)
				LEFT JOIN {$this->tableContracts} scs ON scs.id = spcr.sublease_id
				WHERE true";

        if ($params['where']) {
            $sql = $this->createWhereSQL($sql, $params['where'], $returnOnlySQL);
        }

        if ($params['whereOr']) {
            $sql = $this->createWhereOrSQL($sql, $params['whereOr'], $returnOnlySQL);
        }

        if (isset($params['id_string'])) {
            $id_string = $params['id_string'];
            $sql .= " AND gid IN ({$id_string})";
        }
        if (isset($params['anti_id_string'])) {
            $anti_id_string = $params['anti_id_string'];
            $sql .= " AND gid NOT IN ({$anti_id_string})";
        }

        if (isset($params['anti_pc_rel_string'])) {
            $anti_pc_rel_string = $params['anti_pc_rel_string'];
            $sql .= " AND cpr.id NOT IN ({$anti_pc_rel_string})";
        }

        if ($params['group']) {
            $sql .= ' GROUP BY ' . $params['group'];
        }

        if ($params['having']) {
            $sql .= $params['having'];
        }

        if ($params && false == $counter) {
            if ($params['sort']) {
                $sql .= ' ORDER BY ' . $params['sort'] . ' ' . $params['order'];
            }
            if ($params['limit']) {
                $sql .= ' LIMIT ' . $params['limit'] . ' OFFSET ' . $params['offset'];
            }
        }

        if ($returnOnlySQL) {
            return $sql;
        }

        $cmd = $this->DbModule->createCommand($sql);

        if ($params['where']) {
            $this->createWhereBinds($cmd, $params['where']);
        }

        if ($params['whereOr']) {
            $this->createWhereBinds($cmd, $params['whereOr']);
        }

        return $cmd->query()->readAll();
    }

    public function getSalesContractsSubleasedPlot($params, $counter, $returnOnlySQL)
    {
        $return = $this->createReturnVariable($params['return'], $counter);

        $sql = "SELECT {$return} FROM {$this->contractsPlotsRelTable} cpr
				INNER JOIN {$this->tableSubleasesPlotsContractsRel} spcr on spcr.pc_rel_id = cpr.id
				INNER JOIN {$this->tableContracts} c on c.id = spcr.sublease_id
				INNER JOIN {$this->tableKVS} kvs on kvs.gid = cpr.plot_id
				WHERE true";

        if ($params['where']) {
            $sql = $this->createWhereSQL($sql, $params['where'], $returnOnlySQL);
        }

        if (isset($params['plot_id_string']) && strlen($params['plot_id_string'])) {
            $plot_id_string = $params['plot_id_string'];
            $sql .= " AND gid IN ({$plot_id_string})";
        }

        if ($params && false == $counter) {
            if ($params['sort']) {
                $sql .= ' ORDER BY ' . $params['sort'] . ' ' . $params['order'];
            }
            if ($params['limit']) {
                $sql .= ' LIMIT ' . $params['limit'] . ' OFFSET ' . $params['offset'];
            }
        }

        if ($returnOnlySQL) {
            return $sql;
        }

        $cmd = $this->DbModule->createCommand($sql);

        if ($params['where']) {
            $this->createWhereBinds($cmd, $params['where']);
        }

        return $cmd->query()->readAll();
    }

    public function getSubleaseContractName($gid, $activeAfter = null)
    {
        $sql = "SELECT c.id, c.c_num, to_char(c.start_date, 'DD.MM.YYYY') start_date, to_char(c.due_date, 'DD.MM.YYYY') due_date FROM " . $this->contractsPlotsRelTable . ' cpr
				INNER JOIN ' . $this->tableSubleasesPlotsContractsRel . ' spcr on spcr.pc_rel_id = cpr.id
				INNER JOIN ' . $this->tableContracts . ' c on c.id = spcr.sublease_id
				WHERE cpr.plot_id = :plot_id AND c.active = true';

        if (!empty($activeAfter)) {
            $sql .= ' AND c.due_date > :activeAfter';
        }

        $sql .= ' GROUP BY c.id ORDER BY c.start_date';
        $cmd = $this->DbModule->createCommand($sql);
        $cmd->bindParameter(':plot_id', $gid);
        if (!empty($activeAfter)) {
            $cmd->bindParameter(':activeAfter', $activeAfter);
        }

        return $cmd->query()->readAll();
    }

    public function getPlotIDsForContracts()
    {
        $sql = "SELECT plot_id FROM {$this->contractsPlotsRelTable} GROUP BY plot_id";
        $cmd = $this->DbModule->createCommand($sql);

        return $cmd->query()->readAll();
    }

    public function getPlotIDsForSalesContracts()
    {
        $sql = "SELECT plot_id FROM {$this->salesContractsPlotsRelTable} GROUP BY plot_id";
        $cmd = $this->DbModule->createCommand($sql);

        return $cmd->query()->readAll();
    }

    public function updatePlotContractStatus($id_string, $status)
    {
        if ($status) {
            $contract_status = 'TRUE';
        } else {
            $contract_status = 'FALSE';
        }
        if (0 != strlen($id_string)) {
            $sql = 'UPDATE ' . $this->tableKVS . ' SET has_contracts = ' . $contract_status . " WHERE gid IN ({$id_string})";
            $cmd = $this->DbModule->createCommand($sql);
            $cmd->execute();
        }
    }

    public function getContractPlotRelationID($contract_id, $plot_id)
    {
        $sql = 'SELECT * FROM ' . $this->contractsPlotsRelTable . ' WHERE contract_id = :contract_id AND plot_id = :plot_id';

        $cmd = $this->DbModule->createCommand($sql);

        $cmd->bindParameter(':contract_id', $contract_id);
        $cmd->bindParameter(':plot_id', $plot_id);

        $result = $cmd->query()->read();

        return $result['id'] ?? '';
    }

    public function getPendingPayments($options, $counter)
    {
        $bindNames = array_keys($options['where']);
        $bindOptions = array_values($options['where']);

        if (!$counter) {
            $return = "a.rep_name || ' ' || a.rep_surname || ' ' || a.rep_lastname as rep_names,
					a.name || ' ' || a.surname || ' ' || a.lastname as owner_names,
					company_name,
    				a.id as owner_id,
    				e.kad_ident,
    				d.c_num,
    				d.id as contract_id,
    				e.gid as plot_id,
    				b.percent,
    				(ST_Area(e.geom)/1000*b.percent/100)*d.renta AS renta,
    				(ST_Area(e.geom)/1000*b.percent/100) AS nat_renta,
    				d.renta_nat_type,
					d.renta as renta_amount,
    				(SELECT SUM(amount) FROM su_payments p WHERE p.contract_id = d.id AND p.owner_id = a.id AND p.plot_id = e.gid) AS paid_renta,
					(SELECT SUM(nat_amount) FROM su_payments p WHERE p.contract_id = d.id AND p.owner_id = a.id AND p.plot_id = e.gid) AS paid_nat_renta";
        } else {
            $return = 'COUNT(*)';
        }

        $sql = "SELECT {$return} FROM su_owners a
					INNER JOIN su_plots_owners_rel b ON (a.id = b.owner_id)
					INNER JOIN su_contracts_plots_rel c ON (b.pc_rel_id = c.id)
					INNER JOIN su_contracts d ON (d.id = c.contract_id)
					INNER JOIN layer_kvs e ON (e.gid = c.plot_id)
						WHERE d.nm_usage_rights != 1
						AND EXTRACT(YEAR FROM d.start_date) <= :year1
						AND EXTRACT(YEAR FROM d.due_date) >= :year2";

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], false);
        }

        if ($options && false == $counter) {
            if ($options['sort']) {
                $sql .= ' ORDER BY ' . $options['sort'] . ' ' . $options['order'];
            }
            if (isset($options['limit'], $options['offset'])) {
                $sql .= ' LIMIT :limit OFFSET :offset';
            }
        }

        $cmd = $this->DbModule->createCommand($sql);

        if (isset($options['limit'], $options['offset']) && !$counter) {
            $cmd->bindParameter(':limit', $options['limit'], PDO::PARAM_INT);
            $cmd->bindParameter(':offset', $options['offset'], PDO::PARAM_INT);
        }

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }
        $cmd->bindParameter(':year1', $options['year']);
        $cmd->bindParameter(':year2', $options['year']);

        return $cmd->query()->readAll();
    }

    public function getPayments($options, $counter)
    {
        $return = $this->createReturnVariable($options['return'], $counter);

        $sql = "SELECT {$return} FROM " . $this->tablePayments . ' p
	    			INNER JOIN ' . $this->tableOwners . ' o ON(p.owner_id = o.id)
	    			INNER JOIN ' . $this->tableContracts . ' c ON(p.contract_id = c.id)
	    			INNER JOIN ' . $this->tableKVS . ' l ON(p.plot_id = l.gid)
	    				WHERE true';

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], false);
        }

        if ($options && false == $counter) {
            if ($options['sort']) {
                $sql .= ' ORDER BY ' . $options['sort'] . ' ' . $options['order'];
            }
            if (isset($options['limit'], $options['offset'])) {
                $sql .= ' LIMIT :limit OFFSET :offset';
            }
        }

        $cmd = $this->DbModule->createCommand($sql);

        if (isset($options['limit'], $options['offset']) && !$counter) {
            $cmd->bindParameter(':limit', $options['limit'], PDO::PARAM_INT);
            $cmd->bindParameter(':offset', $options['offset'], PDO::PARAM_INT);
        }

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        return $cmd->query()->readAll();
    }

    public function getSubleasedContractsData($options, $counter, $returnOnlySQL)
    {
        $return = $this->createReturnVariable($options['return'], $counter);

        $sql = "SELECT {$return} FROM {$this->tableContracts} c
					INNER JOIN {$this->tableContracts} c2 ON(c.parent_id = c2.id) WHERE true";

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }

        if ($options['group']) {
            $sql .= ' GROUP BY ' . $options['group'];
        }

        if ($options['order'] && $options['sort'] && false == $counter) {
            $sql .= ' ORDER BY ' . $options['sort'] . ' ' . $options['order'];
        }

        $limit = filter_var($options['limit'], FILTER_VALIDATE_INT);
        $offset = filter_var($options['offset'], FILTER_VALIDATE_INT);

        if (false !== $limit && false !== $offset && false === $counter) {
            $sql .= $returnOnlySQL
                ? " LIMIT {$limit} OFFSET {$offset}"
                : ' LIMIT :limit OFFSET :offset';
        }

        if ($returnOnlySQL) {
            return $sql;
        }

        $cmd = $this->DbModule->createCommand($sql);

        if (isset($options['limit'], $options['offset']) && !$counter) {
            $cmd->bindParameter(':limit', $options['limit'], PDO::PARAM_INT);
            $cmd->bindParameter(':offset', $options['offset'], PDO::PARAM_INT);
        }

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        return $cmd->query()->readAll();
    }

    public function getAnnexes($options, $counter, $returnOnlySQL)
    {
        $return = $this->createReturnVariable($options['return'], $counter);

        $sql = "SELECT {$return} FROM {$this->tableContracts} a 
					INNER JOIN {$this->tableContracts} c ON(a.parent_id = c.id)
						WHERE true";

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }

        if ($options['group']) {
            $sql .= ' GROUP BY ' . $options['group'];
        }

        if ($options['order'] && $options['sort'] && false == $counter) {
            $sql .= ' ORDER BY ' . $options['sort'] . ' ' . $options['order'];
        }

        $limit = filter_var($options['limit'], FILTER_VALIDATE_INT);
        $offset = filter_var($options['offset'], FILTER_VALIDATE_INT);

        if (false !== $limit && false !== $offset && false === $counter) {
            $sql .= $returnOnlySQL
                ? " LIMIT {$limit} OFFSET {$offset}"
                : ' LIMIT :limit OFFSET :offset';
        }

        if ($returnOnlySQL) {
            return $sql;
        }

        $cmd = $this->DbModule->createCommand($sql);

        if (isset($options['limit'], $options['offset']) && !$counter) {
            $cmd->bindParameter(':limit', $options['limit'], PDO::PARAM_INT);
            $cmd->bindParameter(':offset', $options['offset'], PDO::PARAM_INT);
        }

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        return $cmd->query()->readAll();
    }

    public function getContractDataByPCRel($options, $counter, $returnOnlySQL)
    {
        $return = $this->createReturnVariable($options['return'], $counter);

        $startDateSql = '';
        $dueDateSql = '';
        if (!empty($options['start_date'])) {
            $startDateSql = 'AND a.start_date <= :start_date';
        }
        if (!empty($options['due_date'])) {
            $dueDateSql = 'AND a.due_date >= :due_date';
        }

        $sql = '';
        if (isset($options['exclude_contract_if_annex_or_contract_from_sublease'])) {
            $sql = 'WITH contract_details AS (';
        }

        $sql .= "SELECT {$return} FROM {$this->contractsPlotsRelTable} pc
                    RIGHT JOIN {$this->tableContracts} c ON(c.id = pc.contract_id)
						LEFT JOIN {$this->tableContracts} a ON(a.parent_id = c.id AND a.active = true {$startDateSql} {$dueDateSql})
                ";

        if (!empty($options['joins']) && is_array($options['joins'])) {
            foreach ($options['joins'] as $join) {
                $sql .= ' ' . $join . ' ';
            }
        }

        $sql .= ' WHERE true';

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }

        if (isset($options['exclude_removed_with_annex']) && true === $options['exclude_removed_with_annex']) {
            $sql .= ' AND c.id not in (select parent_id from su_contracts where id in (select contract_id from su_contracts_plots_rel cpr1
				inner join su_contracts c1 on cpr1.contract_id = c1.id WHERE cpr1.plot_id = pc.plot_id AND c1 .start_date <= a.due_date AND c1 .due_date >= a.start_date) and pc.annex_action = \'removed\')';
        } elseif (isset($options['exclude_removed_with_annex']) && false === $options['exclude_removed_with_annex']) {
            $sql .= ' AND c.id not in (select parent_id from su_contracts where id in (select contract_id from su_contracts_plots_rel cpr1
				inner join su_contracts c1 on cpr1.contract_id = c1.id WHERE cpr1.plot_id = pc.plot_id AND c1 .start_date <= a.due_date AND c1 .due_date >= a.start_date))';
        }

        if (isset($options['exclude_contract_annexes']) && true === $options['exclude_contract_annexes']) {
            $sql .= ' AND (c.parent_id IS NULL OR c.parent_id <> ' . $options['contract_id'] . ')';
        }

        if (isset($options['group'])) {
            $sql .= ' GROUP BY ' . $options['group'];
        }

        if (isset($options['order']) && $options['sort'] && false == $counter) {
            $sql .= ' ORDER BY ' . $options['sort'] . ' ' . $options['order'];
        }

        if ($options['exclude_contract_if_annex_or_contract_from_sublease']) {
            $sql .= '), contracts_to_exclude AS (
                            SELECT
                                cd1.contract_id
                            FROM
                                contract_details cd1
                            WHERE
                                EXISTS (
                                    SELECT 1
                                    FROM contract_details cd2
                                    WHERE cd2.parent_id = cd1.contract_id
                                )
                        )
                        SELECT
                            plot_id,
                            array_agg(contract_details.contract_area) as contract_area_array,
                            array_agg(contract_details.contract_id) as contract_id_array,
                            array_agg(contract_details.parent_id) as parents_ids,
                            array_agg(contract_details.contract_start_date) as contract_start_date_array,
                            array_agg(contract_details.contract_due_date) as contract_due_date_array,
                            array_agg(contract_details.pc_rel_id) as pc_rel_id_array,
                            contract_details.sold_area,
                            contract_details.sales_contract_ids,
                            array_agg(contract_details.is_annex::int) as is_annex
                        FROM
                            contract_details
                        WHERE
                            contract_id NOT IN (SELECT contract_id FROM contracts_to_exclude)
                            AND 
                            from_sublease is null
                            AND 
                            is_removed_by_annex = FALSE 
                        group by plot_id, sales_contract_ids, sold_area                        
            ';
        }

        $limit = filter_var($options['limit'], FILTER_VALIDATE_INT);
        $offset = filter_var($options['offset'], FILTER_VALIDATE_INT);

        if (false !== $limit && false !== $offset && false === $counter) {
            $sql .= $returnOnlySQL
                ? " LIMIT {$limit} OFFSET {$offset}"
                : ' LIMIT :limit OFFSET :offset';
        }

        if ($returnOnlySQL) {
            $sql = str_replace(':start_date', "'" . $options['start_date'] . "'", $sql);

            return str_replace(':due_date', "'" . $options['due_date'] . "'", $sql);
        }

        $cmd = $this->DbModule->createCommand($sql);

        if (isset($options['limit'], $options['offset']) && !$counter) {
            $cmd->bindParameter(':limit', $options['limit'], PDO::PARAM_INT);
            $cmd->bindParameter(':offset', $options['offset'], PDO::PARAM_INT);
        }

        if (!empty($options['start_date'])) {
            $cmd->bindParameter(':start_date', $options['start_date']);
        }
        if (!empty($options['due_date'])) {
            $cmd->bindParameter(':due_date', $options['due_date']);
        }

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        return $cmd->query()->readAll();
    }

    public function getContractContragentsData($options, $counter, $returnOnlySQL)
    {
        if (!$options['return']) {
            $options['return'] = ['c.*', 'o.*', 'r.*', 'c.id'];
        }

        $return = $this->createReturnVariable($options['return'], $counter);

        $sql = "SELECT {$return} FROM " . $this->tableContractsContragents . ' c '
                . 'LEFT JOIN ' . $this->tableOwners . ' o ON (c.owner_id = o.id) '
                . 'LEFT JOIN ' . $this->tableOwnersReps . ' r ON (c.rep_id = r.id) '
                . 'WHERE true';

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }

        if ($options && false == $counter) {
            if ($options['sort']) {
                $sql .= ' ORDER BY ' . $options['sort'] . ' ' . $options['order'];
            }

            $limit = filter_var($options['limit'], FILTER_VALIDATE_INT);
            $offset = filter_var($options['offset'], FILTER_VALIDATE_INT);

            if (false !== $limit && false !== $offset) {
                $sql .= $returnOnlySQL
                    ? " LIMIT {$limit} OFFSET {$offset}"
                    : ' LIMIT :limit OFFSET :offset';
            }
        }

        if ($returnOnlySQL) {
            return $sql;
        }

        $cmd = $this->DbModule->createCommand($sql);

        if (isset($options['limit'], $options['offset']) && !$counter) {
            $cmd->bindParameter(':limit', $options['limit'], PDO::PARAM_INT);
            $cmd->bindParameter(':offset', $options['offset'], PDO::PARAM_INT);
        }
        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        return $cmd->query()->readAll();
    }

    public function getContracts($options, $counter, $returnOnlySQL)
    {
        if ($counter) {
            $return = 'COUNT(DISTINCT(c.id))';
        } elseif ($options['return']) {
            $return = implode(', ', $options['return']);
        } else {
            $return = '*';
        }

        $sql = " WITH contracts as (
			SELECT 
				{$return}
			FROM {$this->tableContracts} c
			LEFT JOIN {$this->contractsRentsRelTable} cr ON (cr.contract_id = c.id)
			LEFT JOIN {$this->contractsPlotsRelTable} pc ON (pc.contract_id = c.id)
			LEFT JOIN {$this->tablePayments} p ON (p.contract_id = c.id)
			LEFT JOIN {$this->plotsOwnersRelTable} po ON (po.pc_rel_id = pc.id)
			LEFT JOIN {$this->tableOwners} o ON (o.id = po.owner_id)
			LEFT JOIN {$this->tableOwnersReps} o_r ON (o_r.id = po.rep_id)
			LEFT JOIN {$this->plotsFarmingRelTable} f_r ON (f_r.pc_rel_id = pc.id)
			LEFT JOIN {$this->tableKVS} kvs ON(kvs.gid = pc.plot_id)
			LEFT JOIN {$this->tableContracts} a ON (a.parent_id = c.id)
			LEFT JOIN {$this->tableContractsGroup} cg ON (cg.id = c.group)
            LEFT JOIN su_contracts_rents_types scrt ON (scrt.contract_id = c.id)
			WHERE 
				true and c.is_annex = false		
			";

        $sql .= $this->genContractsSql($options, $returnOnlySQL, $counter, true);

        $sql .= "), contracts_annexes AS (
            SELECT
                c.id AS contract_id,
                c.c_num AS contract_c_num,
                a.*
            FROM 
                contracts AS c
            JOIN {$this->tableContracts} AS a
                ON a.parent_id = c.id
                AND a.is_annex = TRUE
                AND a.is_sublease = FALSE
        ), annexes as (
			SELECT 
				{$return}
			FROM {$this->tableContracts} c
			LEFT JOIN {$this->contractsRentsRelTable} cr ON (cr.contract_id = c.id)
			LEFT JOIN {$this->contractsPlotsRelTable} pc ON (pc.contract_id = c.id)
			LEFT JOIN {$this->tablePayments} p ON (p.contract_id = c.id)
			LEFT JOIN {$this->plotsOwnersRelTable} po ON (po.pc_rel_id = pc.id)
			LEFT JOIN {$this->tableOwners} o ON (o.id = po.owner_id)
			LEFT JOIN {$this->tableOwnersReps} o_r ON (o_r.id = po.rep_id)
			LEFT JOIN {$this->plotsFarmingRelTable} f_r ON (f_r.pc_rel_id = pc.id)
			LEFT JOIN {$this->tableKVS} kvs ON(kvs.gid = pc.plot_id)
			LEFT JOIN contracts_annexes a ON (a.id = c.id)
            LEFT JOIN {$this->tableContractsGroup} cg ON (cg.id = c.group)
            LEFT JOIN su_contracts_rents_types scrt ON (scrt.contract_id = c.id)
			WHERE 
				true and c.is_annex = true
			";

        $annexOptions = $options;
        $annexOptions['whereCustom'] = ($annexOptions['whereCustom'] ?? '');

        if (isset($annexOptions['where']['contract_id'])) {
            // Filter annexes by contract's id or annex's id
            $contractIdValue = $annexOptions['where']['contract_id']['value'];

            if (is_array($contractIdValue) && count($contractIdValue) > 0) {
                $annexOptions['whereCustom'] .= ' AND (c.id IN (' . implode(',', $contractIdValue) . ')'
                    . ' OR a.contract_id IN (' . implode(',', $contractIdValue) . '))';
            } elseif ((int) $contractIdValue > 0) { // cast to int in case of string
                $annexOptions['whereCustom'] .= " AND (c.id = {$contractIdValue} OR a.contract_id = {$contractIdValue})";
            }

            $annexOptions['where']['contract_id']['value'] = null;
        }

        if (isset($annexOptions['where']['c_num']) && strlen($annexOptions['where']['c_num']['value']) > 0) {
            // Filter annexes by contract's c_num or annex's c_num
            $cnum = $annexOptions['where']['c_num']['value'];
            $annexOptions['whereCustom'] .= " AND (a.contract_c_num = '{$cnum}' OR c.c_num = '{$cnum}')";
            $annexOptions['where']['c_num']['value'] = null;
        }

        $sql .= $this->genContractsSql($annexOptions, $returnOnlySQL, $counter, true);

        $sql .= "), contracts_with_annnexes as (
			select 
				jsonb_agg(jsonb_build_object(
						'id', a.id,
						'ao_c_num', a.ao_c_num,
						'parent_id', a.parent_id,
						'is_annex', a.is_annex, 
						'c_num', a.c_num,
						'nm_usage_rights', a.nm_usage_rights,
                        'group', a.group,
                        'group_name', a.group_name,
						'c_date', to_char(a.c_date, 'DD.MM.YYYY'),
						'start_date', to_char(a.start_date, 'YYYY-MM-DD HH24:MI:SS'),
						'active', a.active,
						'comment', a.comment ,
						'sv_num', a.sv_num,
						'sv_date', to_char(a.sv_date, 'DD.MM.YYYY'),
						'osz_num', a.osz_num,
						'osz_date', to_char(a.osz_date, 'DD.MM.YYYY'),
						'payday', a.payday,
						'due_date', to_char(a.due_date, 'YYYY-MM-DD HH24:MI:SS'),
						'na_num', a.na_num,
						'tom', a.tom,
						'delo', a.delo,
						'court', a.court,
						'renta', a.renta,
						'overall_renta', a.overall_renta,
						'is_closed_for_editing', a.is_closed_for_editing,
						'farming_id', a.farming_id,
						'from_sublease', a.from_sublease,
                        'payment', a.payment,
                        'is_declaration_subleased', a.is_declaration_subleased
					) ORDER BY a.id DESC
				)  filter (where a.id is not null) as annexes_agg,
				c.id,
				c.ao_c_num,
				c.parent_id,
				c.is_annex,
				c.c_num,
				c.nm_usage_rights,
				c.group,
                c.group_name,
				c.c_date,
				c.start_date,
				c.active,
				c.comment ,
				c.sv_num,
				c.sv_date,
				c.osz_num,
				c.osz_date,
				c.payday,
				c.due_date,
				c.na_num,
				c.tom,
				c.delo,
				c.court,
				c.renta,
				c.overall_renta,
				c.is_closed_for_editing,
				c.farming_id,
				c.from_sublease,
                c.payment,
                c.is_declaration_subleased
			from 
				contracts as c
			left
				join annexes as a on a.parent_id = c.id
			group by
				c.id,
				c.ao_c_num,
				c.c_num,
				c.is_annex,
				c.nm_usage_rights,
				c.group,
                c.group_name,
				c.c_date,
				c.start_date,
				c.active,
				c.comment,
				c.sv_num,
				c.sv_date,
				c.osz_num,
				c.osz_date,
				c.payday,
				c.due_date,
				c.na_num,
				c.tom,
				c.delo,
				c.court,
				c.renta,
				c.overall_renta,
				c.is_closed_for_editing,
				c.farming_id,
				c.parent_id,
				c.from_sublease,
                c.payment,
                c.is_declaration_subleased
			order by c.id desc		
			)";

        $sql .= ", annexes_with_contracts as (
				select 
					jsonb_agg(
						jsonb_build_object(
							'id', a.id,
							'ao_c_num', a.ao_c_num,
							'parent_id', a.parent_id,
							'is_annex', a.is_annex, 
							'c_num', a.c_num,
							'nm_usage_rights', a.nm_usage_rights,
                            'group', a.group,
                            'group_name', a.group_name,
							'c_date', to_char(a.c_date, 'DD.MM.YYYY'),
							'start_date', to_char(a.start_date, 'YYYY-MM-DD HH24:MI:SS'),
							'active', a.active,
							'comment', a.comment ,
							'sv_num', a.sv_num,
							'sv_date', to_char(a.sv_date, 'DD.MM.YYYY'),
							'osz_num', a.osz_num,
							'osz_date', to_char(a.osz_date, 'DD.MM.YYYY'),
							'payday', a.payday,
							'due_date', to_char(a.due_date, 'YYYY-MM-DD HH24:MI:SS'),
							'na_num', a.na_num,
							'tom', a.tom,
							'delo', a.delo,
							'court', a.court,
							'renta', a.renta,
							'overall_renta', a.overall_renta,
							'is_closed_for_editing', a.is_closed_for_editing,
							'farming_id', a.farming_id,
							'from_sublease', a.from_sublease,
                            'payment', a.payment,
                            'is_declaration_subleased', a.is_declaration_subleased
						) ORDER BY a.id DESC 
					) filter (where a.id is not null) as children,
					c.id,
					c.ao_c_num,
					c.parent_id,
					c.is_annex,
					c.c_num,
					c.nm_usage_rights,
					c.group,
                    con.group_name,
					c.c_date,
					c.start_date,
					c.active,
					c.comment,
					c.sv_num,
					c.sv_date,
					c.osz_num,
					c.osz_date,
					c.payday,
					c.due_date,
					c.na_num,
					c.tom,
					c.delo,
					c.court,
					c.renta,
					c.overall_renta,
					c.is_closed_for_editing,
					c.farming_id,
					c.from_sublease,
                    a.payment,
                    c.is_declaration_subleased
				from annexes as a
				left join su_contracts as c on c.id = a.parent_id
				left join contracts as con on c.id = con.id
				group by 
					c.id,
					c.c_num,
					c.is_annex,
					c.nm_usage_rights,
					c.group,
                    con.group_name,
					c.c_date,
					c.start_date,
					c.active,
					c.comment ,
					c.sv_num,
					c.sv_date,
					c.osz_num,
					c.osz_date,
					c.payday,
					c.due_date,
					c.na_num,
					c.tom,
					c.delo,
					c.court,
					c.renta,
					c.overall_renta,
					c.is_closed_for_editing,
					c.farming_id,
					c.parent_id,
					c.from_sublease,
                    a.payment,
                    c.is_declaration_subleased
				order by c.id desc	
			)";

        $sql .= ', results as (
                    select * from annexes_with_contracts
                    union
                    select * from contracts_with_annnexes
				) select *, count(*) OVER() as full_count from results 
			';

        if ($options['order'] && $options['sort'] && false == $counter) {
            if ('c.c_num' == $options['sort']) {
                $sql .= ' ORDER BY c_num  COLLATE "alpha_numeric_bg" ' . $options['order'];
            } else {
                $sql .= ' ORDER BY ' . $options['sort'] . ' ' . $options['order'];
            }
        }

        $limit = filter_var($options['limit'], FILTER_VALIDATE_INT);
        $offset = filter_var($options['offset'], FILTER_VALIDATE_INT);

        if (false !== $limit && false !== $offset && false === $counter) {
            $sql .= $returnOnlySQL
                ? " LIMIT {$limit} OFFSET {$offset}"
                : ' LIMIT :limit OFFSET :offset';
        }

        if ($returnOnlySQL) {
            return $sql;
        }

        $cmd = $this->DbModule->createCommand($sql);

        if (isset($options['limit'], $options['offset']) && !$counter) {
            $cmd->bindParameter(':limit', $options['limit'], PDO::PARAM_INT);
            $cmd->bindParameter(':offset', $options['offset'], PDO::PARAM_INT);
        }

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        if ($options['whereOr']) {
            $this->createWhereBinds($cmd, $options['whereOr']);
        }

        if ($options['orWhere']) {
            foreach ($options['orWhere'] as $orWhere) {
                $this->createWhereBinds($cmd, $orWhere);
            }
        }

        return $cmd->query()->readAll();
    }

    public function getContractsFlat($options, $counter, $returnOnlySQL)
    {
        if ($counter) {
            $return = 'COUNT(DISTINCT(c.id))';
        } elseif ($options['return']) {
            $return = implode(', ', $options['return']);
        } else {
            $return = '*';
        }
        $sql = ' WITH all_contracts as (';
        $sql .= "SELECT {$return}, count(*) OVER() as full_count FROM {$this->tableContracts} c";
        $sql .= " LEFT JOIN {$this->contractsRentsRelTable} cr ON (cr.contract_id = c.id)";
        $sql .= " LEFT JOIN {$this->contractsPlotsRelTable} pc ON (pc.contract_id = c.id)";
        $sql .= " LEFT JOIN {$this->tablePayments} p ON (p.contract_id = c.id)";
        $sql .= " LEFT JOIN {$this->plotsOwnersRelTable} po ON (po.pc_rel_id = pc.id)";
        $sql .= " LEFT JOIN {$this->tableOwners} o ON (o.id = po.owner_id)";
        $sql .= " LEFT JOIN {$this->tableOwnersReps} o_r ON (o_r.id = po.rep_id)";
        $sql .= " LEFT JOIN {$this->plotsFarmingRelTable} f_r ON (f_r.pc_rel_id = pc.id)";
        $sql .= " LEFT JOIN {$this->tableKVS} kvs ON(kvs.gid = pc.plot_id)";
        $sql .= " LEFT JOIN {$this->tableContracts} a ON (a.parent_id = c.id)";
        $sql .= ' LEFT JOIN su_contracts_rents_types scrt ON (scrt.contract_id = c.id)';
        $sql .= ' WHERE true ';

        if ($options['whereCustom']) {
            $sql .= $options['whereCustom'];
        }

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }
        if ($options['whereOr']) {
            $sql .= ' AND( false';
            $sql = $this->createWhereSQL($sql, $options['whereOr'], $returnOnlySQL, 'OR');
            $sql .= ')';
        }
        if ($options['orWhere']) {
            foreach ($options['orWhere'] as $orWhere) {
                $sql .= ' AND( false';
                $sql = $this->createWhereSQL($sql, $orWhere, $returnOnlySQL, 'OR');
                $sql .= ')';
            }
        }
        if ($options['annex_ids']) {
            $sql .= " OR c.id IN ({$options['annex_ids']})";
        }

        if ($options['group'] && false === $counter) {
            $sql .= ' GROUP BY ' . $options['group'];
        }

        if ($options['order'] && $options['sort'] && false == $counter) {
            $sql .= ' ORDER BY ' . $options['sort'] . ' ' . $options['order'] . ' NULLS LAST';
        }

        $limit = filter_var($options['limit'], FILTER_VALIDATE_INT);
        $offset = filter_var($options['offset'], FILTER_VALIDATE_INT);

        if (false !== $limit && false !== $offset && false === $counter) {
            $sql .= $returnOnlySQL
                ? " LIMIT {$limit} OFFSET {$offset}"
                : ' LIMIT :limit OFFSET :offset';
        }

        $sql .= ')';

        $sql .= 'SELECT 
                *
                FROM all_contracts  
        ';

        $sql .= ' UNION ';
        $sql .= "SELECT 
                {$return}, all_contracts.full_count
                FROM all_contracts
                INNER JOIN {$this->tableContracts} c on c.id = all_contracts.parent_id
                LEFT JOIN {$this->contractsRentsRelTable} cr ON (cr.contract_id = c.id) 
                LEFT JOIN {$this->contractsPlotsRelTable} pc ON (pc.contract_id = c.id)
                LEFT JOIN {$this->tablePayments} p ON (p.contract_id = c.id)
                GROUP BY {$options['group']}, full_count
        ";

        if ($options['order'] && $options['sort'] && false == $counter) {
            if ('c_num' == $options['sort']) {
                $sql .= ' ORDER BY c_num ' . $options['order'] . ' NULLS LAST';
            } else {
                $sql .= ' ORDER BY ' . $options['sort'] . ' ' . $options['order'] . ' NULLS LAST';
            }
        }

        if ($returnOnlySQL) {
            return $sql;
        }

        $cmd = $this->DbModule->createCommand($sql);

        if (isset($options['limit'], $options['offset']) && !$counter) {
            $cmd->bindParameter(':limit', $options['limit'], PDO::PARAM_INT);
            $cmd->bindParameter(':offset', $options['offset'], PDO::PARAM_INT);
        }

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }
        if ($options['whereOr']) {
            $this->createWhereBinds($cmd, $options['whereOr']);
        }

        if ($options['orWhere']) {
            foreach ($options['orWhere'] as $orWhere) {
                $this->createWhereBinds($cmd, $orWhere);
            }
        }

        if (!$counter) {
            // die(print_r($sql));
        }

        return $cmd->query()->readAll();
    }

    public function getFullContractDataByFilter($options, $counter, $returnOnlySQL)
    {
        if ($counter) {
            $return = 'COUNT(DISTINCT(c.id))';
        } elseif ($options['return']) {
            $return = implode(', ', $options['return']);
        } else {
            $return = '*';
        }

        $sql = "SELECT {$return} FROM {$this->tableContracts} c";
        $sql .= " LEFT JOIN {$this->contractsRentsRelTable} cr ON (cr.contract_id = c.id)";
        $sql .= " LEFT JOIN {$this->contractsPlotsRelTable} pc ON (pc.contract_id = c.id)";
        $sql .= " LEFT JOIN {$this->tablePayments} p ON (p.contract_id = c.id)";
        $sql .= " LEFT JOIN {$this->plotsOwnersRelTable} po ON (po.pc_rel_id = pc.id)";
        $sql .= " LEFT JOIN {$this->tableOwners} o ON (o.id = po.owner_id)";
        $sql .= " LEFT JOIN {$this->tableOwnersReps} o_r ON (o_r.id = po.rep_id)";
        $sql .= " LEFT JOIN {$this->plotsFarmingRelTable} f_r ON (f_r.pc_rel_id = pc.id)";
        $sql .= " LEFT JOIN {$this->tableKVS} kvs ON(kvs.gid = pc.plot_id)";
        $sql .= " LEFT JOIN {$this->tableContracts} a ON (a.parent_id = c.id)";
        $sql .= ' LEFT JOIN su_contracts_rents_types scrt ON (scrt.contract_id = c.id)';

        $sql .= ' WHERE true ';

        if ($options['whereCustom']) {
            $sql .= $options['whereCustom'];
        }

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }
        if ($options['whereOr']) {
            $sql .= ' AND( false';
            $sql = $this->createWhereSQL($sql, $options['whereOr'], $returnOnlySQL, 'OR');
            $sql .= ')';
        }
        if ($options['orWhere']) {
            foreach ($options['orWhere'] as $orWhere) {
                $sql .= ' AND( false';
                $sql = $this->createWhereSQL($sql, $orWhere, $returnOnlySQL, 'OR');
                $sql .= ')';
            }
        }
        if ($options['annex_ids']) {
            $sql .= " OR c.id IN ({$options['annex_ids']})";
        }

        if ($options['group'] && false === $counter) {
            $sql .= ' GROUP BY ' . $options['group'];
        }

        if ($options['having']) {
            $sql .= ' HAVING ' . $options['having'];
        }

        if ($options['order'] && $options['sort'] && false === $counter) {
            $sql .= ' ORDER BY ' . $options['sort'] . ' ' . $options['order'];
        }

        $limit = filter_var($options['limit'], FILTER_VALIDATE_INT);
        $offset = filter_var($options['offset'], FILTER_VALIDATE_INT);

        if (false !== $limit && false !== $offset && false === $counter) {
            $sql .= $returnOnlySQL
                ? " LIMIT {$limit} OFFSET {$offset}"
                : ' LIMIT :limit OFFSET :offset';
        }

        if ($returnOnlySQL) {
            return $sql;
        }

        $cmd = $this->DbModule->createCommand($sql);

        if (isset($options['limit'], $options['offset']) && !$counter) {
            $cmd->bindParameter(':limit', $options['limit'], PDO::PARAM_INT);
            $cmd->bindParameter(':offset', $options['offset'], PDO::PARAM_INT);
        }

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }
        if ($options['whereOr']) {
            $this->createWhereBinds($cmd, $options['whereOr']);
        }

        if ($options['orWhere']) {
            foreach ($options['orWhere'] as $orWhere) {
                $this->createWhereBinds($cmd, $orWhere);
            }
        }

        return $cmd->query()->readAll();
    }

    public function getContractPlotData($options, $counter, $returnOnlySQL)
    {
        if (!$options['return']) {
            $options['return'] = ['c.*', 'kvs.*', 'pc.*'];
        }

        $return = $this->createReturnVariable($options['return'], $counter);

        $sql = "SELECT {$return} FROM " . $this->tableContracts . ' c '
                . "LEFT JOIN {$this->contractsPlotsRelTable} pc ON (pc.contract_id = c.id) "
                . "LEFT JOIN {$this->tableKVS} kvs ON(kvs.gid = pc.plot_id) ";

        if ($options['joins'] && is_array($options['joins'])) {
            foreach ($options['joins'] as $join) {
                $sql .= ' ' . $join . ' ';
            }
        }

        if ($options['with_active_annex']) {
            $annexDates = '';
            if (!empty($options['start_date'])) {
                $annexDates .= " AND a.start_date <= '" . $options['start_date'] . "'";
            }
            if (!empty($options['due_date'])) {
                $annexDates .= " AND a.due_date >= '" . $options['due_date'] . "'";
            }
            $sql = "SELECT {$return} FROM " . $this->tableContracts . ' c '
                . "LEFT JOIN {$this->tableContracts} a ON (a.parent_id = c.id AND a.active = true " . $annexDates . ') '
                . "INNER JOIN {$this->contractsPlotsRelTable} pc ON(pc.contract_id = (CASE WHEN a.id IS NULL THEN c.id ELSE a.id END)) "
                . "LEFT JOIN {$this->tableKVS} kvs ON(kvs.gid = pc.plot_id) ";
        }
        $sql .= 'WHERE true';

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }

        if ($options['union_all_sold_contracts']) {
            $unionReturn = $this->createReturnVariable($options['union_all_sold_contracts']['return'], $counter);

            $sql .= " UNION ALL SELECT {$unionReturn} FROM {$this->tableSalesContracts} c ";
            $sql .= " INNER JOIN {$this->salesContractsPlotsRelTable} scpr ON(scpr.sales_contract_id = c.id) ";
            $sql .= " LEFT JOIN {$this->tableKVS} kvs ON(kvs.gid = scpr.plot_id) ";
            $sql .= ' WHERE true ';

            $sql = $this->createWhereSQL($sql, $options['union_all_sold_contracts']['where'], $returnOnlySQL);
        }

        if ($options['group'] && false === $counter) {
            $sql .= ' GROUP BY ' . $options['group'];
        }

        if ($options && false == $counter) {
            if ($options['sort']) {
                $sql .= ' ORDER BY ' . $options['sort'] . ' ' . $options['order'];
            }

            $limit = filter_var($options['limit'], FILTER_VALIDATE_INT);
            $offset = filter_var($options['offset'], FILTER_VALIDATE_INT);

            if (false !== $limit && false !== $offset) {
                $sql .= $returnOnlySQL
                    ? " LIMIT {$limit} OFFSET {$offset}"
                    : ' LIMIT :limit OFFSET :offset';
            }
        }

        if ($returnOnlySQL) {
            return $sql;
        }

        $cmd = $this->DbModule->createCommand($sql);

        if (isset($options['limit'], $options['offset']) && !$counter) {
            $cmd->bindParameter(':limit', $options['limit'], PDO::PARAM_INT);
            $cmd->bindParameter(':offset', $options['offset'], PDO::PARAM_INT);
        }

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        if ($options['with_active_annex']) {
            $cmd->bindValue(':start_date', $options['where']['start_date']['value']);
            $cmd->bindValue(':due_date', $options['where']['due_date']['value']);
        }

        return $cmd->query()->readAll();
    }

    public function getContractRentsData($options, $counter, $returnOnlySQL)
    {
        if (!$options['return']) {
            $options['return'] = ['cr.*'];
        }

        $return = $this->createReturnVariable($options['return'], $counter);

        $sql = "SELECT {$return} FROM " . $this->contractsRentsRelTable . ' cr '
            . 'WHERE true';

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }

        if ($options && false == $counter) {
            if ($options['sort']) {
                $sql .= ' ORDER BY ' . $options['sort'] . ' ' . $options['order'];
            }

            $limit = filter_var($options['limit'], FILTER_VALIDATE_INT);
            $offset = filter_var($options['offset'], FILTER_VALIDATE_INT);

            if (false !== $limit && false !== $offset) {
                $sql .= $returnOnlySQL
                    ? " LIMIT {$limit} OFFSET {$offset}"
                    : ' LIMIT :limit OFFSET :offset';
            }
        }

        if ($returnOnlySQL) {
            return $sql;
        }

        $cmd = $this->DbModule->createCommand($sql);

        if (isset($options['limit'], $options['offset']) && !$counter) {
            $cmd->bindParameter(':limit', $options['limit'], PDO::PARAM_INT);
            $cmd->bindParameter(':offset', $options['offset'], PDO::PARAM_INT);
        }

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        return $cmd->query()->readAll();
    }

    public function getPlotOwnerRelData($options, $counter, $returnOnlySQL)
    {
        if (!$options['return']) {
            $options['return'] = ['*'];
        }

        $return = $this->createReturnVariable($options['return'], $counter);

        $sql = "SELECT {$return} FROM {$this->contractsPlotsRelTable} pc "
                . "JOIN {$this->plotsOwnersRelTable} po ON (po.pc_rel_id = pc.id) "
                . 'WHERE true';

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }

        if ($returnOnlySQL) {
            return $sql;
        }

        $cmd = $this->DbModule->createCommand($sql);

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        return $cmd->query()->readAll();
    }

    public function getPlotFarmingRelData($options, $counter, $returnOnlySQL)
    {
        if (!$options['return']) {
            $options['return'] = ['*'];
        }

        $return = $this->createReturnVariable($options['return'], $counter);

        $sql = "SELECT {$return} FROM {$this->contractsPlotsRelTable} pc "
                . "JOIN {$this->plotsFarmingRelTable} pf ON (pf.pc_rel_id = pc.id) "
                . 'WHERE true';

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }

        if ($returnOnlySQL) {
            return $sql;
        }

        $cmd = $this->DbModule->createCommand($sql);

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        return $cmd->query()->readAll();
    }

    public function getEditedPlotsBeforeContractActiveDate($options, $counter)
    {
        $return = $this->createReturnVariable($options['return'], $counter);

        $sql = "SELECT {$return} FROM " . $this->tableKVS . ' kvs	WHERE true';

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], false);
        }

        if ($options && false == $counter) {
            if ($options['sort']) {
                $sql .= ' ORDER BY ' . $options['sort'] . ' ' . $options['order'];
            }
            if (isset($options['limit'], $options['offset'])) {
                $sql .= ' LIMIT :limit OFFSET :offset';
            }
        }

        $cmd = $this->DbModule->createCommand($sql);

        if (isset($options['limit'], $options['offset']) && !$counter) {
            $cmd->bindParameter(':limit', $options['limit'], PDO::PARAM_INT);
            $cmd->bindParameter(':offset', $options['offset'], PDO::PARAM_INT);
        }

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        return $cmd->query()->readAll();
    }

    /**
     * Set Response Data Contracts.
     *
     * @param int $contractType
     * @param bool $hasContractsOwnWriteRights
     *
     * @return array
     */
    public function setResponseDataContracts($contractType, $hasContractsOwnWriteRights)
    {
        return $this->getResponseDataContracts($contractType, $hasContractsOwnWriteRights);
    }

    /**
     * Return Response Data Contracts Array.
     *
     * @param int $contractType
     * @param bool $hasContractsOwnWriteRights
     *
     * @return array
     */
    public function getResponseDataContracts($contractType, $hasContractsOwnWriteRights)
    {
        return [
            'hasContractsOwnWriteRights' => $hasContractsOwnWriteRights,
            'contractType' => $contractType,
        ];
    }

    /**
     * Get ContractType.
     *
     * @param int $contractId
     *
     * @return int
     */
    public function getContractType($contractId)
    {
        $options = [
            'tablename' => $this->tableContracts,
            'where' => [
                'id' => ['column' => 'id', 'compare' => '=', 'value' => $contractId],
            ],
        ];
        $result = $this->getItemsByParams($options, false, false);

        // return ContractType
        return $result[0]['nm_usage_rights'];
    }

    /**
     * Update Contracts In Ids.
     *
     * @param array $contractIds
     * @param string $dueDate (e.g. 2015-09-30)
     */
    public function updateContractsInIds($contractIds, $dueDate)
    {
        if (is_array($contractIds) and $cnt = count($contractIds)) {
            $sql = "UPDATE {$this->tableContracts} SET due_date = :due_date WHERE id IN (";

            for ($i = 0; $i < $cnt; $i++) {
                $sql .= " :k{$i} ";
                if ($i < $cnt - 1) {
                    $sql .= ',';
                }
            }
            $sql .= ')';

            $cmd = $this->DbModule->createCommand($sql);

            $cmd->bindParameter(':due_date', $dueDate);

            for ($i = 0; $i < $cnt; $i++) {
                $cmd->bindParameter(":k{$i}", $contractIds[$i], PDO::PARAM_INT);
            }

            return $cmd->execute();
        }
    }

    public function getBuyersSalesContractsRelation($params, $counter, $returnOnlySQL)
    {
        $return = $this->createReturnVariable($params['return'], $counter);

        $sql = "SELECT {$return} FROM {$this->tableSalesContractsBuyersRel} scbr
				INNER JOIN {$this->tableBuyers} b on b.id = scbr.buyer_id
				WHERE true";

        if ($params['where']) {
            $sql = $this->createWhereSQL($sql, $params['where'], $returnOnlySQL);
        }

        if ($params && false == $counter) {
            if ($params['sort']) {
                $sql .= ' ORDER BY ' . $params['sort'] . ' ' . $params['order'];
            }
            if ($params['limit']) {
                $sql .= ' LIMIT ' . $params['limit'] . ' OFFSET ' . $params['offset'];
            }
        }

        if ($returnOnlySQL) {
            return $sql;
        }

        $cmd = $this->DbModule->createCommand($sql);

        if ($params['where']) {
            $this->createWhereBinds($cmd, $params['where']);
        }

        return $cmd->query()->readAll();
    }

    public function getReportSalesContracts($params, $counter, $returnOnlySQL)
    {
        $return = $this->createReturnVariable($params['return'], $counter);

        $sql = "SELECT {$return} FROM {$this->salesContractsPlotsRelTable} scpr
				INNER JOIN {$this->tableSalesContracts} sc on sc.id = scpr.sales_contract_id
				INNER JOIN {$this->contractsPlotsRelTable} cpr on cpr.contract_id = scpr.contract_id AND cpr.plot_id = scpr.plot_id
				INNER JOIN {$this->tableContracts} c on c.id = scpr.contract_id
				INNER JOIN {$this->tableKVS} kvs on kvs.gid = scpr.plot_id
				WHERE true";

        if ($params['where']) {
            $sql = $this->createWhereSQL($sql, $params['where'], $returnOnlySQL);
        }

        if ($params && false == $counter) {
            if ($params['sort']) {
                $sql .= ' ORDER BY ' . $params['sort'] . ' ' . $params['order'];
            }
            if ($params['limit']) {
                $sql .= ' LIMIT ' . $params['limit'] . ' OFFSET ' . $params['offset'];
            }
        }

        if ($returnOnlySQL) {
            return $sql;
        }

        $cmd = $this->DbModule->createCommand($sql);

        if ($params['where']) {
            $this->createWhereBinds($cmd, $params['where']);
        }

        return $cmd->query()->readAll();
    }

    public function hasContractEditedPlots($params, $counter, $returnOnlySQL)
    {
        $return = $this->createReturnVariable($params['return'], $counter);

        $sql = "SELECT {$return} FROM {$this->tableContracts} c
				LEFT JOIN {$this->contractsPlotsRelTable} pc on (c.id = pc.contract_id)
				INNER JOIN {$this->tableKVS} kvs on (kvs.gid = pc.plot_id)
				WHERE true";

        if ($params['where']) {
            $sql = $this->createWhereSQL($sql, $params['where'], $returnOnlySQL);
        }

        if ($params && false == $counter) {
            if ($params['sort']) {
                $sql .= ' ORDER BY ' . $params['sort'] . ' ' . $params['order'];
            }
            if ($params['limit']) {
                $sql .= ' LIMIT ' . $params['limit'] . ' OFFSET ' . $params['offset'];
            }
        }

        if ($returnOnlySQL) {
            return $sql;
        }

        $cmd = $this->DbModule->createCommand($sql);

        if ($params['where']) {
            $this->createWhereBinds($cmd, $params['where']);
        }

        return $cmd->query()->readAll();
    }

    public function getSalesContractsPlotsAndBayers($params, $counter, $returnOnlySQL)
    {
        $return = $this->createReturnVariable($params['return'], $counter, $params['custom_counter']);

        $sql = "SELECT {$return} FROM {$this->tableSalesContracts} sc
				LEFT JOIN {$this->salesContractsPlotsRelTable} scpr on (sc.id = scpr.sales_contract_id)
				LEFT JOIN {$this->tableKVS} kvs on (kvs.gid = scpr.plot_id)
				LEFT JOIN {$this->tableSalesContractsBuyersRel} scbr on (sc.id = scbr.sales_contract_id)
				LEFT JOIN {$this->tableBuyers} b on (b.id = scbr.buyer_id)
				WHERE true";

        if ($params['where']) {
            $sql = $this->createWhereSQL($sql, $params['where'], $returnOnlySQL);
        }

        if ($params && false == $counter) {
            if ($params['group']) {
                $sql .= ' GROUP BY ' . $params['group'];
            }
            if ($params['sort']) {
                $sql .= ' ORDER BY ' . $params['sort'] . ' ' . $params['order'];
            }
            if ($params['limit']) {
                $sql .= ' LIMIT ' . $params['limit'] . ' OFFSET ' . $params['offset'];
            }
        }

        if ($returnOnlySQL) {
            return $sql;
        }

        $cmd = $this->DbModule->createCommand($sql);

        if ($params['where']) {
            $this->createWhereBinds($cmd, $params['where']);
        }

        return $cmd->query()->readAll();
    }

    public function getContractsFilteredPlots($options, $counter, $returnOnlySQL)
    {
        if (true == $counter) {
            $return = 'COUNT(*)';
        } elseif ($options['return']) {
            $return = implode(', ', $options['return']);
        } else {
            $return = '*';
        }

        $sql = "SELECT {$return} FROM " . $this->tableContracts . ' c 
                INNER JOIN su_contracts_plots_rel pc on (c.id = pc.contract_id)
                INNER JOIN layer_kvs kvs on (pc.plot_id = kvs.gid)
                WHERE true';

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }

        if ($options['order'] && $options['sort'] && false == $counter) {
            $sql .= ' ORDER BY ' . $options['sort'] . ' ' . $options['order'];
        }

        $limit = filter_var($options['limit'], FILTER_VALIDATE_INT);
        $offset = filter_var($options['offset'], FILTER_VALIDATE_INT);

        if (false !== $limit && false !== $offset && false === $counter) {
            $sql .= $returnOnlySQL
                ? " LIMIT {$limit} OFFSET {$offset}"
                : ' LIMIT :limit OFFSET :offset';
        }

        if ($returnOnlySQL) {
            return $sql;
        }

        $cmd = $this->DbModule->createCommand($sql);

        if (isset($options['limit'], $options['offset']) && !$counter) {
            $cmd->bindParameter(':limit', $options['limit'], PDO::PARAM_INT);
            $cmd->bindParameter(':offset', $options['offset'], PDO::PARAM_INT);
        }

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        return $cmd->query()->readAll();
    }

    public function isContractFromSublease($contractID)
    {
        $sql = 'SELECT from_sublease FROM ' . $this->tableContracts . ' WHERE id = :contract_id';

        $cmd = $this->DbModule->createCommand($sql);
        $cmd->bindValue(':contract_id', $contractID);

        $results = $cmd->query()->readAll();

        return (bool) ($results[0]['from_sublease'] > 0);
    }

    public function hasSalesContractsRelation(array $params)
    {
        $sql = "
            SELECT
                sc.id as sales_contract_id,
                sc.c_num,
                scpr.id as sales_contract_plot_rel_id
            FROM {$this->tableSalesContracts} sc
            LEFT JOIN {$this->salesContractsPlotsRelTable} scpr on (sc.id = scpr.sales_contract_id)
            WHERE true";

        if ($params['contract_id']) {
            $sql .= ' AND scpr.contract_id = :contract_id';
        }

        if ($params['start_date']) {
            $sql .= ' AND sc.start_date < :start_date';
        }

        if ($params['pc_rel_id']) {
            $sql .= ' AND scpr.pc_rel_id = :pc_rel_id';
        }

        if ($params['farming_id']) {
            $sql .= ' AND sc.farming_id = :farming_id';
        }

        $cmd = $this->DbModule->createCommand($sql);

        if ($params['contract_id']) {
            $cmd->bindValue(':contract_id', $params['contract_id']);
        }

        if ($params['start_date']) {
            $cmd->bindValue(':start_date', $params['start_date']);
        }

        if ($params['pc_rel_id']) {
            $cmd->bindValue(':pc_rel_id', $params['pc_rel_id']);
        }

        if ($params['farming_id']) {
            $cmd->bindValue(':farming_id', $params['farming_id']);
        }

        return $cmd->query()->readAll();
    }

    public function deleteCPRelForCPRel($pc_rel_id)
    {
        $sql = "SELECT sublease_id FROM {$this->tableSubleasesPlotsContractsRel} WHERE pc_rel_id = :pc_rel_id";

        $cmd = $this->DbModule->createCommand($sql);
        $cmd->bindParameter(':pc_rel_id', $pc_rel_id);
        $cmd->execute();
        $sublease = $cmd->query()->readAll();

        $sql = "SELECT plot_id FROM {$this->contractsPlotsRelTable} WHERE id = :pc_rel_id";

        $cmd = $this->DbModule->createCommand($sql);
        $cmd->bindParameter(':pc_rel_id', $pc_rel_id);
        $cmd->execute();
        $plot_id = $cmd->query()->readAll();
        $plot_id = $plot_id[0]['plot_id'];

        for ($i = 0; $i < count($sublease); $i++) {
            $sql = "SELECT id FROM {$this->tableContracts} WHERE from_sublease = :sublease_id";
            $cmd = $this->DbModule->createCommand($sql);
            $cmd->bindParameter(':sublease_id', $sublease[$i]['sublease_id']);
            $cmd->execute();
            $tmp_contract_id = $cmd->query()->readAll();

            $sql = "DELETE FROM {$this->contractsPlotsRelTable} WHERE contract_id = :contract_id AND plot_id = :plot_id";
            $cmd = $this->DbModule->createCommand($sql);
            $cmd->bindParameter(':contract_id', $tmp_contract_id[0]['id']);
            $cmd->bindParameter(':plot_id', $plot_id);
            $cmd->execute();
        }
    }

    public function getSalesContractsPlotsData($params, $counter, $returnOnlySQL)
    {
        $return = $this->createReturnVariable($params['return'], $counter);

        $sql = "SELECT {$return} FROM {$this->salesContractsPlotsRelTable} scpr
				INNER JOIN {$this->tableKVS} kvs on kvs.gid = scpr.plot_id
				WHERE true";

        if ($params['where']) {
            $sql = $this->createWhereSQL($sql, $params['where'], $returnOnlySQL);
        }

        if ($params && false == $counter) {
            if ($params['sort']) {
                $sql .= ' ORDER BY ' . $params['sort'] . ' ' . $params['order'];
            }
            if ($params['limit']) {
                $sql .= ' LIMIT ' . $params['limit'] . ' OFFSET ' . $params['offset'];
            }
        }

        if ($returnOnlySQL) {
            return $sql;
        }

        $cmd = $this->DbModule->createCommand($sql);

        if ($params['where']) {
            $this->createWhereBinds($cmd, $params['where']);
        }

        return $cmd->query()->readAll();
    }

    public function copyContractFileRelations($fromContractId, $toContractId)
    {
        $sqlInsert = 'INSERT INTO ' . $this->contractsFilesRelTable . ' (file_id, contract_id)
                      SELECT file_id, :new_contract_id FROM ' . $this->contractsFilesRelTable . '
                      where contract_id = :from_contract_id';

        $cmd = $this->DbModule->createCommand($sqlInsert);

        $cmd->bindValue(':new_contract_id', $toContractId);
        $cmd->bindParameter(':from_contract_id', $fromContractId);
        $cmd->execute();
    }

    public function deleteContractFileRelation($fileId, $contractId)
    {
        $sqlInsert = 'DELETE FROM ' . $this->contractsFilesRelTable . ' WHERE file_id = :file_id AND contract_id = :contract_id';

        $cmd = $this->DbModule->createCommand($sqlInsert);
        $cmd->bindParameter(':file_id', $fileId);
        $cmd->bindParameter(':contract_id', $contractId);
        $cmd->execute();
    }

    public function getContractFiles($options, $counter, $returnOnlySQL)
    {
        $return = $this->createReturnVariable($options['return'], $counter);

        $sql = "SELECT {$return} FROM {$this->userFilesTable} uf
        		INNER JOIN {$this->contractsFilesRelTable} cf ON(uf.id = cf.file_id) 
        		WHERE true";

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }

        $cmd = $this->DbModule->createCommand($sql);

        if ($returnOnlySQL) {
            return $sql;
        }

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        return $cmd->query()->readAll();
    }

    public function removeRentPerPlot($contractId)
    {
        $sql = 'UPDATE su_contracts_plots_rel SET rent_per_plot = NULL WHERE contract_id = :contract_id';

        $cmd = $this->DbModule->createCommand($sql);
        $cmd->bindParameter(':contract_id', $contractId);
        $cmd->execute();
    }

    public function getContractPlotsData($options, $counter, $returnOnlySQL)
    {
        if (true == $counter) {
            $return = 'COUNT(*)';
        } elseif ($options['return']) {
            $return = implode(', ', $options['return']);
        } else {
            $return = '*';
        }

        $sql = "SELECT {$return} FROM su_contracts_plots_rel WHERE true";

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }

        if ($options['order'] && $options['sort'] && false == $counter) {
            $sql .= ' ORDER BY ' . $options['sort'] . ' ' . $options['order'];
        }

        $limit = filter_var($options['limit'], FILTER_VALIDATE_INT);
        $offset = filter_var($options['offset'], FILTER_VALIDATE_INT);

        if (false !== $limit && false !== $offset && false === $counter) {
            $sql .= $returnOnlySQL
                ? " LIMIT {$limit} OFFSET {$offset}"
                : ' LIMIT :limit OFFSET :offset';
        }

        if ($returnOnlySQL) {
            return $sql;
        }

        $cmd = $this->DbModule->createCommand($sql);

        if (isset($options['limit'], $options['offset']) && !$counter) {
            $cmd->bindParameter(':limit', $options['limit'], PDO::PARAM_INT);
            $cmd->bindParameter(':offset', $options['offset'], PDO::PARAM_INT);
        }

        if ($options['where']) {
            $this->createWhereBinds($cmd, $options['where']);
        }

        return $cmd->query()->readAll();
    }

    /**
     * Get Not Available Contracts Plots.
     *
     * @param string $startDate
     * @param string $dueDate
     * @param array $contractIds
     * @param array $queryOptions
     * @param array $plotIds
     * @param array $excludeContracts
     * @param null|int $contractFarming
     *
     * @return array
     */
    public function getNotAvailableContractsPlots(
        $startDate,
        $dueDate,
        $contractIds = [],
        $queryOptions = [],
        $plotIds = [],
        $excludeContracts = [],
        $contractFarming = null
    ) {
        // In case we want to validate plots in property contracts
        if (empty($dueDate)) {
            $dueDate = '(select due_date + interval \'1 year\' from su_contracts where due_date is not null order by due_date desc limit 1)';
        } else {
            $dueDate = "'{$dueDate}'";
        }

        $periods = '';
        if (!empty($queryOptions['editContract'])) {
            $periods = " AND cd.period_start >= '{$startDate}' AND cd.period_end <= {$dueDate} ";
        }

        $skipMainContractCondition = '';
        $skipMainContractConditionFromDetails = '';
        if (!empty($contractIds)) {
            $contractIdsStr = implode(',', $contractIds);
            $skipMainContractCondition = ' AND contract_id NOT IN (' . $contractIdsStr . ') AND parent_id NOT IN (' . $contractIdsStr . ')';
            $skipMainContractConditionFromDetails = ' AND c.id NOT IN (' . $contractIdsStr . ')';
        }

        $sql = "
			with ekattes AS (
					SELECT
						ekatte_code,
						ekatte_name
					FROM dblink('host=" . DEFAULT_DB_HOST . ' port=' . DEFAULT_DB_PORT . ' user=' . DEFAULT_DB_USERNAME . ' password=' . DEFAULT_DB_PASSWORD . ' dbname=' . DEFAULT_DB_DATABASE . "',
						'SELECT ekatte_code, ekatte_name FROM su_ekatte') AS t(ekatte_code text, ekatte_name text)
			)
		";

        $sql .= "
            , date_range AS (
                SELECT 
                        generate_series(
                            '{$startDate}'::date,
                            {$dueDate}::date,
                        interval '1 year'
                        ) AS period_start
            )
        ";

        $sql .= "
            , periods AS (
                SELECT 
                    period_start,
                    period_start + interval '1 year - 1 day' AS period_end
                FROM date_range
            )
        ";

        $sql .= ', contract_details as (';
        $sql .= "SELECT
                    c.id as contract_id,
                    c.parent_id as parent_id,
                    contracts_plots.plot_id as cp_plot_id,
                    round(contracts_plots.contract_area::numeric - (case when max(ssc.id) is not null then sum(sscpr.contract_area_for_sale) else 0 end)::numeric, 3) as cp_contract_area,
                    CASE WHEN document_area IS NULL THEN St_Area(geom)/1000 ELSE document_area END,
                    gid,
                    c.is_annex,
                    c.c_num,
                    c.start_date,
                    c.due_date,
                    periods.period_start,
	                periods.period_end,
                    kvs.kad_ident,
                    ekatte_name
				FROM periods AS periods
                LEFT JOIN {$this->tableContracts} AS c on c.active = true and c.start_date <= periods.period_start and (c.due_date >= periods.period_end or c.due_date is null)
                INNER JOIN {$this->contractsPlotsRelTable} AS contracts_plots ON contracts_plots.contract_id = c.id and contracts_plots.annex_action = 'added'	
				LEFT JOIN {$this->tableContracts} AS an on (an.parent_id = c.id AND an.active = true and an.start_date <= periods.period_start and an.due_date >= periods.period_end)	
				LEFT JOIN layer_kvs kvs ON (contracts_plots.plot_id = kvs.gid)
				LEFT JOIN ekattes ON ekate = ekatte_code
                LEFT JOIN su_sales_contracts_plots_rel sscpr ON sscpr.pc_rel_id = contracts_plots.id
	            LEFT JOIN su_sales_contracts ssc ON ssc.id = sscpr.sales_contract_id and ssc.start_date <= periods.period_end
        		WHERE
					(c.start_date <= periods.period_end AND (c.due_date is null OR c.due_date >= periods.period_start))
                    and an.id is null	
                    and c.active = true
                    and c.from_sublease is null
                    {$skipMainContractConditionFromDetails}
				";

        if ($plotIds) {
            $sql .= ' AND contracts_plots.plot_id IN (' . implode(',', $plotIds) . ')';
        }
        if ($contractFarming) {
            $sql .= ' AND c.farming_id = ' . $contractFarming;
        }
        if ($excludeContracts) {
            $sql .= ' AND c.id NOT IN (' . implode(',', $excludeContracts) . ')';
        }

        $sql .= ' GROUP BY 
                        gid,
                        ekattes.ekatte_name,
                        kvs.ekate,
                        c.id,
                        c.parent_id,
                        contracts_plots.plot_id,
                        contracts_plots.contract_area,
                        periods.period_start,
                        periods.period_end
                ';
        $sql .= ')';

        $sql .= ', overlapping_areas AS (
                    SELECT
                        cd.cp_plot_id,
                        cd.period_start,
                        cd.period_end,
                        SUM(cd.cp_contract_area) AS overlapping_area
                    FROM
                        contract_details cd
                    WHERE true
                        ' . $periods . '
                    GROUP BY
                        cd.cp_plot_id,
                        cd.period_start,
                        cd.period_end
                ), max_overlapping_areas AS (
                    SELECT
                        cp_plot_id,
                        period_start,
                        period_end,
                        MAX(overlapping_area) AS max_overlapping_area
                    FROM
                        overlapping_areas
                    GROUP BY
                        cp_plot_id,
                        period_start,
                        period_end
                ) ';

        $sql .= " SELECT 
                    json_agg(distinct contract_details.contract_id) as c_ids,
                    json_agg(distinct contract_details.parent_id) as parent_ids,
                    json_agg(distinct contract_details.cp_plot_id) as plot_ids,
                    ROUND((contract_details.document_area)::numeric, 3) as document_area,
                    ROUND(MAX(max_overlapping_areas.max_overlapping_area)::numeric, 3) as max_overlaping_area,
                    ROUND((
                        ROUND((contract_details.document_area)::numeric, 3) - ROUND(MAX(max_overlapping_areas.max_overlapping_area)::numeric, 3)
                    )::numeric, 3) AS available_plot_area,
                    json_agg(
                        DISTINCT json_build_object(
                            'gid', contract_details.gid,
                            'contract_id', contract_details.contract_id,
                            'is_annex', contract_details.is_annex,
                            'plot_id', contract_details.cp_plot_id,
                            'contract_c_num', contract_details.c_num,
                            'contract_area', contract_details.cp_contract_area,
                            'kad_ident', contract_details.kad_ident,
                            'ekatte_name', contract_details.ekatte_name,
                            'contract_start_date', contract_details.start_date,
                            'contract_due_date', contract_details.due_date
                        )::jsonb
                    ) as contracts_info
                FROM contract_details
                LEFT JOIN max_overlapping_areas ON contract_details.cp_plot_id = max_overlapping_areas.cp_plot_id
                WHERE 
                    contract_details.cp_contract_area > 0
                    {$skipMainContractCondition}
                GROUP BY 
                    contract_details.document_area, 
                    contract_details.cp_plot_id  
        ";

        $cmd = $this->DbModule->createCommand($sql);

        return $cmd->query()->readAll();
    }

    public function getSoldContractsPlots($contractStartDate, $contractIds, $plotId)
    {
        $sqlSaleContracts = "
            select 
                ssc.id as sale_contract_id,
                ssc.c_num as sale_contract_c_num,
                to_char(ssc.start_date, 'DD.MM.YYYY') as start_date,
                sscpr.pc_rel_id,
                ssc.farming_id,
                sscpr.contract_area,
                ROUND(sscpr.contract_area_for_sale::numeric,3) as contract_area_for_sale,
                sum(sscpr.contract_area_for_sale)  over() as total_contract_area_for_sale 
            from su_sales_contracts_plots_rel sscpr 
            left join su_sales_contracts ssc on ssc.id = sscpr.sales_contract_id 
            where 
                ssc.active = true
                and ssc.start_date >= :contract_start_date	
                and sscpr.contract_id = :contract_id
                and sscpr.plot_id = :plot_id
        ";

        $cmd = $this->DbModule->createCommand($sqlSaleContracts);
        $cmd->bindParameter(':contract_start_date', $contractStartDate);
        $cmd->bindParameter(':contract_id', $contractIds);
        $cmd->bindParameter(':plot_id', $plotId);

        return $cmd->query()->readAll();
    }

    public function getContractsToProcess(array $params): array
    {
        $contractIds = implode("','", $params['contract_ids']);

        $sqlContracts = "
            SELECT
                *,
                c_num as contract_number,
                c_date as contract_date,
                nm_usage_rights as contract_type,
                start_date as contract_start_date,
                due_date as contract_due_date,
                farming_id as contract_farming
            FROM su_contracts as c
            WHERE
                c.id IN ('{$contractIds}')
                AND
                c.is_annex = false
                AND
                is_sublease = false
                AND
                active = true
        ";

        if (!empty($params['c_types_array'])) {
            $cTypes = implode("','", $params['c_types_array']);
            $sqlContracts .= " AND nm_usage_rights IN ('{$cTypes}')";
        }

        $cmdContracts = $this->DbModule->createCommand($sqlContracts);
        $contracts = $cmdContracts->query()->readAll();

        $sqlAnnexes = "
            SELECT
                *,
                c_num as contract_number,
                c_date as contract_date,
                nm_usage_rights as contract_type,
                start_date as contract_start_date,
                due_date as contract_due_date,
                farming_id as contract_farming
            FROM su_contracts as c
            WHERE
                c.parent_id IN ('{$contractIds}')
                AND
                c.is_annex = true
                AND
                active = true
        ";

        $cmdAnnexes = $this->DbModule->createCommand($sqlAnnexes);
        $annexes = $cmdAnnexes->query()->readAll();

        // Compare contract and annex dates
        foreach ($contracts as $key => $contract) {
            $biggestAnnexDueDate = $contract['due_date'];
            foreach ($annexes as $annex) {
                if ($annex['parent_id'] == $contract['id'] && strtotime($annex['due_date']) >= strtotime($contract['due_date']) && strtotime($annex['due_date']) >= strtotime($biggestAnnexDueDate)) {
                    $biggestAnnexDueDate = $annex['due_date'];
                    $contracts[$key] = $annex;
                }
            }
        }

        return $contracts;
    }

    public function validatePlotAreasInContracts($plotIds, $starDate, $dueDate, $parentId): array
    {
        $plotIdsString = implode(',', $plotIds);

        $sql = "SELECT 
                    kvs.document_area - sum(cpr.contract_area) as available_area,
                    json_agg(
                        json_build_object(
                            'plot_id', cpr.plot_id,
                            'contract_id', cpr.contract_id,
                            'contract_area', cpr.contract_area,
                            'annex_action', cpr.annex_action,
                            'c_num', contracts.c_num,
                            'start_date', contracts.start_date,
                            'due_date', contracts.due_date,
                            'is_annex', contracts.is_annex,
                            'parent_id', contracts.parent_id,
                            'kad_ident', kvs.kad_ident,
                            'ekatte_name', se.ekatte_name
                        )
                    ) AS contracts_info
                FROM su_contracts_plots_rel AS cpr
                INNER JOIN layer_kvs AS kvs ON (kvs.gid = cpr.plot_id)
                INNER JOIN su_ekatte se on se.ekatte_code = kvs.ekate
                INNER JOIN su_contracts AS contracts ON (contracts.id = cpr.contract_id) AND  contracts.start_date <= :due_date AND contracts.due_date >= :start_date
                WHERE 
                    cpr.plot_id IN ( {$plotIdsString} )
                    AND
                    cpr.annex_action = 'added'
                    AND
                    contracts.active = true
                    AND
                    contracts.is_sublease = false
                    AND
                    contracts.parent_id != :current_annex_parent_id -- Exclude all annexes with a parent_id (we cannot have annexes with intersecting dates, but exclude the currently edited one)
                    AND NOT EXISTS (
                        SELECT 1 
                        FROM su_contracts annexes 
                        WHERE 
                            annexes.parent_id = contracts.id
                            AND 
                            annexes.parent_id != :parent_id
                    )
                GROUP BY kvs.gid        
                ";

        $cmd = $this->DbModule->createCommand($sql);
        $cmd->bindParameter(':due_date', $dueDate);
        $cmd->bindParameter(':start_date', $starDate);
        $cmd->bindParameter(':parent_id', $parentId);
        $cmd->bindParameter(':current_annex_parent_id', $parentId);

        return $cmd->query()->readAll();
    }

    private function genContractsSql($options, $returnOnlySQL, $counter, $ignoreLimit = false, $additionalGroupBy = '')
    {
        $sql = '';
        if ($options['whereCustom']) {
            $sql .= $options['whereCustom'];
        }

        if ($options['where']) {
            $sql = $this->createWhereSQL($sql, $options['where'], $returnOnlySQL);
        }
        if ($options['whereOr']) {
            $sql .= ' AND( false';
            $sql = $this->createWhereSQL($sql, $options['whereOr'], $returnOnlySQL, 'OR');
            $sql .= ')';
        }
        if ($options['orWhere']) {
            foreach ($options['orWhere'] as $orWhere) {
                $sql .= ' AND( false';
                $sql = $this->createWhereSQL($sql, $orWhere, $returnOnlySQL, 'OR');
                $sql .= ')';
            }
        }
        if ($options['annex_ids']) {
            $sql .= " OR c.id IN ({$options['annex_ids']})";
        }

        if ($options['group'] && false === $counter) {
            $sql .= ' GROUP BY ' . $options['group'];

            if (strlen($additionalGroupBy)) {
                $sql .= ", {$additionalGroupBy}";
            }
        }

        if ($options['order'] && $options['sort'] && false === $counter) {
            if ('c.c_num' == $options['sort']) {
                $sql .= ' ORDER BY c.c_num  COLLATE "alpha_numeric_bg" ' . $options['order'];
            } else {
                $sql .= ' ORDER BY ' . $options['sort'] . ' ' . $options['order'];
            }
        }

        $limit = filter_var($options['limit'], FILTER_VALIDATE_INT);
        $offset = filter_var($options['offset'], FILTER_VALIDATE_INT);

        if (false === $ignoreLimit && false === $counter && false !== $limit && false !== $offset) {
            $sql .= $returnOnlySQL
                ? " LIMIT {$limit} OFFSET {$offset}"
                : ' LIMIT :limit OFFSET :offset';
        }

        return $sql;
    }
}
