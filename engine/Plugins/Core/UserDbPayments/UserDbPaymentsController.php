<?php

namespace TF\Engine\Plugins\Core\UserDbPayments;

use TF\Engine\Kernel\MTRpcException;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\UserDbOwners\UserDbOwnersController;
use TF\Engine\Plugins\Core\UserDbPlotCategoriesType\UserDbPlotCategoriesTypeController;
use TF\Engine\Plugins\Core\Users\UsersController;

class UserDbPaymentsController extends UserDbController
{
    public const CONTRACTS_HAS_PAYMENTS_EXCEPTION = -33658;
    /**
     * @var UserDbPaymentsModel
     */
    public $DbHandler;

    public function __construct($database)
    {
        $this->DbHandler = new UserDbPaymentsModel($database);
        $this->Database = $database;
    }

    public function getPaymentsForContracts($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getPaymentsForContracts($options, $counter, $returnOnlySQL);
    }

    public function getPaymentsForOwners($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getPaymentsForOwners($options, $counter, $returnOnlySQL);
    }

    public function getPersonalUseForOwners($params, $counter = false, $returnOnlySQL = false)
    {
        // build main query(getting all personal use payments)
        $options = [
            'return' => [
                'pu.owner_id as owner_id',
                'c.id as contract_id',
                'c.c_num as c_num',
                'spur.renta_type',
                'spur.unit_value as personal_use_unit_value',
                'srt."name" as renta_type_name',
                'ROUND((sum(sum(ROUND(spur.area::numeric, 5))) over (PARTITION BY owner_id, c.id))::numeric, 3) as total_personal_use_area',
                'json_agg(json_build_object(
                    \'pc_rel_id\', pu.pc_rel_id,
                    \'area\', ROUND(spur.area::numeric, 5)
                )) as personal_use_plots_area',
                'sum(ROUND(spur.area::numeric, 3)) as area_by_renta_type',
                'sum(ROUND(spur.area::numeric, 3) * spur.renta_per_dka) as personal_use_renta',
                '(
                    select
                        ROUND(sum(scpr.area_for_rent * spor.percent / 100)::NUMERIC, 3)	
                    from su_contracts_plots_rel scpr
                    inner join su_plots_owners_rel spor on spor.pc_rel_id = scpr.id
                    where 
                        spor.owner_id = pu.owner_id
                        and scpr.contract_id = coalesce(a.id, c.id)
                ) as total_owned_area',
                'ROUND(sum(ROUND(spur.area::numeric, 3) * coalesce(spur.treatments_price, 0))::numeric, 2) as personal_use_treatments_sum',
                '(
                    SELECT COALESCE(sum(amount), 0)
                    FROM public.su_collections
                    where
                        contract_id = coalesce(a.id, c.id)
                        and "type" = ' . TRANSACTION_TYPE_PERSONAL_USE . '
                        AND status = true
                        AND farming_year IN (' . $params['chosen_years'] . ')
                        AND payment_data->>\'owner_id\' = pu.owner_id::text 
                        AND payment_data->>\'renta_type_id\' = srt.id::text
                    ) as personal_use_paid_treatments',
                'ROUND(sum(ROUND(spur.area::numeric, 3) * coalesce(spur.treatments_price, 0))::numeric, 2) - (
                    SELECT COALESCE(sum(amount), 0)
                    FROM public.su_collections
                    where
                        contract_id = coalesce(a.id, c.id)
                        and "type" = ' . TRANSACTION_TYPE_PERSONAL_USE . '
                        AND status = true
                        AND farming_year IN (' . $params['chosen_years'] . ')
                        AND payment_data->>\'owner_id\' = pu.owner_id::text 
                        AND payment_data->>\'renta_type_id\' = srt.id::text
                ) as personal_use_unpaid_treatments',
                '(
                    select 
                        sum(stn.amount)
                    from su_transactions st
                    left join su_transactions_natura stn on stn.transaction_id = st.id 
                    left join su_payments sp on sp.transaction_id = st.id
                    where 
                        st.type = ' . TRANSACTION_TYPE_PERSONAL_USE . '
                        and st.status = true
                        and sp.owner_id = pu.owner_id
                        and sp.farming_year IN (' . $params['chosen_years'] . ')
                        and sp.contract_id = coalesce(a.id, c.id)
                        and stn.nat_type = srt.id
                 ) as personal_use_paid_renta',
                'sum(ROUND(spur.area::numeric, 3) * spur.renta_per_dka) - 
                 coalesce ((
                    select 
                        sum(stn.amount)
                    from su_transactions st
                    left join su_transactions_natura stn on stn.transaction_id = st.id 
                    left join su_payments sp on sp.transaction_id = st.id
                    where 
                        st.type = ' . TRANSACTION_TYPE_PERSONAL_USE . '
                        and st.status = true
                        and sp.owner_id = pu.owner_id
                        and sp.farming_year IN (' . $params['chosen_years'] . ')
                        and sp.contract_id = coalesce(a.id, c.id)
                        and stn.nat_type = srt.id
                 ), 0) as personal_use_unpaid_renta',
            ],
            'where' => [
                'annex_action' => ['column' => 'annex_action', 'compare' => '=', 'prefix' => 'pc', 'value' => 'added'],
            ],
            'group' => 'pu.owner_id, c.id, a.id, spur.renta_type, srt."name", srt.id, spur.unit_value',
            'custom_counter' => 'COUNT(DISTINCT(o.id))',
            'chosen_years' => $params['chosen_years'],
        ];
        if ($params['owner_ids']) {
            $options['where']['owner_id'] = ['column' => 'owner_id', 'compare' => 'IN', 'prefix' => 'pu', 'value' => $params['owner_ids']];
        }
        if ($params['contract_id']) {
            $options['contract_id_string'] = $params['contract_id'];
        }
        if ($params['year'] || $params['chosen_years']) {
            $options['start_date'] = $GLOBALS['Farming']['years'][$params['year'] ?? $params['chosen_years']]['start_date'];
            $options['due_date'] = $GLOBALS['Farming']['years'][$params['year'] ?? $params['chosen_years']]['end_date'];
        }

        return $this->DbHandler->getPersonalUseForOwners($options, $counter, $returnOnlySQL);
    }

    public function getOwnerPersonalUseInfo($personalUse, &$owner)
    {
        $owner['personal_use'] = [];
        $owner['personal_use_nat_type_id'] = [];
        $owner['personal_use_nat_types_names_arr'] = [];
        $owner['personal_use_amount_arr'] = [];
        $owner['personal_use_price_sum'] = [];
        $owner['personal_use_paid_arr'] = [];
        $owner['personal_use_unpaid_arr'] = [];
        $owner['personal_use_total'] = [];
        $totalPersonalUse = [];
        foreach ($personalUse as $personalUseValue) {
            if ($personalUseValue['owner_id'] === $owner['owner_id']) {
                $part = round(($owner['owner_area'] / $personalUseValue['total_owned_area']), 3);
                $owner['personal_use_nat_type_id'][] = $personalUseValue['renta_type'];
                $owner['personal_use_unit_value'][] = $personalUseValue['personal_use_unit_value'];
                $owner['personal_use_nat_types_names_arr'][] = $personalUseValue['renta_type_name'];
                $owner['personal_use_renta_arr'][] = number_format($personalUseValue['personal_use_renta'], 3, '.', '');
                $owner['personal_use_paid_renta_arr'][] = number_format($personalUseValue['personal_use_paid_renta'], 3, '.', '');
                $owner['personal_use_unpaid_renta_arr'][] = number_format($personalUseValue['personal_use_unpaid_renta'], 3, '.', '');
                $owner['personal_use_treatments_sum_arr'][] = number_format($personalUseValue['personal_use_treatments_sum'], 3, '.', '');
                $owner['personal_use_paid_treatments_arr'][] = number_format($personalUseValue['personal_use_paid_treatments'], 3, '.', '');
                $owner['personal_use_unpaid_treatments_arr'][] = number_format($personalUseValue['personal_use_unpaid_treatments'], 3, '.', '');

                if (!array_key_exists($personalUseValue['renta_type'], $totalPersonalUse)) {
                    $totalPersonalUse[$personalUseValue['renta_type']] = [
                        'name' => $personalUseValue['renta_type_name'],
                        'amount' => $personalUseValue['personal_use_renta'],
                        'paid' => 0,
                        'unpaid' => 0,
                    ];
                } else {
                    $totalPersonalUse[$personalUseValue['renta_type']]['amount'] += $personalUseValue['personal_use_renta'];
                }

                $owner['personal_use'][] = $personalUseValue;
            }
        }

        $owner['personal_use_nat_types_names'] = implode('</br>', $owner['personal_use_nat_types_names_arr']);
        $owner['personal_use_renta'] = implode('</br>', $owner['personal_use_renta_arr']);
        $owner['personal_use_paid_renta'] = implode('</br>', $owner['personal_use_paid_renta_arr']);
        $owner['personal_use_unpaid_renta'] = implode('</br>', $owner['personal_use_unpaid_renta_arr']);
        $owner['personal_use_treatments_sum'] = implode('</br>', $owner['personal_use_treatments_sum_arr']);
        $owner['personal_use_paid_treatments'] = implode('</br>', $owner['personal_use_paid_treatments_arr']);
        $owner['personal_use_unpaid_treatments'] = implode('</br>', $owner['personal_use_unpaid_treatments_arr']);

        return $totalPersonalUse;
    }

    public function getPersonalUseArea($personalUse, $ownerID, $plot, $оwnerArea)
    {
        $puArea = 0;

        foreach ($personalUse as $ownerPersonalUse) {
            if ($ownerPersonalUse['owner_id'] == $ownerID && $ownerPersonalUse['contract_id'] == $plot['contract_id']) {
                $ownerPersonalUsePlots = json_decode($ownerPersonalUse['personal_use_plots_area'], true);
                // Проверяваме дали дадения имот има лично ползване
                foreach ($ownerPersonalUsePlots as $ownerPersonalUsePlot) {
                    if ($ownerPersonalUsePlot['pc_rel_id'] == $plot['pc_rel_id']) {
                        // Намираме, каква част от личното ползване е за конкретния собственик т.к. може да се явява и като наследник на друг собственик
                        $personalUsePart = $оwnerArea / $ownerPersonalUse['total_owned_area'];

                        // Намираме площта на личното ползване за имота
                        $puArea += round(($ownerPersonalUsePlot['area'] * $personalUsePart), 5);
                    }
                }
            }
        }

        return round($puArea, 3);
    }

    public function getOwnersPropertyFromOwnersTree($tree, $key = 'owner_id')
    {
        $vals = [];
        foreach ($tree as $element) {
            if (isset($element[$key])) {
                $vals[] = $element[$key];
            }
            if (isset($element['children'])) {
                $vals = array_merge($vals, $this->getOwnersPropertyFromOwnersTree($element['children'], $key));
            }
        }

        return $vals;
    }

    public function findOwnerFromTreeByPath($tree, $searchedOwner)
    {
        foreach ($tree as $owner) {
            if (!isset($owner['path']) && !isset($searchedOwner['path']) && $owner['owner_id'] == $searchedOwner['owner_id']) {
                return $owner;
            }
            if (isset($owner['path']) && $owner['path'] == $searchedOwner['path']) {
                return $owner;
            }
            if (isset($owner['children'])) {
                $result = $this->findOwnerFromTreeByPath($owner['children'], $searchedOwner);
                if (null !== $result) {
                    return $result;
                }
            }
        }

        return;
    }

    public function getPaymentsForOwnersWithLastAnnex($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getPaymentsForOwnersWithLastAnnex($options, $counter, $returnOnlySQL);
    }

    public function getChargedRentaParams($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getChargedRentaParams($options, $counter, $returnOnlySQL);
    }

    public function getPaymentsByParams($options, $counter = false, $returnOnlySQL = false, $status = 'true')
    {
        return $this->DbHandler->getPaymentsByParams($options, $counter, $returnOnlySQL, $status);
    }

    public function getCollectionsByParams($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getCollectionsByParams($options, $counter, $returnOnlySQL);
    }

    public function getPaymentsByParamsWithKvs($options, $counter = false, $returnOnlySQL = false, $status = 'true')
    {
        return $this->DbHandler->getPaymentsByParamsWithKvs($options, $counter, $returnOnlySQL, $status);
    }

    public function getTransactionsByParams($options, $counter = false, $returnOnlySQL = false, $status = 'true')
    {
        return $this->DbHandler->getTransactionsByParams($options, $counter, $returnOnlySQL, $status);
    }

    public function disableTransactionsById($id, $user_name)
    {
        return $this->DbHandler->disableTransactionsById($id, $user_name);
    }

    public function getOwnersPersonalUse($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getOwnersPersonalUse($options, $counter, $returnOnlySQL);
    }

    public function getOwnersHeritorsPersonalUse($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getOwnersHeritorsPersonalUse($options, $counter, $returnOnlySQL);
    }

    public function getContractPersonalUse($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getContractPersonalUse($options, $counter, $returnOnlySQL);
    }

    public function getPersonalUse($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getPersonalUse($options, $counter, $returnOnlySQL);
    }

    public function deletePersonalUse($options)
    {
        $this->DbHandler->deletePersonalUse($options);
    }

    public function getOwnerPersonalUse($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getOwnerPersonalUse($options, $counter, $returnOnlySQL);
    }

    public function getCollectionsPersonalUse($options, $counter = false, $returnOnlySQL = false)
    {
        $options['return'] = [
            'pu.year as farming_year',
            'c.farming_id as farming_id',
            'c.active as active',
            'farming.name as farming_name',
            'farming.mol as farming_mol',
            'farming.mol_egn as farming_mol_egn',
            'srt."name" as renta_type_name',
            'srt.id as renta_type_id',
            'farming.address as farming_address',
            'c.id as contract_id',
            'o.id as owner_id',
            'o.bank_name as owner_bank_name',
            'o.bic as owner_bic',
            'o.iban as owner_iban',
            '(case when p.id is not null then p.c_num || \' - \' || c.c_num else c.c_num end) as c_num',
            'to_char(c.start_date, \'DD.MM.YYYY\') as start_date',
            'to_char(c.due_date, \'DD.MM.YYYY\') as due_date',
            '(case when o.owner_type = 1 then concat(o."name", \' \', o.surname, \' \',o.lastname) else o.company_name end) as owner_names',
            '(case when o.owner_type = 1 then o.egn else o.eik end) as egn_eik',
            'sum(ROUND(pur.area::numeric, 3)) as personal_use_area',
            'max(pur.average_yield) as average_yield',
            'max(pur.renta_per_dka) as renta_per_dka',
            'max(pur.treatments_price) as treatments_price',
            'ROUND(sum(ROUND(pur.area::numeric, 3) * coalesce(pur.treatments_price, 0))::numeric, 2) as personal_use_treatments_sum',
            '(SELECT COALESCE(sum(amount), 0)
                    FROM public.su_collections
                    where
                        contract_id = c.id
                        AND "type" = ' . TRANSACTION_TYPE_PERSONAL_USE . '
                        AND status = true
                        AND farming_year = pu.year
                        AND payment_data->>\'owner_id\' = o.id::text 
                        AND payment_data->>\'renta_type_id\' = srt.id::text 
                ) as personal_use_paid_treatments,
                ROUND(sum(ROUND(pur.area::numeric, 3) * coalesce(pur.treatments_price, 0))::numeric, 2) - (SELECT COALESCE(sum(amount),0)
                    FROM public.su_collections
                    where
                        contract_id = c.id
                        AND "type" = ' . TRANSACTION_TYPE_PERSONAL_USE . '
                        AND status = true
                        AND farming_year = pu.year
                        AND payment_data->>\'owner_id\' = o.id::text 
                        AND payment_data->>\'renta_type_id\' = srt.id::text
                ) as personal_use_unpaid_treatments',
            'sum(pur.renta_per_dka * ROUND(pur.area::numeric, 3)) as personal_use_rent_quantity',
            '(
                    select 
                        sum(stn.amount)
                    from su_transactions st
                    left join su_transactions_natura stn on stn.transaction_id = st.id 
                    left join su_payments sp on sp.transaction_id = st.id
                    where 
                        st.type = ' . TRANSACTION_TYPE_PERSONAL_USE . '
                        and st.status = true
                        and sp.owner_id = o.id
                        and sp.farming_year = pu.year
                        and sp.contract_id = c.id
                        and stn.nat_type = srt.id
                 ) as personal_use_paid_rent_quantity',
            'sum(pur.renta_per_dka * round(pur.area::numeric, 3)) - 
                     coalesce ((
                        select 
                            sum(stn.amount)
                        from su_transactions st
                        left join su_transactions_natura stn on stn.transaction_id = st.id 
                        left join su_payments sp on sp.transaction_id = st.id
                        where 
                            st.type = ' . TRANSACTION_TYPE_PERSONAL_USE . '
                            and st.status = true
                            and sp.owner_id = o.id
                            and sp.farming_year = pu.year
                            and sp.contract_id = c.id
                            and stn.nat_type = srt.id
                     ), 0) as personal_use_unpaid_rent_quantity',
        ];

        $options['joins'] = [
            "left join lateral (
                    select
                        id,
                        name,
                        company,
                        address,
                        company_address,
                        mol,
                        mol_egn,
                        iban_arr
                    from
                        dblink('dbname=" . DBLINK_DATABASE . ' host=' . DBLINK_HOST . ' port=' . DBLINK_PORT . ' user=' . DBLINK_USERNAME . ' password=' . DBLINK_PASSWORD . "',
                        'SELECT id, name, company, address, company_address, mol, mol_egn, iban_arr FROM su_users_farming WHERE id = '  || c.farming_id) as t( id int,
                        name text,
                        company text,
                        address text,
                        company_address text,
                        mol text,
                        mol_egn text,
                        iban_arr json ) 
                ) farming on true",
            // 'inner join su_plots_owners_rel spor on spor.pc_rel_id = cpr.id and spor.owner_id = o.id',
        ];

        if ($options['farming_year_start'] && $options['farming_year_end']) {
            $options['joins'][] = "left join su_contracts a ON(a.parent_id = c.id AND a.active = true and a.start_date <= '" . $options['farming_year_start'] . "' and a.due_date >= '" . $options['farming_year_end'] . "')";
        }

        $options['group'] = 'o.id, c.id, p.id, pu.year, farming.name, farming.mol, farming.address, farming.mol_egn, srt.id';
        $options['custom_counter'] = 'COUNT(distinct o.id)';

        $options['sort'] = 'o."name"';
        $options['order'] = 'asc';

        return $this->DbHandler->getCollectionsPersonalUse($options, $counter, $returnOnlySQL);
    }

    public function getContractOwnerDistributionData($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getContractOwnerDistributionData($options, $counter, $returnOnlySQL);
    }

    public function getPayrollData($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getPayrollData($options, $counter, $returnOnlySQL);
    }

    public function getPayrollDataWithPayments($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getPayrollDataWithPayments($options, $counter, $returnOnlySQL);
    }

    public function getPaidData($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getPaidData($options, $counter, $returnOnlySQL);
    }

    public function getPaymentsForOwnersReport($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getPaymentsForOwnersReport($options, $counter, $returnOnlySQL);
    }

    public function getRentaNaturaContracts($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getRentaNaturaContracts($options, $counter, $returnOnlySQL);
    }

    public function getPaidRentaNaturaAmount($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getPaidRentaNaturaAmount($options, $counter, $returnOnlySQL);
    }

    public function getNaturaAmountDueByContracts($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getNaturaAmountDueByContracts($options, $counter, $returnOnlySQL);
    }

    public function getNaturaCalculatedAmountByContracts($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getNaturaCalculatedAmountByContracts($options, $counter, $returnOnlySQL);
    }

    public function calculateUnpaidAndOverpaidRentaNat($renta_nat, $charged_renta_nat = null, $paid_renta_nat)
    {
        return $this->DbHandler->calculateUnpaidAndOverpaidRentaNat($renta_nat, $charged_renta_nat, $paid_renta_nat);
    }

    public function getRentaNaturaByContractsAndNaturaType($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getRentaNaturaByContractsAndNaturaType($options, $counter, $returnOnlySQL);
    }

    public function getRentasAndAreasByContractsAndNaturaType($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getRentasAndAreasByContractsAndNaturaType($options, $counter, $returnOnlySQL);
    }

    public function editOrAddChargeRentaNatura($options)
    {
        return $this->DbHandler->editOrAddChargeRentaNatura($options);
    }

    public function hasContractRentNat(int $contractId, $annexId, int $rentNatId, $plotRentId = null)
    {
        return $this->DbHandler->hasContractRentNat($contractId, $annexId, $rentNatId, $plotRentId);
    }

    public function getPaymentReports($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getPaymentReports($options, $counter, $returnOnlySQL);
    }

    public function getPaidAmount($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getPaidAmount($options, $counter, $returnOnlySQL);
    }

    public function getRentasAndAreas($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getRentasAndAreas($options, $counter, $returnOnlySQL);
    }

    public function getAmountDue($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getAmountDue($options, $counter, $returnOnlySQL);
    }

    public function getChargedRentasAndAreas($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getChargedRentasAndAreas($options, $counter, $returnOnlySQL);
    }

    public function getChargedAmount($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getChargedAmount($options, $counter, $returnOnlySQL);
    }

    public function calculateUnpaidAndOverpaidRenta($renta, $charged_renta = null, $paid_renta)
    {
        return $this->DbHandler->calculateUnpaidAndOverpaidRenta($renta, $charged_renta, $paid_renta);
    }

    public function getDetailedPaymentReportsByDate($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getDetailedPaymentReportsByDate($options, $counter, $returnOnlySQL);
    }

    public function getAreaUsed($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getAreaUsed($options, $counter, $returnOnlySQL);
    }

    public function getContractsByOwnerPaymentsData($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getContractsByOwnerPaymentsData($options, $counter, $returnOnlySQL);
    }

    public function getContractsOrAnnexByOwnerPaymentsData($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getContractsOrAnnexByOwnerPaymentsData($options, $counter, $returnOnlySQL);
    }

    public function getContractsWithRentaNaturaData($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getContractsWithRentaNaturaData($options, $counter, $returnOnlySQL);
    }

    public function getAnnexesByOwnerPaymentsData($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getAnnexesByOwnerPaymentsData($options, $counter, $returnOnlySQL);
    }

    public function getRentasAndAreasNatura($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getRentasAndAreasNatura($options, $counter, $returnOnlySQL);
    }

    public function getContractRentaNatura($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getContractRentaNatura($options, $counter, $returnOnlySQL);
    }

    public function getChargedRentasNatura($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getChargedRentasNatura($options, $counter, $returnOnlySQL);
    }

    public function getLastGeneratedRkoNumber($options = [], $returnOnlySQL = false)
    {
        return $this->DbHandler->getLastGeneratedRkoNumber($options, $returnOnlySQL);
    }

    public function setRkoNumberingStart($newNum, $farmingId)
    {
        return $this->DbHandler->setRkoNumberingStart($newNum, $farmingId);
    }

    public function getPaymentDocNumbersByTransactionID($transactionID)
    {
        return $this->DbHandler->getPaymentDocNumbersByTransactionID($transactionID);
    }

    public function updateRkoNumberByPaymentId($paymentArray)
    {
        return $this->DbHandler->updateRkoNumberByPaymentId($paymentArray);
    }

    public function getCurrentAnnexId($contractID, $startDate, $dueDate)
    {
        return $this->DbHandler->getCurrentAnnexId($contractID, $startDate, $dueDate);
    }

    public function getPaymentsByBankAndNatura($options, $counter, $returnOnlySQL)
    {
        return $this->DbHandler->getPaymentsByBankAndNatura($options, $counter, $returnOnlySQL);
    }

    public function getUnpaidRentaByOwner($options)
    {
        $data = $this->DbHandler->getUnpaidRentaByOwner($options);
        $total = $this->DbHandler->getUnpaidRentaByOwner($options, true);

        $rows = [];

        foreach ($data as $idx => $item) {
            $row = [
                'owner_names' => $item['owner_names'],
                'egn_eik' => $item['egn_eik'],
                'unpaid_leva' => $item['unpaid_leva'],
                'farming_year' => $GLOBALS['Farming']['years'][$options['where']['farming_years']['value'][0]]['farming_year_short'],
            ];

            if ($item['unpaid_natura']) {
                $natCols = $this->formatUnpaidRentaNatColumns($item['unpaid_natura']);
                $row = array_merge($row, $natCols);
            }

            $rows[] = $row;
        }
        $footerRow = [
            'egn_eik' => '<b>Общо</b>',
            'unpaid_leva' => $total['unpaid_leva'],
        ];

        $footerTotalNat = $this->formatUnpaidRentaNatColumns($total['unpaid_natura']);
        $footerRow = array_merge($footerRow, $footerTotalNat);

        return [
            'rows' => $rows,
            'total' => $total['count'],
            'footer' => [
                $footerRow,
            ],
        ];
    }

    public function prepareChargedRentaDataGrid($results)
    {
        $UserDbOwnersController = new UserDbOwnersController($this->Database);
        $UsersController = new UsersController();
        $UserDbController = new UserDbController($this->Database);
        $UserDbPlotCategoriesTypeController = new UserDbPlotCategoriesTypeController($this->Database);

        $renta_types = [];

        $options = [
            'tablename' => $UserDbController->DbHandler->tableRentaTypes,
        ];
        $renta_results = $UserDbController->getItemsByParams($options, false, false);
        // create renta types array

        $rentCount = count($renta_results);
        for ($i = 0; $i < $rentCount; $i++) {
            $renta_types[$renta_results[$i]['id']] = $renta_results[$i]['name'] . '(' . $GLOBALS['Contracts']['renta_units'][$renta_results[$i]['unit']]['name'] . ')';
        }

        $resultsCount = count($results);
        for ($i = 0; $i < $resultsCount; $i++) {
            $converted_renta = 0;
            $have_converted_renta = false;
            $result = &$results[$i];

            if (null != $result['ekate'] || '' != $result['ekate']) {
                $result['ekate'] = $UsersController->getEkatteName($result['ekate']);
            }

            if (null != $result['category'] || '' != $result['category']) {
                $result['category'] = $UserDbPlotCategoriesTypeController->getPlotCategoryTitle($result['category']);
            }

            if (null != $result['owner_id'] || '' != $result['owner_id']) {
                $ownerId = $result['owner_id'];
                $options = [
                    'return' => ['o.*'],
                    'where' => [
                        'owner_id' => ['column' => 'id', 'prefix' => 'o', 'compare' => '=', 'value' => $ownerId],
                    ],
                ];
                $resultOwner = $UserDbOwnersController->getOwnersData($options, false, false);

                $owner = $resultOwner[0];

                $result['owner_id'] = $owner['name'] . ' ' . $owner['surname'] . ' ' . $owner['lastname'];

                if ($owner['company_name']) {
                    $result['owner_id'] = $owner['company_name'];
                }
            }

            if (true === $result['should_recalculate_renta_nat'] || (null != $result['charged_renta_nat'] || '' != $result['charged_renta_nat'])) {
                if (empty($result['renta_nats']) && !empty($result['params_id'])) {
                    $options = [
                        'tablename' => $UserDbController->DbHandler->chargedRentaNaturaParams,
                        'return' => ['amount', 'nat_type', 'is_converted', 'price_per_unit'],
                        'where' => [
                            'params_id' => ['column' => 'params_id', 'compare' => '=', 'value' => $result['params_id']],
                        ],
                    ];
                    $charged_renta_nat_results = $UserDbController->getItemsByParams($options, false, false);
                } else {
                    $charged_renta_nat_results = $result['renta_nats'];
                    unset($result['renta_nats']);
                }

                foreach ($charged_renta_nat_results as $charged_renta_nat) {
                    if (true == $charged_renta_nat['is_converted']) {
                        $amount = $charged_renta_nat['renta_value'] ?? $charged_renta_nat['amount'];
                        $converted_renta += ($charged_renta_nat['price_per_unit'] * $amount * $result['owner_area']);
                        $have_converted_renta = true;
                    }
                }
                $rentaTypeCount = count($charged_renta_nat_results);
                $result['charged_renta_nat'] = [];

                for ($m = 0; $m < $rentaTypeCount; $m++) {
                    $renta = $charged_renta_nat_results[$m];
                    $type = $renta['renta_nat_type'];
                    $amount = $renta['is_converted'] ? 0 : $renta['renta_value'] * $result['owner_area'];

                    if ('' == $amount || 0 == $amount) {
                        $amount = 'без количество';
                    } else {
                        $amount = number_format($amount, 3, '.', '');
                    }

                    $result['charged_renta_nat'][] = $renta_types[$type] . ' - ' . $amount;
                }

                $result['charged_renta_nat'] = implode('</br> ', $result['charged_renta_nat']);
            }

            if (null != $result['owner_area'] || '' != $result['owner_area']) {
                $result['owner_area'] = number_format($result['owner_area'], 3, '.', '');
            }

            if ($have_converted_renta) {
                $result['charged_renta'] += $converted_renta;
            }

            if (null != $result['charged_renta'] || '' != $result['charged_renta']) {
                $result['charged_renta'] = number_format($result['charged_renta'], 2, '.', '');
            }

            if (null != $result['contract_id'] || '' != $result['contract_id']) {
                $options = [
                    'tablename' => $UserDbController->DbHandler->tableContracts,
                    'return' => ['c_num'],
                    'where' => [
                        'id' => ['column' => 'id', 'compare' => '=', 'value' => $result['contract_id']],
                    ],
                ];

                $resultsContracts = $UserDbController->getItemsByParams($options);
                $contractName = $resultsContracts[0];

                $result['contract_id'] = $contractName['c_num'];
            }
        }

        return $results;
    }

    public function getDeclaration73Data(array $data, ?int $page = null, ?int $rows = null, ?string $sort = null, ?string $order = null): array
    {
        $timePeriod = $this->getDeclaration73TimePeriod($data['year']);
        $dateFrom = $timePeriod['dateFrom'];
        $dateTo = $timePeriod['dateTo'];
        $options = [
            'return' => [
                'concat(\'R_\',t.ID) AS transaction_id',
                'to_char(p.date, \'YYYY-MM-DD\') as date',
                'concat(o.name, \' \',o.surname, \' \', o.lastname) as name',
                'o.egn',
                'round(coalesce(sum(p.amount::numeric), 0),2) as amount',
                '0.00 as tax',
                '\'рента\' as reason',
                'o.address',
                'case when o.is_foreigner is true then \'Да\' else \'Не\' end as is_foreigner',
            ],
            'where' => [
                'farm' => ['column' => 'farming_id', 'compare' => '=', 'prefix' => 'c', 'value' => $data['farming']],
                'dateFrom' => ['column' => 'date', 'compare' => '>=', 'prefix' => 'p', 'value' => $dateFrom],
                'dateTo' => ['column' => 'date', 'compare' => '<=', 'prefix' => 'p', 'value' => $dateTo],
                'paid_in' => ['column' => 'paid_in', 'compare' => '=', 'prefix' => 'p', 'value' => 1],
                'paid_from' => ['column' => 'paid_from', 'compare' => 'BETWEEN', 'prefix' => 'p', 'value' => [1, 2]],
                'payments' => ['column' => "(
                                    select sum(p_inner.amount)
                                    from su_transactions t_inner
                                    inner join su_payments p_inner on t_inner.id = p_inner.transaction_id
                                    inner join su_owners o_inner on o_inner.id = p_inner.owner_id
                                    inner join su_contracts c_inner on c_inner.id = p_inner.contract_id
                                    where p_inner.owner_id = o.id
                                    and c_inner.farming_id = '{$data['farming']}'
                                    and t_inner.status = true
                                    and p_inner.date >= '{$dateFrom}'
                                    and p_inner.date <= '{$dateTo}'
                                )", 'compare' => '>=', 'value' => 3000],
            ],
            'caseWhere' => ' and case 
                                when o.is_foreigner is false 
                                then LENGTH(o.egn) > 0
                                else o.is_foreigner is true
                            end
            ',
            'annexes_no_join' => true,
            'rent_nature_no_join' => true,
            'union_dividends' => false,
            'sort' => 'p.date',
            'group' => 't.id, p.date, o.name, o.surname, o.lastname, o.egn, o.address, o.is_foreigner',
        ];

        if ($data['with_dividends']) {
            $options['union_dividends'] = true;
            $options['return_dividends'] = [
                'concat(\'D_\',MAX(dp.id)) as transaction_id',
                'to_char(dp.pay_date, \'YYYY-MM-DD\') as date',
                'concat(c.name, \' \',c.surname, \' \', c.lastname) as name',
                'c.egn',
                'round(sum(dp.cashout::numeric)::numeric, 2) as amount',
                'round((sum(dp.cashout::numeric)::numeric * 0.05),2) as tax',
                '\'дивидент\' as reason',
                '\'\' as address',
                '\'Не\' as is_foreigner',
            ];
            $options['where_dividends'] = [
                'dateFrom' => ['column' => 'pay_date', 'compare' => '>=', 'prefix' => 'dp', 'value' => $dateFrom],
                'dateTo' => ['column' => 'pay_date', 'compare' => '<=', 'prefix' => 'dp', 'value' => $dateTo],
            ];
            $options['group_dividends'] = ' c.name, c.surname, c.lastname, c.egn, dp.pay_date';
            $options['having_dividends'] = 'SUM(round(coalesce(dp.cashout::numeric, 0), 2)) > 0';

            $options['union_sort'] = 'date';
        }

        $totalResults = $this->getPaymentsByParamsWithKvs($options, false, false);

        if (0 == count($totalResults)) {
            return ['rows' => [], 'total' => 0];
        }

        if (!empty($order)) {
            $options['order'] = $order;
        }

        if (!empty($sort)) {
            $options['sort'] = $sort;
        }

        if (!empty($page) && !empty($rows)) {
            $options['offset'] = ($page - 1) * $rows;
            $options['limit'] = $rows;
        }

        $result = $this->getPaymentsByParamsWithKvs($options, false, false);

        foreach ($result as &$row) {
            $row['amount'] = BGNtoEURO($row['amount']);
            $row['tax'] = BGNtoEURO($row['tax']);
        }

        return [
            'rows' => $result,
            'total' => count($totalResults),
        ];
    }

    public function getDeclaration73TimePeriod(int $yearCode): array
    {
        // Тази дата се използва за начална дата на справката. Не трябва да се визуализират данни за плащания, правени преди тази дата.
        $beginDate = '2016-01-01';
        $year = $GLOBALS['Farming']['years'][$yearCode]['year'];
        $dateFrom = $year . '-01-01';
        $dateTo = $year . '-12-31';

        if ($dateFrom < $beginDate) {
            $dateFrom = $beginDate;
        }

        if ($dateTo) {
            $dateTo = $dateTo > $dateFrom ? $dateTo : date('Y-m-d', strtotime($dateFrom . ' + 7 days'));
        }

        return [
            'dateFrom' => $dateFrom,
            'dateTo' => $dateTo,
        ];
    }

    public function getPersonalUseData($options)
    {
        $default = [
            'rows' => [],
            'total' => 0,
        ];
        if (!$options['contract_id'] || !(int)$options['contract_id']) {
            return $default;
        }
        if (!(int)$options['year'] && !$options['addedOnly']) {
            return $default;
        }
        $UserDbController = new UserDbController($this->Database);
        $UsersController = new UsersController('Users');

        // get annex_id from $options if not exists get contract_id
        $contractId = $options['annex_id'] ? $options['annex_id'] : $options['contract_id'];

        $repQuery = "SELECT string_agg(rep_name || ' ' || rep_surname || ' ' || rep_lastname, ', ') as rep_names FROM {$UserDbController->DbHandler->tableOwnersReps} ir
                    WHERE ir.id IN 
                        (SELECT rep_id FROM {$UserDbController->DbHandler->plotsOwnersRelTable} ipo 
                            WHERE ipo.owner_id = o.id
                            AND ipo.pc_rel_id IN
                                (SELECT DISTINCT(id) FROM {$UserDbController->DbHandler->contractsPlotsRelTable} 
                                WHERE contract_id IN ({$contractId})))";
        $params = [
            'return' => [
                // owner data
                'o.id as owner_id',
                "(CASE WHEN owner_type = 1 THEN name || ' ' || surname || ' ' || lastname ELSE company_name END) as owner_names",
                // contract data
                'c.id as contract_id', 'c.c_num', 'c.c_date',
                // kvs data
                'SUM(pc.area_for_rent * po.percent / 100) as kvs_used_area',
                'SUM(ST_Area(geom)/1000 * po.percent / 100) as kvs_area',
                // personal use data
                "string_agg (
                    concat_ws ('|',
                        COALESCE(kvs.gid::TEXT, ''),
                        COALESCE(kvs.virtual_ekatte_name::TEXT, ''),
                        COALESCE(kvs.kad_ident::TEXT, ''),
                        COALESCE(kvs.virtual_category_title::TEXT, ''),
                        COALESCE(kvs.virtual_ntp_title::TEXT, ''),
                        (pc.area_for_rent * po.percent / 100)::TEXT, 
                        (case when COALESCE(pu.area, 0) = 0 THEN '' else pu.area::TEXT end)
                    ),
                    ','
                ) AS personal_use_plots_string",
                'SUM(pur.area) as personal_area',
                "string_agg(pu.id::text, ','::TEXT) as pu_ids",
                "({$repQuery}) as rep_names",
            ],
            'where' => [
                'annex_action' => ['column' => 'annex_action', 'compare' => '=', 'prefix' => 'pc', 'value' => 'added'],
                'is_edited' => ['column' => 'is_edited', 'compare' => '=', 'prefix' => 'kvs', 'value' => 'FALSE'],
            ],
            'sort' => $options['sort'],
            'order' => $options['order'],
            'offset' => ($options['page'] - 1) * $options['rows'],
            'limit' => $options['rows'],
            'group' => 'o.id, c.id',
            'custom_counter' => 'COUNT(DISTINCT(o.id))',
            // this parameter will be used for joining charged renta table
            'contract_id_string' => $contractId,
        ];
        if ($options['year']) {
            $params['where']['year'] = ['column' => 'year', 'compare' => '=', 'prefix' => 'pu', 'value' => $options['year']];
        }
        if ($options['addedOnly']) {
            $params['addedOnly'] = $options['addedOnly'];
            $params['where']['addedOnly'] = ['column' => 'id', 'compare' => 'is not', 'prefix' => 'pu', 'value' => 'NULL'];
        }
        if ($options['alreadyAddedOwnersIds']) {
            $params['where']['alreadyAddedOwnersIds'] = ['column' => 'id', 'compare' => 'NOT IN', 'prefix' => 'o', 'value' => $options['alreadyAddedOwnersIds']];
        }
        $counter = $this->getOwnersPersonalUse($params, true, false);
        if (0 == $counter[0]['count']) {
            return $default;
        }
        $results = $this->getOwnersPersonalUse($params, false, false);
        $resultsCount = count($results);

        // iterate and convert results to grid format
        for ($i = 0; $i < $resultsCount; $i++) {
            $result = &$results[$i];
            $result['kvs_used_area'] = number_format($result['kvs_used_area'], 3, '.', '');
            $result['personal_area'] = number_format($result['personal_area'], 3, '.', '');
            $personalUseFinal = [];
            if ($result['personal_use_plots_string']) {
                $personalUsePlots = explode(',', $result['personal_use_plots_string']);
                $personalUsePlotsCount = count($personalUsePlots);
                for ($m = 0; $m < $personalUsePlotsCount; $m++) {
                    [$plot_id, $ekate, $kad_ident, $category,$area_type, $contract_area, $personal_use] = explode('|', $personalUsePlots[$m]);

                    $personalUseFinal[] = [
                        'plot_id' => $plot_id,
                        'ekate' => $ekate,
                        'kad_ident' => $kad_ident,
                        'category' => $category,
                        'area_type' => $area_type,
                        'contract_area' => $contract_area,
                        'personal_use' => $personal_use,
                    ];
                }
            }
            $result['personal_use_plots'] = $personalUseFinal;
            // put new rows if reps are more than one
            $tmp_reps_array = explode(', ', $result['rep_names']);
            if (count($tmp_reps_array) > 1) {
                $result['rep_names'] = implode(', <br/>', $tmp_reps_array);
            }
        }

        return [
            'rows' => $results,
            'total' => $counter[0]['count'],
        ];
    }

    public function getPersonalUsePayments($options, $counter = false, $returnOnlySQL = false)
    {
        $paidOptions = [
            'custom_counter' => 'COUNT(DISTINCT(p.owner_id))',
            'order' => 'asc',
            'sort' => 'p.contract_id',
            'return' => [
                'p.id as payment_id',
                'p.owner_id as owner_id',
                'p.contract_id as contract_id',
                'p.amount::numeric as trans_amount',
                'pn.amount::numeric as trans_amount_nat',
                'p.amount_nat::numeric as amount_nat',
                'pn.unit_value::numeric as unit_value',
                'pn.nat_type as nat_type',
                'p.paid_in',
                'p.paid_from',
                'rent.name as trans_nat_type_text',
            ],
            'where' => [
                'owner_id' => ['column' => 'owner_id', 'compare' => '=', 'prefix' => 'p', 'value' => $options['owner_id']],
                'path' => ['column' => 'path', 'compare' => 'IS', 'prefix' => 'p', 'value' => 'NULL'],
                'contract_id' => ['column' => 'contract_id', 'compare' => 'IN', 'prefix' => 'p', 'value' => [$options['contract_id']]],
                'farming_year' => ['column' => 'farming_year', 'compare' => '=', 'prefix' => 'p', 'value' => $options['year']],
                'type' => ['column' => 'type', 'compare' => '=', 'prefix' => 't', 'value' => TRANSACTION_TYPE_PERSONAL_USE],
            ],
        ];

        return $this->getPaidData($paidOptions, false, false);
    }

    /**
     * @return bool
     */
    public function checkIfOwnerIsRecipientRko($result)
    {
        $recipientNames = preg_split('/\s+/', $result['recipient']);
        $ownerNames = preg_split('/\s+/', $result['owner_names']);
        $recipientCount = count($recipientNames);
        $ownerCount = count($ownerNames);
        for ($i = 0; $i < $recipientCount; $i++) {
            $recipientNames[$i] = strtolower($recipientNames[$i]);
        }
        for ($i = 0; $i < $ownerCount; $i++) {
            $ownerNames[$i] = strtolower($ownerNames[$i]);
        }
        $result = array_diff($recipientNames, $ownerNames);
        $result = array_filter($result, function ($item) {
            return !('' == $item);
        });

        return !(count($result) > 0 || (strtolower(trim($result['owner_egn'])) != strtolower(trim($result['recipient_egn']))));
    }

    /**
     * @return mixed|string
     */
    public function getPaymentSubject($id)
    {
        $UserDbController = new UserDbController($this->Database);
        $id = (int) $id;
        $options = [
            'tablename' => $UserDbController->DbHandler->tablePaymentSubjects,
            'where' => [
                'id' => ['column' => 'id', 'compare' => '=', 'value' => $id],
            ],
        ];
        $result = $UserDbController->getItemsByParams($options);

        return $result[0]['fulltext'];
    }

    /**
     * @param float $plotArea
     * @param float $usedArea
     * @param float $personalUseArea
     *
     * @return float|int
     */
    public function calculateProportionsOfPersonalUseArea($plotArea, $usedArea, $personalUseArea)
    {
        return ($plotArea / $usedArea) * $personalUseArea;
    }

    public function getPersonalUseDataByCrop($data)
    {
        $return = [];

        $options = [
            'return' => [
                'pur.renta_type as type',
                'pu.auto_crops_divide',
                'sum(pur.area) as area',
                'pur.average_yield',
                'pur.treatments_price',
                'pur.price_sum',
                'pur.renta_per_dka as amount',
                'pur.unit_value as unit_value',
            ],
            'where' => [
                'contract_id' => ['column' => 'contract_id', 'prefix' => 'cprel', 'compare' => '=', 'value' => $data['contract_id']],
                'owner_id' => ['column' => 'owner_id', 'prefix' => 'pu', 'compare' => '=', 'value' => $data['owner_id']],
            ],
            'year' => $data['year'],
            'group' => 'pu.auto_crops_divide,
                        pur.average_yield,
                        pur.treatments_price,
                        pur.price_sum,
                        pur.renta_type,
                        pur.renta_per_dka,
                        pur.unit_value,
                        pur.id',
        ];

        $results = $this->getPersonalUse($options, false, false);

        foreach ($results as $result) {
            $type = $result['type'] ?? 'no_nat_type';
            if (!$return[$type]) {
                $return[$type] = $result;
            } else {
                $return[$type]['area'] += $result['area'];
            }
        }

        return array_values($return);
    }

    public function calculatePersonalUseForOwner(&$resultByCont, $gridType = 'owner_payments')
    {
        if ('owner_payroll' == $gridType) {
            $resultByCont['renta_nat'] = explode('<br/>', rtrim($resultByCont['renta_nat_type'], '<br/>'));
            $resultByCont['charged_renta_nat'] = explode('</br>', $resultByCont['charged_renta_nat']);
            $resultByCont['unpaid_renta_nat'] = [];
        }

        // Find the renta per unit area
        $contractRenta = $resultByCont['renta'] / $resultByCont['owned_area'];
        $rentaMoney = $contractRenta * $resultByCont['area'];
        $resultByCont['renta'] = number_format($rentaMoney, 2, '.', '');

        // Find the charged renta per unit area
        if ('-' != $resultByCont['charged_renta'] && $resultByCont['charged_renta'] > 0) {
            $contractChargedRenta = $resultByCont['charged_renta'] / $resultByCont['charged_renta_area_with_pu'];
            $chargedRentaMoney = $contractChargedRenta * $resultByCont['charged_renta_area_without_pu'];
            $resultByCont['charged_renta'] = number_format($chargedRentaMoney, 2, '.', '');
        }

        $resultByCont['unpaid_renta'] = number_format((($chargedRentaMoney ?? $rentaMoney) - $resultByCont['paid_renta']), 2, '.', '');

        // Recalculate renta nat in case of personal use
        if (!empty($resultByCont['renta_nat'])) {
            foreach ($resultByCont['renta_nat'] as $key => $rentaNat) {
                $rentaNat = 0;
                if ('-' != $resultByCont['renta_nat'][$key] && $resultByCont['renta_nat'][$key] > 0) {
                    $contractRentaNat = $resultByCont['renta_nat'][$key] / $resultByCont['owned_area'];
                    $rentaNat = $contractRentaNat * $resultByCont['area'];
                    $resultByCont['renta_nat'][$key] = number_format($rentaNat, 3, '.', '');
                }

                if ('-' != $resultByCont['charged_renta_nat'][$key] && $resultByCont['charged_renta_nat'][$key] > 0) {
                    $chargedRentaNat = $resultByCont['charged_renta_nat'][$key] / $resultByCont['owned_area'];
                    $rentaNat = $chargedRentaNat * $resultByCont['area'];
                    $resultByCont['charged_renta_nat'][$key] = number_format($rentaNat, 3, '.', '');
                }

                $paidRentaNat = 0;
                if ('-' != $resultByCont['paid_renta_nat'][$key] && $resultByCont['paid_renta_nat'][$key] > 0) {
                    $paidRentaNat = $resultByCont['paid_renta_nat'][$key];
                }
                $resultByCont['unpaid_renta_nat'][$key] = number_format(($rentaNat - $paidRentaNat), 3, '.', '');
            }

            $resultByCont['renta_nat_text'] = implode('</br>', array_map(function ($value) {
                return number_format($value, 3, '.', '');
            }, $resultByCont['renta_nat']));

            $resultByCont['charged_renta_nat_text'] = implode('</br>', array_map(function ($value) {
                return number_format($value, 3, '.', '');
            }, $resultByCont['charged_renta_nat']));

            $resultByCont['unpaid_renta_nat_text'] = implode('</br>', array_map(function ($value) {
                return number_format($value, 3, '.', '');
            }, $resultByCont['unpaid_renta_nat']));

            if ('owner_payroll' == $gridType) {
                $resultByCont['charged_renta_nat'] = implode('</br>', array_map(function ($value) {
                    return number_format($value, 3, '.', '');
                }, $resultByCont['charged_renta_nat']));

                $resultByCont['plot_unpaid_renta'] = $resultByCont['unpaid_renta'];
                $resultByCont['plot_unpaid_renta_nat'] = $resultByCont['unpaid_renta_nat_text'];
            }
        }
    }

    public function calculatePersonalUseByPlot(&$owners, $personalUses)
    {
        foreach ($owners as &$owner) {
            foreach ($personalUses as $personalUse) {
                if ($owner['contract_id'] == $personalUse['contract_id'] && $owner['gid'] == $personalUse['plot_id']) {
                    $owner['pu_area'] = $personalUse['plot_pu_area'];
                    $owner['area'] = number_format(($owner['area'] - $personalUse['plot_pu_area']), 3);
                    $owner['renta'] = number_format(($owner['area'] * $owner['contract_renta']), 2);
                    $owner['charged_renta'] = number_format(($owner['area'] * $owner['contract_renta']), 2);

                    $owner['plot_unpaid_renta'] = number_format((($owner['charged_renta'] ? $owner['charged_renta'] : $owner['renta']) - $owner['paid_renta']), 2);
                    $rentaNatsTypes = explode('<br/>', rtrim($owner['renta_nat_type'], '<br/>'));

                    if (!empty($owner['renta_nat']) && '-</br>-' !== $owner['renta_nat']) {
                        $rentaNatsNewValues = [];

                        $rentaNats = explode('</br>', $owner['renta_nat']);
                        $unpaidNatRents = explode('</br>', $owner['plot_unpaid_renta_nat']);
                        $paidRentaNats = array_values($owner['paid_renta_nat_details']);
                        foreach ($rentaNats as $key => $rentaNat) {
                            $rentaNat = $rentaNat / $owner['contract_area'];
                            $newRentaNat = $owner['area'] * $rentaNat;
                            $rentaNatsNewValues[] = number_format($newRentaNat, 2);

                            $unpaidNatRentsCalculated[] = number_format($newRentaNat - $paidRentaNats[$key], 3) . ' X ' . $rentaNatsTypes[$key];
                        }
                        $owner['renta_nat'] = implode('</br>', $rentaNatsNewValues);
                        $owner['plot_unpaid_renta_nat'] = implode('</br>', $unpaidNatRentsCalculated);
                    }

                    if (!empty($owner['charged_renta_nat'])) {
                        $rentaNatsNewValues = [];
                        $rentaNats = explode('</br>', $owner['charged_renta_nat']);

                        $unpaidNatRentsCalculated = [];
                        $paidRentaNats = array_values($owner['paid_renta_nat_details']);

                        foreach ($rentaNats as $k => $rentaNat) {
                            $rentaNat = $rentaNat / $owner['contract_area'];
                            $newRentaNat = $owner['area'] * $rentaNat;
                            $rentaNatsNewValues[] = number_format($newRentaNat, 2);

                            $unpaidNatRentsCalculated[] = number_format(($newRentaNat - $paidRentaNats[$k]), 3) . ' X ' . $rentaNatsTypes[$k];
                        }
                        $owner['charged_renta_nat'] = implode('</br>', $rentaNatsNewValues);
                        $owner['plot_unpaid_renta_nat'] = implode('</br>', $unpaidNatRentsCalculated);
                    }

                    if (!empty($rentaNatsNewValues)) {
                    }
                }
            }
        }
    }

    public function calculatePersonalUse(&$ownersTree, $personalUse, $calcType)
    {
        // Calculate owner areas after subtracting the personal use area and return owners levels tree
        $levelsTree = $this->personalUseRecalculateAreasAndSetInitData($ownersTree, $personalUse, $levelsTree, 1, null, null, $calcType);

        // Calculate heritors owned percent
        $levelsTree = $this->personalUseCalculateOwnedPercent($ownersTree, $levelsTree, 1, null, null, $calcType);

        // Calculate rents after subtracting the personal use area
        $this->personalUseRecalculateRents($ownersTree, $levelsTree, 1, null, null, $calcType);
    }

    public function hasPaymentRestriction($contractId, $additionalParams = [])
    {
        $paymentsOptions = [
            'return' => [
                'max(c.c_num) as c_num',
                'max(p.farming_year) as farming_year',
                'max(round(p.amount::numeric, 2)) as amount',
                'max(t.id) as transaction_id',
                'max(bank_acc) as bank_acc',
                'max(recipient) as recipient',
                "string_agg(round(pn.amount::numeric, 2)::text || ' ' || (case when srt.unit = 1 then 'кг.' when srt.unit = 2 then 'л.' else 'бр.' end) || ' ' || srt.name, '<br>') FILTER (WHERE pn.amount IS NOT NULL) as nat_payments",
                "'payment' as transaction_type",
            ],
            'custom_counter' => 'count(DISTINCT(p.id))',
            'where' => [
                'contract_id' => ['column' => 'id', 'compare' => '=', 'prefix' => 'c', 'value' => $contractId],
            ],
            'group' => 'p.id, c.id, o.id',
            'joins' => [
                'left join su_renta_types srt on srt.id = pn.nat_type',
            ],
        ];

        if (isset($additionalParams['parent_id']) && !empty($additionalParams['parent_id'])) {
            $farmingYears = getFarmingYearsInPeriod($additionalParams['start_date'], $additionalParams['due_date']);
            $paymentsOptions['where']['contract_id'] = ['column' => 'id', 'compare' => '=', 'prefix' => 'c', 'value' => $additionalParams['parent_id']];
            $paymentsOptions['where']['farming_year'] = ['column' => 'farming_year', 'compare' => 'IN', 'prefix' => 'p', 'value' => array_keys($farmingYears)];
        }

        $contractPayments = $this->getPaymentsByParams($paymentsOptions, false, false);

        $collectionsOptions = [
            'return' => [
                'max(c.c_num) as c_num',
                'max(coll.farming_year) as farming_year',
                'max(round(coll.amount::numeric, 2)) as amount',
                'max(coll.id) as transaction_id',
                'null as bank_acc',
                'max(recieved_from) as recipient',
                'null as nat_payments',
                "'collection' as transaction_type",
            ],
            'where' => [
                'contract_id' => ['column' => 'contract_id', 'compare' => '=', 'prefix' => 'coll', 'value' => $contractId],
                'status' => ['column' => 'status', 'compare' => '=', 'prefix' => 'coll', 'value' => true],
            ],
            'group' => 'coll.id, c.id',
        ];

        if (isset($additionalParams['parent_id']) && !empty($additionalParams['parent_id'])) {
            $farmingYears = getFarmingYearsInPeriod($additionalParams['start_date'], $additionalParams['due_date']);
            $paymentsOptions['where']['contract_id'] = ['column' => 'contract_id', 'compare' => '=', 'prefix' => 'coll', 'value' => $additionalParams['parent_id']];
            $collectionsOptions['where']['farming_year'] = ['column' => 'farming_year', 'compare' => 'IN', 'prefix' => 'coll', 'value' => array_keys($farmingYears)];
        }

        $contractCollections = $this->getCollectionsByParams($collectionsOptions, false, false);

        $payments = array_merge($contractPayments, $contractCollections);

        if (count($payments) > 0) {
            $payments = array_map(function ($payment) {
                $payment['farming_year'] = $GLOBALS['Farming']['years'][$payment['farming_year']]['farming_year_short'];

                return $payment;
            }, $payments);

            throw new MTRpcException($payments, self::CONTRACTS_HAS_PAYMENTS_EXCEPTION);
        }
    }

    private function personalUseRecalculateAreasAndSetInitData(&$ownersTree, $personalUse, &$levelsTree = [], $level = 1, $parentId = null, $parentContractsKey = null, $calcType)
    {
        foreach ($ownersTree as $key => &$owner) {
            $ownerKey = $parentId ? $parentId . '-' . $owner['owner_id'] : $owner['owner_id'];
            if ('payments' == $calcType) {
                $ownerArea = $owner['owner_area'];
                $ownerContractIds = [];
                $ownerContractsKey = '0';
                $owner['all_owner_area'] = $ownerArea;
                $personalUse = $owner['personal_use'];
            } elseif ('payroll' == $calcType) {
                $ownerArea = $owner['area'];
                if (!empty($parentContractsKey)) {
                    $ownerContractsKey = $parentContractsKey;
                    $ownerContractIds = explode('_', $parentContractsKey);
                } else {
                    $ownerContractIds = $owner['contract_array'];
                    if (!is_array($owner['contract_array'])) {
                        $ownerContractIds = array_unique(explode(',', trim($owner['contract_array'], '{}')));
                    }

                    $ownerContractsKey = implode('_', $ownerContractIds);
                }

                $personalUse = $this->getPersonalUseForOwnerAndContract($owner['owner_id'], $ownerContractIds, $_POST['farming_year']);
            }
            $levelsTree[$level][$ownerKey][$ownerContractsKey]['owner_names'] = $owner['owner_names'];
            $levelsTree[$level][$ownerKey][$ownerContractsKey]['owner_id'] = $owner['owner_id'];
            $levelsTree[$level][$ownerKey][$ownerContractsKey]['original_area'] = $ownerArea;
            $levelsTree[$level][$ownerKey][$ownerContractsKey]['parent_id'] = $parentId;
            $levelsTree[$level][$ownerKey][$ownerContractsKey]['contract_ids'] = $ownerContractIds;
            $levelsTree[$level][$ownerKey][$ownerContractsKey]['plots_contracts_charged_renta'] = $owner['plots_contracts_charged_renta'];
            $levelsTree[$level][$ownerKey][$ownerContractsKey]['plots_contracts_area_array_gids'] = $owner['plots_contracts_area_array_gids'];

            $levelsTree[$level][$ownerKey][$ownerContractsKey]['heritor_percent'] = 1;

            if ($parentId) {
                $levelsTree[$level][$ownerKey][$ownerContractsKey]['heritor_percent'] = $ownerArea / $levelsTree[$level - 1][$parentId][$ownerContractsKey]['original_area'];
                $levelsTree[$level][$ownerKey][$ownerContractsKey]['plots_contracts_charged_renta'] = $levelsTree[$level - 1][$parentId][$ownerContractsKey]['plots_contracts_charged_renta'];
                $levelsTree[$level][$ownerKey][$ownerContractsKey]['plots_contracts_area_array_gids'] = $levelsTree[$level - 1][$parentId][$ownerContractsKey]['plots_contracts_area_array_gids'];

                foreach ($levelsTree[$level][$ownerKey][$ownerContractsKey]['plots_contracts_area_array_gids'] as $k => $contractArea) {
                    $levelsTree[$level][$ownerKey][$ownerContractsKey]['plots_contracts_area_array_gids'][$k]['area'] = $contractArea['area'] * $levelsTree[$level][$ownerKey][$ownerContractsKey]['heritor_percent'];
                }
            }

            // Set owner rentas in levels tree
            if ($owner['renta']) {
                if ('payroll' == $calcType) {
                    $moneyPerContract = [];
                    foreach ($owner['plots_contracts_renta'] as $rentaByContract) {
                        if (!isset($moneyPerContract[$rentaByContract['contract_id']])) {
                            $moneyPerContract[$rentaByContract['contract_id']] = 0;
                        }
                        $moneyPerContract[$rentaByContract['contract_id']] += $rentaByContract['renta_by_plot'];
                    }

                    foreach ($moneyPerContract as $contractId => $contractMoney) {
                        if (0 == $owner['total_area_contract'][$contractId]) {
                            continue;
                        }

                        $mRent = 0;
                        if ($owner['total_area_contract'][$contractId] > 0) {
                            $mRent = $contractMoney / $owner['total_area_contract'][$contractId];
                        }

                        $levelsTree[$level][$ownerKey][$ownerContractsKey]['contract_info'][$contractId]['renta_money'] = $mRent;
                        $levelsTree[$level][$ownerKey][$ownerContractsKey]['contract_info'][$contractId]['area'] = $owner['total_area_contract'][$contractId];
                        $levelsTree[$level][$ownerKey][$ownerContractsKey]['contract_info'][$contractId]['is_charged'] = array_key_exists($contractId, $owner['plots_contracts_charged_renta_down_grid']);
                        $levelsTree[$level][$ownerKey][$ownerContractsKey]['contract_info'][$contractId]['pu_area'] = 0;
                        $levelsTree[$level][$ownerKey][$ownerContractsKey]['contract_info'][$contractId]['area_without_pu'] = $owner['total_area_contract'][$contractId];
                    }

                    $levelsTree[$level][$ownerKey][$ownerContractsKey]['renta_money'] = $owner['renta'] / $ownerArea;
                } else {
                    if ($parentId) {
                        // Can't charge renta for heritors so we get the renta direct from parent
                        $levelsTree[$level][$ownerKey][$ownerContractsKey]['renta_money'] = $levelsTree[$level - 1][$parentId][$ownerContractsKey]['renta_money'];
                        $levelsTree[$level][$ownerKey][$ownerContractsKey]['contract_renta'] = $levelsTree[$level - 1][$parentId][$ownerContractsKey]['contract_renta'] * $levelsTree[$level][$ownerKey][$ownerContractsKey]['heritor_percent'];
                    } else {
                        $levelsTree[$level][$ownerKey][$ownerContractsKey]['renta_money'] = $owner['renta'] / $ownerArea;
                        $levelsTree[$level][$ownerKey][$ownerContractsKey]['contract_renta'] = $owner['contract_renta'] * $levelsTree[$level][$ownerKey][$ownerContractsKey]['heritor_percent'];
                    }
                }
            }
            if ($owner['renta_nat']) {
                if ('payroll' == $calcType) {
                    $natPerContract = [];
                    foreach ($owner['renta_nat'] as $natType => $rentaNatValue) {
                        foreach ($owner['plots_contracts_renta_nat'] as $contractIdNat => $plotsNat) {
                            foreach ($plotsNat as $plotNat) {
                                foreach ($plotNat as $natTypeId => $natQty) {
                                    if ($natType == $natTypeId) {
                                        $natPerContract[$contractIdNat][$natTypeId] = $natQty;
                                    }
                                }
                            }
                        }
                    }
                    foreach ($natPerContract as $cId => $contractNats) {
                        foreach ($contractNats as $natId => $contractNat) {
                            $nRent = 0;
                            if ($owner['total_area_contract'][$cId] > 0) {
                                $nRent = $contractNat / $owner['total_area_contract'][$cId];
                            }
                            $levelsTree[$level][$ownerKey][$ownerContractsKey]['contract_info'][$cId]['renta_nat'][$natId]['renta'] = $nRent;
                            $levelsTree[$level][$ownerKey][$ownerContractsKey]['contract_info'][$cId]['renta_nat'][$natId]['is_charged'] = array_key_exists($cId, $owner['plots_contracts_charged_renta_nat_down_grid']);
                            $levelsTree[$level][$ownerKey][$ownerContractsKey]['contract_info'][$cId]['area'] = $owner['total_area_contract'][$cId];
                            $levelsTree[$level][$ownerKey][$ownerContractsKey]['contract_info'][$cId]['pu_area'] = 0;
                            $levelsTree[$level][$ownerKey][$ownerContractsKey]['contract_info'][$cId]['area_without_pu'] = $owner['total_area_contract'][$cId];
                        }
                    }
                } else {
                    if ($parentId) {
                        // Can't charge renta for heritors so we get the renta direct from parent
                        $levelsTree[$level][$ownerKey][$ownerContractsKey]['renta_nat'] = $levelsTree[$level - 1][$parentId][$ownerContractsKey]['renta_nat'];
                    } else {
                        foreach ($owner['renta_nat'] as $natType => $rentaNatValue) {
                            if ($rentaNatValue > 0) {
                                $levelsTree[$level][$ownerKey][$ownerContractsKey]['renta_nat'][$natType] = $rentaNatValue / $ownerArea;
                            }
                        }
                    }
                }
            }

            if ($owner['charged_renta_nat']) {
                if ($parentId) {
                    // Can't charge renta for heritors and get the renta direct from parent
                    $levelsTree[$level][$ownerKey][$ownerContractsKey]['charged_renta_nat'] = $levelsTree[$level - 1][$parentId][$ownerContractsKey]['charged_renta_nat'];
                } else {
                    foreach ($owner['charged_renta_nat'] as $natType => $rentaNatValue) {
                        if ($rentaNatValue > 0) {
                            $levelsTree[$level][$ownerKey][$ownerContractsKey]['charged_renta_nat'][$natType] = $rentaNatValue / $ownerArea;
                        }
                    }
                }
            }
            // Set owner paid renta in levels tree
            $levelsTree[$level][$ownerKey][$ownerContractsKey]['paid_renta_money'] = $owner['paid_renta'] ?? 0;
            $levelsTree[$level][$ownerKey][$ownerContractsKey]['parent_paid_rent_money'] = 0;
            $levelsTree[$level][$ownerKey][$ownerContractsKey]['paid_renta_nat'] = [];

            if ($owner['paid_renta_nat_details']) {
                $levelsTree[$level][$ownerKey][$ownerContractsKey]['paid_renta_nat'] = $owner['paid_renta_nat_details'];
            }

            // All paid money renta of all parents plus the paid money renta of the owner
            $levelsTree[$level][$ownerKey][$ownerContractsKey]['all_paid_rent_money'] = ($levelsTree[$level - 1][$parentId][$ownerContractsKey]['all_paid_rent_money'] ?? 0) + $owner['paid_renta'];

            // Parent paid money renta
            if ($parentId) {
                $parentPaidRentaMoney = ($levelsTree[$level - 1][$parentId][$ownerContractsKey]['paid_renta_money'] + $levelsTree[$level - 1][$parentId][$ownerContractsKey]['parent_paid_rent_money']);
                $levelsTree[$level][$ownerKey][$ownerContractsKey]['parent_paid_rent_money'] = (round(($parentPaidRentaMoney), 2) ?? 0);
            }

            // All paid nature renta of all parents plus the paid nature renta of the owner
            if ($owner['paid_renta_nat_details']) {
                foreach ($owner['paid_renta_nat_details'] as $natType => $paidValue) {
                    $levelsTree[$level][$ownerKey][$ownerContractsKey]['all_paid_rent_nat'][$natType] = ($levelsTree[$level - 1][$parentId][$ownerContractsKey]['all_paid_rent_nat'][$natType] ?? 0) + $paidValue;
                }
            } else {
                $levelsTree[$level][$ownerKey][$ownerContractsKey]['all_paid_rent_nat'] = ($levelsTree[$level - 1][$parentId][$ownerContractsKey]['all_paid_rent_nat'] ?? []);
            }
            if (!empty($owner['children'])) {
                $this->personalUseRecalculateAreasAndSetInitData($owner['children'], $personalUse, $levelsTree, ($level + 1), $ownerKey, $ownerContractsKey, $calcType);
            }
            $puIds = [];
            foreach ($personalUse as $personalUseItem) {
                $puKey = $personalUseItem['owner_id'] . '_' . $personalUseItem['contract_id'];
                if (in_array($puKey, $puIds)) {
                    continue;
                }
                if ($owner['owner_id'] == $personalUseItem['owner_id']
                && (empty($ownerContractIds) || in_array($personalUseItem['contract_id'], $ownerContractIds))
                ) {
                    if ('payroll' == $calcType) {
                        $ownerAreaPuContract = $ownerArea;
                        if (!empty($owner['total_area_contract'])) {
                            $ownerAreaPuContract = $owner['total_area_contract'][$personalUseItem['contract_id']];
                        }

                        $totalPersonalUseOwnerArea = $personalUseItem['total_personal_use_area'];
                        $personalUsePart = $ownerAreaPuContract / $personalUseItem['total_owned_area'];
                        $personalUseArea = $totalPersonalUseOwnerArea * $personalUsePart;

                        // Subtract personal use area from owner area
                        if ($ownerArea) {
                            $ownerArea -= round($personalUseArea, 3);
                            $owner['pu_area'] += round($personalUseArea, 3);
                            $levelsTree[$level][$ownerKey][$ownerContractsKey]['contract_info'][$personalUseItem['contract_id']]['pu_area'] = round($personalUseArea, 3);
                            $levelsTree[$level][$ownerKey][$ownerContractsKey]['contract_info'][$personalUseItem['contract_id']]['area_without_pu'] = round(($levelsTree[$level][$ownerKey][$ownerContractsKey]['contract_info'][$personalUseItem['contract_id']]['area'] - $personalUseArea), 3);
                        }
                    } else {
                        $ownerAreaPuContract = $ownerArea;
                        if (!empty($owner['total_area_contract'])) {
                            $ownerAreaPuContract = $owner['total_area_contract'][$personalUseItem['contract_id']];
                        }

                        $totalPersonalUseOwnerArea = $personalUseItem['total_personal_use_area'];
                        $personalUsePart = $ownerAreaPuContract / $personalUseItem['total_owned_area'];
                        $personalUseArea = $totalPersonalUseOwnerArea * $personalUsePart;

                        $levelsTree[$level][$ownerKey][$ownerContractsKey]['total_personal_use_area'] = $personalUsePart;

                        // Subtract personal use area from owner area
                        if ($ownerArea) {
                            $ownerArea -= round($personalUseArea, 3);
                            $owner['pu_area'] += round($personalUseArea, 3);
                        }
                    }
                }
                $puIds[] = $puKey;
            }

            // Store the values from heritors which will be subtracted from the parent values
            $levelsTree[$level][$ownerKey][$ownerContractsKey]['calculated_area'] = $ownerArea;
            $levelsTree[$level][$ownerKey][$ownerContractsKey]['pu_area'] = $owner['pu_area'];
            $levelsTree[$level][$ownerKey][$ownerContractsKey]['total_personal_use_area'] = $totalPersonalUseOwnerArea;
            $levelsTree[$level][$ownerKey][$ownerContractsKey]['heritors_paid_renta_money'] = 0;
            $levelsTree[$level][$ownerKey][$ownerContractsKey]['heritors_paid_renta_nat'] = [];

            if ($owner['is_dead']) {
                $ownerArea = 0;
                $childrenPersonalUse = 0;
                $childrenPaidRenta = 0;
                $childrenPaidRentaNat = [];
                $childrenPersonalUseAreaPerContract = [];
                foreach ($levelsTree[$level + 1] as $heritor) {
                    foreach ($heritor as $heritorContractKey => $heritorContract) {
                        if ($heritorContractKey == $ownerContractsKey) {
                            $ownerArea += $heritorContract['calculated_area'];
                            $childrenPersonalUse += $heritorContract['pu_area'];
                            $childrenPaidRenta += $heritorContract['heritors_paid_renta_money'] + $heritorContract['paid_renta_money'];

                            foreach ($heritorContract['paid_renta_nat'] as $natType => $paidValue) {
                                if (empty($childrenPaidRentaNat[$natType])) {
                                    $childrenPaidRentaNat[$natType] += $paidValue;
                                } else {
                                    $childrenPaidRentaNat[$natType] = $paidValue;
                                }
                            }

                            foreach ($heritorContract['pu_area_per_contract'] as $contractId => $poAreaPerContract) {
                                $childrenPersonalUseAreaPerContract[$contractId] += $poAreaPerContract;
                            }
                        }
                    }
                }

                $levelsTree[$level][$ownerKey][$ownerContractsKey]['calculated_area'] = $ownerArea;
                $levelsTree[$level][$ownerKey][$ownerContractsKey]['pu_area'] = $childrenPersonalUse;
                $levelsTree[$level][$ownerKey][$ownerContractsKey]['pu_area_per_contract'] = $childrenPersonalUseAreaPerContract;
                $levelsTree[$level][$ownerKey][$ownerContractsKey]['heritors_paid_renta_money'] = round($childrenPaidRenta, 2);
                $levelsTree[$level][$ownerKey][$ownerContractsKey]['heritors_paid_renta_nat'] = $childrenPaidRentaNat;

                $owner['pu_area'] = $childrenPersonalUse;
            }

            if (trim($owner['charged_renta'], '-')) {
                if ('payroll' == $calcType) {
                    $moneyPerContract = [];
                    foreach ($owner['plots_contracts_renta'] as $rentaByContract) {
                        if (!isset($moneyPerContract[$rentaByContract['contract_id']])) {
                            $moneyPerContract[$rentaByContract['contract_id']] = 0;
                        }
                        $moneyPerContract[$rentaByContract['contract_id']] += $rentaByContract['renta_by_plot'];
                    }

                    foreach ($moneyPerContract as $contractId => $contractMoney) {
                        $cRenta = 0;
                        if ($owner['total_area_contract'][$contractId] > 0) {
                            $cRenta = $contractMoney / $owner['total_area_contract'][$contractId];
                        }
                        $levelsTree[$level][$ownerKey][$ownerContractsKey]['contract_info'][$contractId]['renta_money'] = $cRenta;
                        $levelsTree[$level][$ownerKey][$ownerContractsKey]['contract_info'][$contractId]['area'] = $owner['total_area_contract'][$contractId];
                    }

                    foreach ($owner['plots_contracts_charged_renta'] as $chargedRentaPlotInfo) {
                        $chargedRentaPlotsArea = 0;
                        $puArea = 0;
                        foreach ($owner['plots_contracts_area_array_gids'] as $plotInfo) {
                            if ($plotInfo['plot_gid'] == $chargedRentaPlotInfo['plot_gid']) {
                                foreach ($personalUse as $puByRentaType) {
                                    $puPlotsAreas = json_decode($puByRentaType['personal_use_plots_area'], true);
                                    foreach ($puPlotsAreas as $puPlotArea) {
                                        if ($puPlotArea['pc_rel_id'] == $plotInfo['rel_id']) {
                                            $puArea += $puPlotArea['area'];
                                            $plotInfo['area'] -= $puPlotArea['area'];
                                        }
                                    }
                                }

                                $chargedRentaPlotsArea += $plotInfo['area'];
                            }
                        }
                        $levelsTree[$level][$ownerKey][$ownerContractsKey]['contract_info'][$chargedRentaPlotInfo['contract_id']]['charged_renta_money'] = $owner['charged_renta'] / ($chargedRentaPlotsArea + $puArea);
                        $levelsTree[$level][$ownerKey][$ownerContractsKey]['contract_info'][$chargedRentaPlotInfo['contract_id']]['charged_renta_area'] = $chargedRentaPlotsArea;
                    }
                } else {
                    if ($parentId) {
                        // Ако има родител
                        if ($personalUse) {
                            $chargedRentaPUPlotsArea = 0;
                            $chargedRentaMoney = 0;
                            $puArea = 0;
                            foreach ($levelsTree[$level][$ownerKey][$ownerContractsKey]['plots_contracts_charged_renta'] as $chargedRentaPlotInfo) {
                                foreach ($levelsTree[$level][$ownerKey][$ownerContractsKey]['plots_contracts_area_array_gids'] as $plotInfo) {
                                    if ($plotInfo['plot_gid'] == $chargedRentaPlotInfo['plot_gid']) {
                                        echo '===== DEBUG =====> ' . print_r($levelsTree[$level][$ownerKey][$ownerContractsKey], true) . ' <====';
                                        echo '===== DEBUG =====> ' . print_r($owner, true) . ' <====';
                                        echo '===== DEBUG =====> ' . print_r($plotInfo, true) . ' <====';
                                        echo '===== DEBUG =====> ' . print_r($chargedRentaPlotInfo, true) . ' <====';
                                        exit;
                                        foreach ($personalUse as $puByRentaType) {
                                            $puPlotsAreas = json_decode($puByRentaType['personal_use_plots_area'], true);
                                            foreach ($puPlotsAreas as $puPlotArea) {
                                                if ($puPlotArea['pc_rel_id'] == $plotInfo['rel_id']) {
                                                    $puCoef = $levelsTree[$level][$ownerKey][$ownerContractsKey]['pu_area'] / $levelsTree[$level][$ownerKey][$ownerContractsKey]['total_personal_use_area'];
                                                    $puCalculated = $puPlotArea['area'] * $puCoef;
                                                    $puArea += $puCalculated;

                                                    $plotInfo['area'] -= $puCalculated;
                                                }
                                            }
                                        }

                                        $chargedRentaPUPlotsArea += $plotInfo['area'];
                                    }
                                }
                            }

                            // Can't charge renta for heritors and we get the renta direct from parent
                            if (!$levelsTree[$level][$ownerKey][$ownerContractsKey]['has_children']) {
                                $levelsTree[$level][$ownerKey][$ownerContractsKey]['charged_renta_money'] = ($owner['charged_renta']) / ($chargedRentaPUPlotsArea + $puArea);
                                $levelsTree[$level][$ownerKey][$ownerContractsKey]['charged_renta_area'] = $chargedRentaPUPlotsArea;
                            }
                        } else {
                            $chargedRentaPlotsArea = 0;
                            foreach ($levelsTree[$level][$ownerKey][$ownerContractsKey]['plots_contracts_charged_renta'] as $chargedRentaPlotInfo) {
                                foreach ($levelsTree[$level][$ownerKey][$ownerContractsKey]['plots_contracts_area_array_gids'] as $plotInfo) {
                                    if ($plotInfo['plot_gid'] == $chargedRentaPlotInfo['plot_gid']) {
                                        $chargedRentaPlotsArea += $plotInfo['area'];
                                    }
                                }
                            }

                            if (!$levelsTree[$level][$ownerKey][$ownerContractsKey]['has_children']) {
                                $levelsTree[$level][$ownerKey][$ownerContractsKey]['charged_renta_area'] = $chargedRentaPlotsArea;
                                $levelsTree[$level][$ownerKey][$ownerContractsKey]['charged_renta_money'] = $owner['charged_renta'] / $chargedRentaPlotsArea;
                            }
                        }

                        $levelsTree[$level - 1][$parentId][$ownerContractsKey]['has_children'] = true;

                        if ('Тодор Мани Маниев' == $levelsTree[$level][$ownerKey][$ownerContractsKey]['owner_names']) {
                            // echo '===== DEBUG222 =====> ' . print_r($owner, true) . ' <====';
                            echo '===== DEBUG222 =====> ' . print_r($levelsTree[$level][$ownerKey][$ownerContractsKey]['plots_contracts_charged_renta'], true) . ' <====';
                            // echo '===== DEBUG222 =====> ' . print_r($parentRentaSum, true) . ' <====';
                            // echo '===== DEBUG222 =====> ' . print_r($ownerRentasSum, true) . ' <====';
                            // echo '===== DEBUG222 =====> ' . print_r($parentRentaSum, true) . ' <====';
                            exit;
                        }

                        if ($levelsTree[$level - 1][$parentId][$ownerContractsKey]['has_children']) {
                            $levelsTree[$level - 1][$parentId][$ownerContractsKey]['charged_renta_money'] = $levelsTree[$level][$ownerKey][$ownerContractsKey]['charged_renta_money'];
                            $levelsTree[$level - 1][$parentId][$ownerContractsKey]['charged_renta_area'] += $levelsTree[$level][$ownerKey][$ownerContractsKey]['charged_renta_area'];
                        }
                    } else {
                        // Ако няма родител
                        $chargedRentaPlotsArea = 0;
                        $puArea = 0;
                        foreach ($owner['plots_contracts_charged_renta'] as $chargedRentaPlotInfo) {
                            foreach ($owner['plots_contracts_area_array_gids'] as $plotInfo) {
                                if ($plotInfo['plot_gid'] == $chargedRentaPlotInfo['plot_gid']) {
                                    foreach ($personalUse as $puByRentaType) {
                                        $puPlotsAreas = json_decode($puByRentaType['personal_use_plots_area'], true);
                                        foreach ($puPlotsAreas as $puPlotArea) {
                                            if ($puPlotArea['pc_rel_id'] == $plotInfo['rel_id']) {
                                                $puCoef = $levelsTree[$level][$ownerKey][$ownerContractsKey]['pu_area'] / $levelsTree[$level][$ownerKey][$ownerContractsKey]['total_personal_use_area'];
                                                $puCalculated = $puPlotArea['area'] * $puCoef;
                                                $puArea += $puCalculated;

                                                $plotInfo['area'] -= $puCalculated;
                                            }
                                        }
                                    }

                                    $chargedRentaPlotsArea += $plotInfo['area'];
                                }
                            }
                        }

                        if (!$levelsTree[$level][$ownerKey][$ownerContractsKey]['has_children']) {
                            $levelsTree[$level][$ownerKey][$ownerContractsKey]['charged_renta_money'] = $owner['charged_renta'] / ($chargedRentaPlotsArea + $puArea);
                            $levelsTree[$level][$ownerKey][$ownerContractsKey]['charged_renta_area'] = $chargedRentaPlotsArea;
                        }

                        if ($levelsTree[$level - 1][$parentId][$ownerContractsKey]['has_children']) {
                            $levelsTree[$level - 1][$parentId][$ownerContractsKey]['charged_renta_money'] = $levelsTree[$level][$ownerKey][$ownerContractsKey]['charged_renta_money'];
                            $levelsTree[$level - 1][$parentId][$ownerContractsKey]['charged_renta_area'] += $levelsTree[$level][$ownerKey][$ownerContractsKey]['charged_renta_area'];
                        }
                    }
                }
            }

            if ('payments' == $calcType) {
                $owner['owner_area'] = number_format($ownerArea, 3, '.', '');
                $owner['pu_area'] = number_format($owner['pu_area'], 3, '.', '');
            } elseif ('payroll' == $calcType) {
                $owner['area'] = number_format($ownerArea, 3, '.', '');
                $owner['pu_area'] = number_format($owner['pu_area'], 3, '.', '');
            }
        }

        return $levelsTree;
    }

    private function personalUseCalculateOwnedPercent(&$ownersTree, &$levelsTree = [], $level = 1, $parentId = null, $parentContractsKey = null, $calcType)
    {
        foreach ($ownersTree as $key => &$owner) {
            $ownerKey = $parentId ? $parentId . '-' . $owner['owner_id'] : $owner['owner_id'];

            if ('payments' == $calcType) {
                $ownerArea = $owner['owner_area'];
                $ownerContractsKey = '0';
            } elseif ('payroll' == $calcType) {
                $ownerArea = $owner['area'];
                if (!empty($parentContractsKey)) {
                    $ownerContractsKey = $parentContractsKey;
                    $ownerContractIds = explode('_', $parentContractsKey);
                } else {
                    $ownerContractIds = $owner['contract_array'];
                    if (!is_array($owner['contract_array'])) {
                        $ownerContractIds = array_unique(explode(',', trim($owner['contract_array'], '{}')));
                    }

                    $ownerContractsKey = implode('_', $ownerContractIds);
                }
            }

            // Calculate parent:heritor atitude and set it in levels tree
            $levelsTree[$level][$ownerKey][$ownerContractsKey]['owned_percent'] = 1;
            $parentAreaCalculated = $levelsTree[$level - 1][$parentId][$ownerContractsKey]['calculated_area'];
            $ownedPercentCalculated = $ownerArea / $parentAreaCalculated;
            if (!empty($parentAreaCalculated)) {
                $levelsTree[$level][$ownerKey][$ownerContractsKey]['owned_percent'] = $ownedPercentCalculated;
            }

            if ($parentId) {
                // get charged_renta_money from parent
                $levelsTree[$level][$ownerKey][$ownerContractsKey]['charged_renta_money'] = $levelsTree[$level - 1][$parentId][$ownerContractsKey]['charged_renta_money'];
            }

            if (!empty($owner['children'])) {
                $ownerContractIds = explode('_', $parentContractsKey);
                $this->personalUseCalculateOwnedPercent($owner['children'], $levelsTree, ($level + 1), $ownerKey, $ownerContractsKey, $calcType);
            }
        }

        return $levelsTree;
    }

    /**
     * Recursive function that interate from buttom to the top of the owners tree and calculate rents, unpaid rents and overpaid rents.
     *
     * @param null|mixed $parentId
     * @param null|mixed $ownerContractsKey
     * @param null|mixed $parentContractsKey
     */
    private function personalUseRecalculateRents(&$ownersTree, &$levelsTree = [], $level = 1, $parentId = null, $parentContractsKey = null, $calcType)
    {
        $UserDbController = new UserDbController($this->Database);
        $rentaNatResults = $UserDbController->getItemsByParams([
            'tablename' => $UserDbController->DbHandler->tableRentaTypes,
        ], false, false);
        $rentaNatTypeValues = [];
        $rentaNatTypeNames = [];
        foreach ($rentaNatResults as $key => $renta) {
            $rentaNatTypeValues[$renta['id']] = $renta['unit_value'];
            $rentaNatTypeNames[$renta['id']] = $renta['name'] . '(' . $GLOBALS['Contracts']['renta_units'][$renta['unit']]['name'] . ')';
        }
        foreach ($ownersTree as $key => &$owner) {
            $ownerKey = $parentId ? $parentId . '-' . $owner['owner_id'] : $owner['owner_id'];
            // Payments and Payroll have different structure of owner array so we need to map the values
            if ('payments' == $calcType) {
                $unpaidRentaNats = $owner['unpaid_renta_nat_details'];
                $unpaidRentaNatValues = $owner['unpaid_renta_nat_unit_value_details'];
                $overpaidRentaNats = $owner['over_paid_renta_nat_details'];
                $ownerContractsKey = '0';
            } elseif ('payroll' == $calcType) {
                $unpaidRentaNats = $owner['unpaid_renta_nat_arr'];
                $unpaidRentaNatValues = $owner['unpaid_renta_nat_unit_value_details'];
                $overpaidRentaNats = $owner['over_paid_renta_nat_arr'];

                if (!empty($parentContractsKey)) {
                    $ownerContractsKey = $parentContractsKey;
                    $ownerContractIds = explode('_', $parentContractsKey);
                } else {
                    $ownerContractIds = $owner['contract_array'];
                    if (!is_array($owner['contract_array'])) {
                        $ownerContractIds = array_unique(explode(',', trim($owner['contract_array'], '{}')));
                    }
                    $ownerContractsKey = implode('_', $ownerContractIds);
                }
            }
            if (!empty($owner['children'])) {
                $this->personalUseRecalculateRents($owner['children'], $levelsTree, ($level + 1), $ownerKey, $ownerContractsKey, $calcType);
            }

            /**
             * Find Charged or Contract renta. First check if charged renta is set, if not use contract renta and multiply it to the
             * calculated area (area after substracting the personal use area).
             * Then substract the already paid renta which is multiply to the owned percent (area atitude between parent and heritor).
             */
            $ownerContractData = $levelsTree[$level][$ownerKey][$ownerContractsKey];
            $allPaidRent = $ownerContractData['all_paid_rent_money'];

            $paidByOwnerRenta = ($ownerContractData['paid_renta_money'] - $allPaidRent) * $ownerContractData['owned_percent'];

            if ('payroll' == $calcType) {
                $renta = 0;
                $owner['charged_renta'] = 0;
                $owner['renta'] = 0;
                foreach ($ownerContractData['contract_info'] as $contract) {
                    if ($contract['is_charged']) {
                        $owner['charged_renta'] += ($contract['charged_renta_area'] * $contract['charged_renta_money']) - $paidByOwnerRenta;
                    } else {
                        $owner['renta'] += ($contract['area_without_pu'] * $contract['renta_money']) - $paidByOwnerRenta;
                    }

                    $renta += $owner['charged_renta'] + $owner['renta'];
                }
            } else {
                // $owner['charged_renta'] = 0;
                // $owner['renta'] = 0;
                // $paidByOwnerRenta
                // Set the found renta to the owner
                if ($ownerContractData['charged_renta_money']) {
                    // Начислена рента
                    $owner['charged_renta'] = (($ownerContractData['charged_renta_area'] * $ownerContractData['charged_renta_money'])) ?? 0;
                    // $ownerContractData['calculated_charged_renta'] = $owner['charged_renta'];
                }

                if ($owner['renta']) {
                    $contractRenta = $ownerContractData['contract_renta'] / ($ownerContractData['calculated_area'] - $ownerContractData['charged_renta_area']);
                    // Дължима рента
                    $owner['renta'] = round((($ownerContractData['calculated_area'] - $ownerContractData['charged_renta_area']) * $contractRenta), 2) ?? 0;

                    // if ($parentId && $ownerContractData['parent_paid_rent_money']) {
                    //     if ($owner['charged_renta'] & !$owner['renta']) {
                    //         $owner['charged_renta'] -= $ownerContractData['parent_paid_rent_money'];
                    //     } elseif ($owner['renta'] & !$owner['charged_renta']) {
                    //         $owner['renta'] -= $ownerContractData['parent_paid_rent_money'];
                    //     } elseif ($owner['charged_renta'] & $owner['renta']) {
                    //         // $parentRentaSum = $levelsTree[$level - 1][$parentId][$ownerContractsKey]['contract_renta'] + $levelsTree[$level - 1][$parentId][$ownerContractsKey]['calculated_charged_renta'];
                    //         // $ownerRentasSum = $owner['charged_renta'] + $owner['renta'];

                    //         // $ownerParentProportion = $ownerRentasSum / $parentRentaSum;
                    //         // $ownerPaidRentaSum = $ownerContractData['parent_paid_rent_money'] * $ownerParentProportion;

                    //         if ('Тодор Мани Маниев' == $ownerContractData['owner_names']) {
                    //             echo '===== DEBUG222 =====> ' . print_r($levelsTree, true) . ' <====';
                    //             // echo '===== DEBUG222 =====> ' . print_r($parentRentaSum, true) . ' <====';
                    //             // echo '===== DEBUG222 =====> ' . print_r($ownerRentasSum, true) . ' <====';
                    //             // echo '===== DEBUG222 =====> ' . print_r($parentRentaSum, true) . ' <====';
                    //             exit;
                    //         }

                    //         // $owner['charged_renta'] -= $ownerPaidRentaSum * ($owner['charged_renta'] / $ownerRentasSum);
                    //         // $owner['renta'] -= $ownerPaidRentaSum * ($owner['renta'] / $ownerRentasSum);
                    //     }

                    //     // $owner['renta'] -= $ownerContractData['parent_paid_rent_money'];
                    // }
                }

                $renta = $owner['charged_renta'] + $owner['renta'];
                $owner['renta'] = round($owner['renta'], 2);
                $owner['charged_renta'] = round($owner['charged_renta'], 2);
            }

            // Оставаща рента
            // When we have paid renta, calculate unpaid renta. If unpaid renta is negative set unpaid = 0 and set absolute value to overpaid
            $owner['unpaid_renta'] = round($renta - $ownerContractData['paid_renta_money'], 2);

            // Substract heritors paid renta from the parent unpaid renta
            if (!empty($ownerContractData['heritors_paid_renta_money'])) {
                $owner['unpaid_renta'] -= $ownerContractData['heritors_paid_renta_money'];

                // Correcting rounding mistake. This value is only for visualization.
                if (0.01 >= $owner['unpaid_renta']) {
                    $owner['unpaid_renta'] = 0.00;
                }
            }

            if ($owner['unpaid_renta'] < 0) {
                $owner['over_paid'] = abs($owner['unpaid_renta']);
                $owner['unpaid_renta'] = 0;
            } else {
                $owner['over_paid'] = 0;
            }

            // Iterate through renta nature types and calculate charged or contract renta nature using the same logic as the money renta
            if ($owner['renta_nat'] && '-' !== $owner['renta_nat_text']) {
                if ('payroll' == $calcType) {
                    $ownerNats = [];
                    $ownerChargedNats = [];
                    $allOwnerNats = [];
                    foreach ($ownerContractData['contract_info'] as $cId => $contract) {
                        if ($contract['renta_nat']) {
                            foreach ($contract['renta_nat'] as $nTypetId => $nType) {
                                // Find Charged or Contract renta nature
                                $rentaNat = 0;
                                $rentaNat = ($contract['area_without_pu'] * $contract['renta_nat'][$nTypetId]['renta']) - (($ownerContractData['all_paid_rent_nat'][$nTypetId] - $ownerContractData['paid_renta_nat'][$nTypetId]) * $ownerContractData['owned_percent']);

                                if (!isset($allOwnerNats[$nTypetId])) {
                                    $allOwnerNats[$nTypetId] = 0;
                                }
                                $allOwnerNats[$nTypetId] += $rentaNat;

                                if ($contract['renta_nat'][$nTypetId]['is_charged']) {
                                    if (!isset($ownerChargedNats[$nTypetId])) {
                                        $ownerChargedNats[$nTypetId] = 0;
                                    }
                                    $ownerChargedNats[$nTypetId] += number_format($rentaNat, 3, '.', '');
                                } else {
                                    if (!isset($ownerNats[$nTypetId])) {
                                        $ownerNats[$nTypetId] = 0;
                                    }
                                    $ownerNats[$nTypetId] += $rentaNat;
                                }
                                if ($unpaidRentaNats[$nTypetId] < 0) {
                                    $overpaidRentaNats[$nTypetId] = abs($unpaidRentaNats[$nTypetId]);
                                    $unpaidRentaNats[$nTypetId] = number_format(0, 3, '.', '');
                                } else {
                                    $overpaidRentaNats[$nTypetId] = number_format(0, 3, '.', '');
                                }
                            }
                        }
                    }

                    foreach ($allOwnerNats as $allOwnerNatId => $allOwnerNatValue) {
                        $unpaidRentaNats[$allOwnerNatId] = number_format(($allOwnerNatValue - $ownerContractData['paid_renta_nat'][$allOwnerNatId]), 3, '.', '');
                        if (!empty($ownerContractData['heritors_paid_renta_nat'][$allOwnerNatId])) {
                            $unpaidRentaNats[$allOwnerNatId] -= $ownerContractData['heritors_paid_renta_nat'][$allOwnerNatId];
                        }
                    }

                    $owner['renta_nat'] = $ownerNats;
                    $owner['charged_renta_nat'] = $ownerChargedNats;
                    $owner['charged_renta_nat_text'] = '';
                    foreach ($owner['charged_renta_nat'] as $chargetRentaNatId => $val) {
                        if ($val > 0) {
                            $owner['charged_renta_nat_text'] .= number_format($val, 3, '.', '') . ' X ' . $rentaNatTypeNames[$chargetRentaNatId] . '</br>';
                        }
                    }
                } else {
                    foreach ($owner['renta_nat'] as $natType => $rentaNatValue) {
                        // Find Charged or Contract renta nature
                        $paidByOwnerRentaNat = (($ownerContractData['all_paid_rent_nat'][$natType] - $ownerContractData['paid_renta_nat'][$natType]) * $ownerContractData['owned_percent']);

                        // Начислена рента в натура
                        if ($ownerContractData['charged_renta_nat'][$natType]) {
                            $owner['charged_renta_nat'][$natType]
                                = (($ownerContractData['calculated_area'] * $ownerContractData['charged_renta_nat'][$natType]) - $paidByOwnerRentaNat) ?? 0;
                        }

                        // Дължима рента в натура
                        if ($ownerContractData['renta_nat'][$natType]) {
                            $owner['renta_nat'][$natType]
                                = (($ownerContractData['calculated_area'] * $ownerContractData['renta_nat'][$natType]) - $paidByOwnerRentaNat) ?? 0;
                        }

                        // Оставаща рента в натура
                        $unpaidRentaNats[$natType]
                            = $owner['charged_renta_nat'][$natType] + $owner['renta_nat'][$natType] - $ownerContractData['paid_renta_nat'][$natType];

                        // Substract heritors paid renta nature from the parent unpaid renta nature
                        if (!empty($ownerContractData['heritors_paid_renta_nat'][$natType])) {
                            $unpaidRentaNats[$natType] -= $ownerContractData['heritors_paid_renta_nat'][$natType];
                        }

                        $overpaidRentaNats[$natType] = 0;
                        if ($unpaidRentaNats[$natType] < 0) {
                            $overpaidRentaNats[$natType] = abs($unpaidRentaNats[$natType]);
                            $unpaidRentaNats[$natType] = 0;
                        }

                        // Format numbers
                        $owner['charged_renta_nat'][$natType] = number_format(
                            $owner['charged_renta_nat'][$natType],
                            3,
                            '.',
                            ''
                        );
                        $owner['renta_nat'][$natType] = number_format(
                            $owner['renta_nat'][$natType],
                            3,
                            '.',
                            ''
                        );
                        $unpaidRentaNats[$natType] = number_format(
                            $unpaidRentaNats[$natType],
                            3,
                            '.',
                            ''
                        );
                        $overpaidRentaNats[$natType] = number_format(
                            $overpaidRentaNats[$natType],
                            3,
                            '.',
                            ''
                        );
                    }
                }
            }

            if ('payments' == $calcType) {
                $owner['renta'] = number_format($owner['renta'], 2, '.', '');
                $owner['charged_renta'] = number_format($owner['charged_renta'], 2, '.', '');
                $owner['renta_nat_text'] = implode('</br>', $owner['renta_nat']);
                $owner['unpaid_renta_nat'] = implode('</br>', $unpaidRentaNats);
                $owner['over_paid_nat'] = implode('</br>', $overpaidRentaNats);
                $owner['unpaid_renta_nat_unit_value'] = implode('</br>', $unpaidRentaNatValues);
                $owner['charged_renta_nat_text'] = implode('</br>', $owner['charged_renta_nat']);
            } elseif ('payroll' == $calcType) {
                $owner['renta_nat_text'] = '';
                $owner['unpaid_renta_nat'] = '';
                $owner['over_paid_nat'] = '';
                $owner['unpaid_renta_nat_unit_value'] = '';

                $owner['renta'] = number_format($owner['renta'], 2, '.', '');
                $owner['charged_renta'] = number_format($owner['charged_renta'], 2, '.', '');

                foreach ($owner['renta_nat'] as $natType => $val) {
                    if ($val > 0) {
                        $owner['renta_nat_text'] .= number_format($val, 3, '.', '') . ' X ' . $rentaNatTypeNames[$natType] . '</br>';
                    }
                    if ($unpaidRentaNats[$natType] > 0) {
                        $owner['unpaid_renta_nat'] .= number_format($unpaidRentaNats[$natType], 3, '.', '') . ' X ' . $rentaNatTypeNames[$natType] . '</br>';
                    }
                    if ($overpaidRentaNats[$natType] > 0) {
                        $owner['over_paid_nat'] .= number_format($overpaidRentaNats[$natType], 3, '.', '') . ' X ' . $rentaNatTypeNames[$natType] . '</br>';
                    }
                    if ($unpaidRentaNatValues[$natType] > 0) {
                        $owner['unpaid_renta_nat_unit_value'] .= number_format($unpaidRentaNatValues[$natType], 3, '.', '') . ' X ' . $rentaNatTypeNames[$natType] . '</br>';
                    }
                }

                if (empty($owner['paid_renta_nat_by_arr'])) {
                    $owner['paid_renta_nat'] = '-';
                }

                if (empty($owner['renta_nat_text'])) {
                    $owner['renta_nat_text'] = '-';
                }

                if (empty($owner['unpaid_renta_nat'])) {
                    $owner['unpaid_renta_nat'] = '-';
                }

                if (empty($owner['over_paid_nat'])) {
                    $owner['over_paid_nat'] = '-';
                }

                if (empty($owner['unpaid_renta_nat_unit_value'])) {
                    $owner['unpaid_renta_nat_unit_value'] = '-';
                }

                rtrim($owner['renta_nat_text'], '</br>');
                rtrim($owner['unpaid_renta_nat'], '</br>');
                rtrim($owner['over_paid_nat'], '</br>');
                rtrim($owner['unpaid_renta_nat_unit_value'], '</br>');
            }
        }
    }

    /**
     * Recursive function that interate from buttom to the top of the owners tree and calculate rents, unpaid rents and overpaid rents.
     *
     * @param null|mixed $parentId
     * @param null|mixed $ownerContractsKey
     * @param null|mixed $parentContractsKey
     */
    private function personalUseRecalculatePaidRents(&$ownersTree, &$levelsTree = [], $level = 1, $parentId = null, $parentContractsKey = null, $calcType)
    {
        // echo '===== DEBUG =====> ' . print_r($levelsTree, true) . ' <====';
        // exit;
        // foreach ($ownersTree as $owner) {
        // $ownerKey = $parentId ? $parentId . '-' . $owner['owner_id'] : $owner['owner_id'];

        // Payments and Payroll have different structure of owner array so we need to map the values
        // if ('payments' == $calcType) {
        //     $unpaidRentaNats = $owner['unpaid_renta_nat_details'];
        //     $unpaidRentaNatValues = $owner['unpaid_renta_nat_unit_value_details'];
        //     $overpaidRentaNats = $owner['over_paid_renta_nat_details'];
        //     $ownerContractsKey = '0';
        // } elseif ('payroll' == $calcType) {
        //     $unpaidRentaNats = $owner['unpaid_renta_nat_arr'];
        //     $unpaidRentaNatValues = $owner['unpaid_renta_nat_unit_value_details'];
        //     $overpaidRentaNats = $owner['over_paid_renta_nat_arr'];

        //     if (!empty($parentContractsKey)) {
        //         $ownerContractsKey = $parentContractsKey;
        //         $ownerContractIds = explode('_', $parentContractsKey);
        //     } else {
        //         $ownerContractIds = $owner['contract_array'];
        //         if (!is_array($owner['contract_array'])) {
        //             $ownerContractIds = array_unique(explode(',', trim($owner['contract_array'], '{}')));
        //         }
        //         $ownerContractsKey = implode('_', $ownerContractIds);
        //     }
        // }
        // if (!empty($owner['children'])) {
        //     $this->personalUseRecalculatePaidRents($owner['children'], $levelsTree, ($level + 1), $ownerKey, $ownerContractsKey, $calcType);
        // }

        // $ownerContractData = $levelsTree[$level][$ownerKey][$ownerContractsKey];

        // if ($parentId) {
        //     if ($owner['charged_renta'] & !$owner['renta']) {
        //         $owner['charged_renta'] -= $ownerContractData['parent_paid_rent_money'];
        //     } elseif ($owner['renta'] & !$owner['charged_renta']) {
        //         $owner['renta'] -= $ownerContractData['parent_paid_rent_money'];
        //     } elseif ($owner['charged_renta'] & $owner['renta']) {
        //         $parentRentaSum = $levelsTree[$level - 1][$parentId][$ownerContractsKey]['contract_renta'] + $levelsTree[$level - 1][$parentId][$ownerContractsKey]['calculated_charged_renta'];
        //         $ownerRentasSum = $owner['charged_renta'] + $owner['renta'];

        //         $ownerParentProportion = $ownerRentasSum / $parentRentaSum;
        //         $ownerPaidRentaSum = $ownerContractData['parent_paid_rent_money'] * $ownerParentProportion;

        //         // if ('Тодор Мани Маниев' == $ownerContractData['owner_names']) {
        //         //     echo '===== DEBUG222 =====> ' . print_r($ownerContractData, true) . ' <====';
        //         //     echo '===== DEBUG222 =====> ' . print_r($parentRentaSum, true) . ' <====';
        //         //     echo '===== DEBUG222 =====> ' . print_r($ownerRentasSum, true) . ' <====';
        //         //     // echo '===== DEBUG222 =====> ' . print_r($parentRentaSum, true) . ' <====';
        //         //     exit;
        //         // }

        //         $owner['charged_renta'] -= $ownerPaidRentaSum * ($owner['charged_renta'] / $ownerRentasSum);
        //         $owner['renta'] -= $ownerPaidRentaSum * ($owner['renta'] / $ownerRentasSum);
        //     }
        // }
        // }
    }

    private function getPersonalUseForOwnerAndContract($ownerId, $ownerContractIds, $farmingYear)
    {
        $personalUseOptions = [
            'chosen_years' => $farmingYear,
            'owner_ids' => $ownerId,
            'contract_id' => implode(',', $ownerContractIds),
        ];

        return $this->getPersonalUseForOwners($personalUseOptions, false, false);
    }

    private function formatUnpaidRentaNatColumns($natJson)
    {
        $unpaidNat = json_decode($natJson, true);
        if (!$unpaidNat) {
            return [];
        }
        $row = [];
        foreach ($unpaidNat as $idx2 => $natura) {
            $fieldName = 'unpaid_renta_nat_arr_' . $natura['id'];
            $row[$fieldName] = $natura['value'];
        }

        return $row;
    }
}
