<?php

namespace TF\Engine\Plugins\Core\UserDbSubleases;

use TF\Engine\Plugins\Core\Farming\FarmingController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;

class UserDbSubleasesController extends UserDbController
{
    /**
     * @var UserDbSubleasesModel
     */
    public $DbHandler;
    /**
     * @var string
     */
    public $Database;

    public function __construct($database)
    {
        parent::__construct($database);

        $this->DbHandler = new UserDbSubleasesModel($database);
        $this->Database = $database;
    }

    public function getSubleasesContragentsData($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getSubleasesContragentsData($options, $counter, $returnOnlySQL);
    }

    public function getSubleasesFarmingContragentsData($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getSubleasesFarmingContragentsData($options, $counter, $returnOnlySQL);
    }

    public function getSubleasePlotContracts($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getSubleasePlotContracts($options, $counter, $returnOnlySQL);
    }

    public function getSubleaseFullData($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getSubleaseFullData($options, $counter, $returnOnlySQL);
    }

    public function getSubleasePlotsData($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getSubleasePlotsData($options, $counter, $returnOnlySQL);
    }

    public function getSoldPlotsData($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getSoldPlotsData($options, $counter, $returnOnlySQL);
    }

    public function getFullContractDataByFilter($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getFullContractDataByFilter($options, $counter, $returnOnlySQL);
    }

    public function hasSubleaseEditedPlots($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->hasSubleaseEditedPlots($options, $counter, $returnOnlySQL);
    }

    public function getSubleaseMoneyToCollect(array $filterObj = [], $counter = false, $returnOnlySQL = false)
    {
        $options = [
            'tablename' => $this->DbHandler->tableContracts,
            'return' => [
                'money_to_collect',
            ],
            'where' => [
                'sublease_contract_id' => ['column' => 'sublease_contract_id', 'compare' => '=', 'prefix' => 'sv', 'value' => $filterObj['sublease_id']],
            ],
        ];

        return $this->DbHandler->getCollectionsContractData($options, $counter, $returnOnlySQL);
    }

    public function getCollectionsContractData(array $filterObj = [], $counter = false, $returnOnlySQL = false)
    {
        if (null != $filterObj) {
            $filterObj['owner_name'] = $filterObj['owner_name'] ? preg_replace('/\s+/', ' ', $filterObj['owner_name']) : '';
            $filterObj['rep_name'] = $filterObj['rep_name'] ? preg_replace('/\s+/', ' ', $filterObj['rep_name']) : '';
        }
        // prepare options for contract query
        $isIrrigatedArea = 'all' === $filterObj['irrigated_area'] ? null : $filterObj['irrigated_area'];

        $farmingController = new FarmingController('Farming');
        $userFarmingIds = array_keys($farmingController->getUserFarmings());
        $farmingIds = $this->ArrayHelper->filterEmptyStringArr($filterObj['farming']);
        $farmingIds = isset($farmingIds) && count($farmingIds) > 0
            ? array_intersect($farmingIds, $userFarmingIds)
            : $userFarmingIds;

        $options = [
            'tablename' => $this->DbHandler->tableContracts,
            'return' => [
                'sublease_contract_id AS id',
                'sublease_c_num AS c_num',
                'nm_usage_rights',
                "to_char(sublease_start_date, 'dd.mm.YYYY') AS start_date",
                "to_char(sublease_due_date, 'dd.mm.YYYY') AS due_date",
                "COALESCE(payday, '-') AS payday",
                'renta_dka renta',
                "CASE WHEN collected_money is null or collected_money = 0 THEN '-' ELSE collected_money::text END AS collected_money",
                'subleaser_name AS owner_names',
                'sublease_contract_area AS contract_area',
                'money_to_collect',
                "COALESCE(unpaid::text, '0.00') unpaid",
                'active',
                'rent_area',
                'farming_id',
                'subleasing_farm_id',
                'subleasing_farm_name',
                'subleasing_farm_company',
                'subleasing_farm_address',
                'subleasing_farm_company_address',
                'subleasing_farm_mol',
                'subleasing_farm_mol_egn',
                'subleasing_farm_iban_arr',
                'owner_id',
                'owner_iban',
                'owner_bic',
                'owner_bank_name',
            ],
            'where' => [
                'sublease_c_num' => ['column' => 'sublease_c_num', 'compare' => 'ILIKE', 'prefix' => 'sv', 'value' => $filterObj['c_num']],
                'date_from' => ['column' => 'sublease_start_date', 'compare' => '>=', 'prefix' => 'sv', 'value' => $filterObj['date_from']],
                'date_to' => ['column' => 'sublease_start_date', 'compare' => '<=', 'prefix' => 'sv', 'value' => $filterObj['date_to']],
                'due_date_from' => ['column' => 'sublease_due_date', 'compare' => '>=', 'prefix' => 'sv', 'value' => $filterObj['due_date_from']],
                'due_date_to' => ['column' => 'sublease_due_date', 'compare' => '<=', 'prefix' => 'sv', 'value' => $filterObj['due_date_to']],
                'farming' => ['column' => 'farming_id', 'compare' => 'IN', 'prefix' => 'sv', 'value' => $farmingIds],
                'c_type' => ['column' => 'nm_usage_rights', 'compare' => 'IN', 'prefix' => 'sv', 'value' => $this->ArrayHelper->filterEmptyStringArr($filterObj['c_type'])],
                'subleaser_name' => ['column' => "TRIM(o.name) || ' ' || TRIM(o.surname) || ' ' || TRIM(o.lastname)", 'compare' => 'ILIKE', 'value' => $filterObj['owner_name']],
                'subleaser_egn' => ['column' => 'egn', 'compare' => 'ILIKE', 'prefix' => 'o', 'value' => $filterObj['owner_egn']],
                'rep_name' => ['column' => "TRIM(orep.rep_name) || ' ' || TRIM(orep.rep_surname) || ' ' || TRIM(orep.rep_lastname)", 'compare' => 'ILIKE', 'value' => $filterObj['rep_name']],
                'rep_egn' => ['column' => 'rep_egn', 'compare' => 'ILIKE', 'prefix' => 'orep', 'value' => $filterObj['rep_egn']],
                'company_name' => ['column' => 'TRIM(company_name)', 'compare' => 'ILIKE', 'value' => $filterObj['company_name']],
                'company_eik' => ['column' => 'eik', 'compare' => '=', 'prefix' => 'o', 'value' => $filterObj['company_eik']],
                'renta_types' => ['column' => 'renta_id', 'compare' => 'IN', 'prefix' => 'cr', 'value' => $this->ArrayHelper->filterEmptyStringArr($filterObj['renta_types'])],
                'kad_ident' => ['column' => 'kad_ident', 'compare' => '=', 'prefix' => 'kvs', 'value' => $filterObj['kad_ident']],
                'ekate' => ['column' => 'ekate', 'compare' => 'IN', 'prefix' => 'kvs', 'value' => $this->ArrayHelper->filterEmptyStringArr($filterObj['ekate'])],
                'masiv' => ['column' => 'masiv', 'compare' => '=', 'prefix' => 'kvs', 'value' => $filterObj['masiv']],
                'number' => ['column' => 'number', 'compare' => '=', 'prefix' => 'kvs', 'value' => $filterObj['number']],
                'category' => ['column' => 'category', 'compare' => 'IN', 'prefix' => 'kvs', 'value' => $this->ArrayHelper->filterEmptyStringArr($filterObj['category'])],
                'irrigated_area' => ['column' => 'irrigated_area', 'compare' => '=', 'prefix' => 'kvs', 'value' => $isIrrigatedArea],
                'area_type' => ['column' => 'area_type', 'compare' => 'IN', 'prefix' => 'kvs', 'value' => $this->ArrayHelper->filterEmptyStringArr($filterObj['ntp'])],
            ],
            'group' => 'c.id',
            'custom_counter' => 'count(*),
                                 sum(money_to_collect) money_to_collect,
                                 SUM(CASE WHEN (active = true) THEN money_to_collect ELSE 0 END) AS active_contract_money_to_collect,
                                 sum(unpaid) unpaid,
                                 SUM(CASE WHEN (active = true) THEN unpaid ELSE 0 END) AS active_contract_unpaid,
                                 sum(sublease_contract_area) contract_area,
                                 SUM(CASE WHEN (active = true) THEN sublease_contract_area ELSE 0 END) AS active_contract_sublease_contract_area,
                                 sum(collected_money) collected_money,
                                 SUM(CASE WHEN (active = true) THEN rent_area ELSE 0 END) AS active_contract_rent_area
                                 ',
        ];

        date_default_timezone_set('Europe/Sofia');
        $currentDate = date('Y-m-d');

        if (UserDbSubleasesModel::CONTRACT_STATUS_TERMINATED == $filterObj['c_status']) {
            $options['where']['active'] = ['column' => 'active', 'compare' => '=', 'value' => 'FALSE'];
        } elseif (UserDbSubleasesModel::CONTRACT_STATUS_ACTIVE == $filterObj['c_status']) {
            $options['where']['active'] = ['column' => 'active', 'compare' => '=', 'prefix' => 'sv', 'value' => 'TRUE'];
            $options['where']['due_date'] = ['column' => 'sublease_due_date', 'compare' => '>=', 'value' => $currentDate];
        } elseif (UserDbSubleasesModel::CONTRACT_STATUS_EXPIRED == $filterObj['c_status']) {
            $options['where']['due_date'] = ['column' => 'sublease_due_date', 'compare' => '<', 'value' => $currentDate];
        } elseif (UserDbSubleasesModel::CONTRACT_STATUS_NOT_CANCELED == $filterObj['c_status']) {
            $options['where']['active'] = ['column' => 'active', 'compare' => '=', 'prefix' => 'sv', 'value' => 'TRUE'];
        }

        if ($filterObj['collections_search_year']) {
            $options['collections_search_year'] = $filterObj['collections_search_year'];
            $options['where']['farming_year_start_date'] = [
                'column' => 'sublease_start_date',
                'compare' => '<=',
                'prefix' => 'sv',
                'value' => $GLOBALS['Farming']['years'][$filterObj['collections_search_year']]['year'] . '-09-30',
            ];
            $options['where']['farming_year_due_date'] = [
                'column' => 'sublease_due_date',
                'compare' => '>=',
                'prefix' => 'sv',
                'value' => ($GLOBALS['Farming']['years'][$filterObj['collections_search_year']]['year'] - 1) . '-10-01',
            ];
        }

        if ($filterObj['person_name']) {
            $tmp_person_names = mb_strtolower(preg_replace('/\s+/', '.*', $filterObj['person_name']), 'UTF-8');
            $options['whereOr']['owner_name'] = ['column' => "lower(TRIM (o.name)) || ' ' || lower(TRIM (o.surname)) || ' ' || lower(TRIM (o.lastname))", 'compare' => '~', 'value' => $tmp_person_names];
            $options['whereOr']['rep_names'] = ['column' => "lower(TRIM (orep.rep_name)) || ' ' || lower(TRIM (orep.rep_surname)) || ' ' || lower(TRIM (orep.rep_lastname))", 'compare' => '~', 'value' => $tmp_person_names];
        }

        if ($filterObj['person_egn']) {
            $options['whereOr']['owner_egn'] = ['column' => 'egn', 'compare' => 'ILIKE', 'prefix' => 'o', 'value' => $filterObj['person_egn']];
            $options['whereOr']['rep_egn'] = ['column' => 'rep_egn', 'compare' => 'ILIKE', 'prefix' => 'orep', 'value' => $filterObj['person_egn']];
        }

        return $this->DbHandler->getCollectionsContractData($options, $counter, $returnOnlySQL);
    }

    public function getEkateNamesForSubleaseContract($subleaseID)
    {
        return $this->DbHandler->getEkateNamesForSubleaseContract($subleaseID);
    }
}
