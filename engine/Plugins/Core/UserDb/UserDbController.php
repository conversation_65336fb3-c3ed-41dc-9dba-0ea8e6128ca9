<?php

namespace TF\Engine\Plugins\Core\UserDb;

use DateTime;
use Exception;
use TF\Application\Common\Config;
use TF\Application\Entity\DTO\EkatteDto;
use TF\Application\Entity\LayerStyles;
use TF\Application\Entity\UserLayers;
use TF\Engine\Kernel\ArrayHelper;
use TF\Engine\Kernel\Loader;
use TF\Engine\Kernel\MTRpcException;
use TF\Engine\Kernel\StringHelper;
use TF\Engine\Plugins\Core\Farming\FarmingController;
use TF\Engine\Plugins\Core\Interfaces\ILayerable;
use TF\Engine\Plugins\Core\Layers\LayersController;
use TF\Engine\Plugins\Core\UserDbContracts\UserDbContractsController;
use TF\Engine\Plugins\Core\Users\UsersController;

/**
 * UsersController class file.
 *
 * <AUTHOR> <PERSON>
 */

/**
 * UsersController class.
 *
 * @property UserDbModel $DbHandler
 * @property string $String
 */
class UserDbController implements ILayerable
{
    /**
     * @var UserDbModel
     */
    public $DbHandler;
    public $Database;
    public $StringHelper;
    public $ArrayHelper;

    public function __construct($database)
    {
        $this->DbHandler = new UserDbModel($database);
        $this->Database = $database;
        $this->StringHelper = new StringHelper(); // new Loader('Kernel.StringHelper');
        $this->ArrayHelper = new ArrayHelper();   // /new Loader('Kernel.ArrayHelper');
    }

    public function getLayerColumnValues(string $tableName, string $columnName, array $options = [])
    {
        return $this->DbHandler->getLayerColumnValues($tableName, $columnName, $options);
    }

    public function getKVSDefinition($database, $tmp_name)
    {
        return $this->DbHandler->getKVSDefinition($database, $tmp_name);
    }

    public function getWorkDefinition($database, $tmp_name)
    {
        return $this->DbHandler->getWorkDefinition($database, $tmp_name);
    }

    public function getKMSDefinition($database, $tmpTableName)
    {
        return $this->DbHandler->getKMSDefinition($database, $tmpTableName);
    }

    public function copyKVSData($database, $definition, $tmp_table)
    {
        return $this->DbHandler->copyKVSData($database, $definition, $tmp_table);
    }

    public function getValidTableStructure()
    {
        $flag1 = $this->DbHandler->getColumnNameExist($this->Database, 'tmp_geom', 'prc_uin');

        $flag2 = $this->DbHandler->getColumnNameExist($this->Database, 'tmp_geom', 'cropcode');

        $flag3 = $this->DbHandler->getColumnNameExist($this->Database, 'tmp_geom', 'cropname');

        return !(0 == $flag1 || 0 == $flag2 || 0 == $flag3);
    }

    public function getValidData($tablename, $layer_type)
    {
        $flag = $this->DbHandler->getTableNameExist($this->Database, $tablename);

        if ($flag) {
            $flag = $this->DbHandler->getIntersectByGeom($tablename, 'tmp_geom');

            if ($flag) {
                return false;
            }

            if (6 == $layer_type) {
                $flag = $this->DbHandler->getIsakmNumberIntersect($tablename, 'tmp_geom');

                if ($flag) {
                    return false;
                }
            }
        }

        return true;
    }

    public function getIsIntersect($tablename1, $tablename2)
    {
        return $this->DbHandler->getIntersectByGeom($tablename1, $tablename2);
    }

    public function getLayerCRS($tablename)
    {
        return $this->DbHandler->getLayerCRS($tablename);
    }

    public function getValidGeom($tablename)
    {
        $notValidCount = $this->DbHandler->getValidGeom($tablename);

        return !($notValidCount > 0);
    }

    public function getNotValidGeom($tablename, $geomfield = 'geom')
    {
        return $this->DbHandler->getNotValidGeom($tablename, $geomfield);
    }

    public function copyDataFromTMPTable($tablename, $layer_type, $layerId = 0, $definition = [], $addToExisting = true, $fileId = null)
    {
        $tablenameExists = $this->DbHandler->getTableNameExist($this->Database, $tablename);
        $simplifyGeomTolerance = $GLOBALS['Layers']['simplifyGeomTolerance'];
        $tmpGeomTable = 'tmp_geom_' . $fileId;
        $layer = UserLayers::getLayerById($layerId);

        if (Config::LAYER_TYPE_ISAK == $layer_type) {
            if ($tablenameExists) {
                $this->DbHandler->deleteTable($tablename);
            }

            $this->DbHandler->simplifyGeometry($tmpGeomTable, $simplifyGeomTolerance);
            $this->createTableForLayer($layer);

            if ($this->getColumnNameExist($layer->table_name, 'area')) {
                // Set precision to 3 decimal places for area column
                $this->updateColumnType($layer->table_name, 'area', 'numeric(15, 3)');
            }

            $tmpTableColumns = $this->getTableColumnsList($tmpGeomTable);

            $this->DbHandler->copyLayerItemsFromTable($layer, $tmpGeomTable, true, $tmpTableColumns);
        } elseif (Config::LAYER_TYPE_DSS == $layer_type) {
            if (!$tablenameExists) {
                $this->DbHandler->createTableDSS($tablename);
            }
            $this->DbHandler->insertFromTmpInto($tablename);
        } elseif (Config::LAYER_TYPE_KMS == $layer_type) {
            if (!$tablenameExists) {
                $this->DbHandler->createTableForLayer($layer);
            }

            $tmpTable = 'tmp_' . $tablename . '_' . $fileId;
            $this->DbHandler->simplifyGeometry($tmpTable, $simplifyGeomTolerance);

            $this->DbHandler->copyLayerItemsFromTable($layer, $tmpTable);
        } elseif (Config::LAYER_TYPE_WORK_LAYER == $layer_type) {
            $tmpWorkLayerTable = 'tmp_work_' . $fileId;

            $simplifyGeomTolerance = $GLOBALS['Layers']['simplifyGeomTolerance'];
            $this->DbHandler->simplifyGeometry($tmpWorkLayerTable, $simplifyGeomTolerance);

            // passing add to existing = true so we dont try to truncate a non existing table
            $this->DbHandler->insertFromTmpWithDefinitionForWork($this->Database, $tablename, $tmpWorkLayerTable, 'name', $definition, true);
        }

        // Update the value of $tablenameExists in case the table is created after the first check
        $tablenameExists = $this->DbHandler->getTableNameExist($this->Database, $tablename);

        if ($tablenameExists && $layer) {
            $this->DbHandler->createLayerMissingColumns($layer);
        }
    }

    public function copyDataFromTMPWorkTable($tablename, $tmp_tablename)
    {
        $this->DbHandler->copyDataFromTMPWorkTable($tablename, $tmp_tablename);
    }

    public function createTableForLayer(UserLayers $layer)
    {
        $this->DbHandler->createTableForLayer($layer);
    }

    public function transformColumnGeometryProjection($tablename, $projection)
    {
        $this->DbHandler->transformColumnGeometryProjection($tablename, $projection);
    }

    public function checkColumnNameExist($table, $column)
    {
        return $this->DbHandler->checkColumnNameExist($table, $column);
    }

    /**
     * Is Exist Required Columns.
     *
     * @param string $table
     * @param array $arrColumn
     *
     * @return bool
     */
    public function isExistRequiredColumns($table, $arrColumn)
    {
        $flagExist = true;
        $count = count($arrColumn);
        for ($i = 0; $i < $count; $i++) {
            if (!$this->DbHandler->checkColumnNameExist($table, $arrColumn[$i])) {
                $flagExist = false;
            }
        }

        return $flagExist;
    }

    /**
     * @deprecated Use method copyLayerItems instead
     *
     * @param null|mixed $ekate
     */
    public function copyDataFromTo($tablenameFrom, $tablenameTo, $fromTableType, $toTableType, $idArray = [], $ekate = null, $definitions = [])
    {
        $this->FarmingController = new FarmingController('Farming');
        $this->UsersController = new UsersController('Users');

        if (Config::LAYER_TYPE_GPS == $toTableType) {
            $idField = 'gid';
            if (Config::LAYER_TYPE_ZP == $fromTableType) {
                $idField = 'id';
            }

            $this->DbHandler->insertIntoLayerGPSFromTableGeomOnly($tablenameFrom, $fromTableType, 'geom', $idField, $idArray, $ekate, $definitions);
        } else {
            if (Config::LAYER_TYPE_GPS == $fromTableType) {
                $this->DbHandler->insertIntoTableFromTableGeomOnly($tablenameTo, $tablenameFrom);
            } elseif (Config::LAYER_TYPE_ISAK == $fromTableType) {
                $year = $this->FarmingController->getFarmingYearByTableName($tablenameFrom);

                $yearID = $year[0]['year'];
                $options = [
                    'tablename' => 'su_crop_codes',
                    'return' => ['crop_code'],
                    'where' => [
                        'year' => ['column' => 'year', 'compare' => '=', 'value' => $yearID],
                    ],
                ];

                $idCropsArray = $this->UsersController->getItemsByParams($options);

                if (0 == count($idCropsArray)) {
                    $idCropsArray[0]['crop_code'] = '000000';
                }

                $this->DbHandler->insertIntoTableFromTableIsakNum($tablenameTo, $tablenameFrom, $idCropsArray);
            }
        }
    }

    /** From WORK_LAYER, FOR_ISAK , ZP to WORK_LAYER, FOR_ISAK , ZP.
     * @param null|mixed $farmingYear
     * @param null|mixed $ekate
     * @param null|mixed $definitions
     *
     * @deprecated Use method copyLayerItems instead
     */
    public function copyDataFromToCustom($tablenameFrom, $tablenameTo, $fromTableType, $toTableType, $idArray = [], $farmingYear = null, $ekate = null, $definitions = null)
    {
        switch ($toTableType) {
            case Config::LAYER_TYPE_FOR_ISAK:
                $this->copyDataFromToLayerForIsak($tablenameFrom, $tablenameTo, $fromTableType, $idArray, $farmingYear, $ekate);

                break;
            case Config::LAYER_TYPE_ZP:
                $this->copyDataFromToLayerZP($tablenameFrom, $tablenameTo, $fromTableType, $idArray, $ekate, $definitions);

                break;
            case Config::LAYER_TYPE_WORK_LAYER:
                $this->copyDataFromToWorkLayer($tablenameFrom, $tablenameTo, $fromTableType, $idArray, $ekate, $definitions);

                break;
            default:
                return false;

                break;
        }
    }

    public function getMaxExtent($tablename)
    {
        return $this->DbHandler->getMaxExtent($tablename);
    }

    public function getZPMaxExtent($tablename, $id)
    {
        return $this->DbHandler->getZPMaxExtent($tablename, $id);
    }

    public function getKvsMaxExtent($tablename, $id, $bymasiv = false)
    {
        return $this->DbHandler->getKvsMaxExtent($tablename, $id, $bymasiv);
    }

    public function getIntersectionCount($tablename1, $tablename2)
    {
        return $this->DbHandler->getIntersectByGeom($tablename1, $tablename2);
    }

    public function updateLayerCRS($fromCRS, $toCRS, $tablename)
    {
        return $this->DbHandler->updateLayerCRS($fromCRS, $toCRS, $tablename);
    }

    public function createColumnCRS($CRS, $tablename)
    {
        return $this->DbHandler->createColumnCRS($CRS, $tablename);
    }

    /**
     * @deprecated Use method createTableForLayer instead
     */
    public function createTableKVS()
    {
        $flag = $this->DbHandler->getTableNameExist($this->Database, 'layer_kvs');
        if (!$flag) {
            $this->DbHandler->createTableKVS();
        }
    }

    public function createTableKvsEkatteInvalid($tmpTable)
    {
        $flag = $this->DbHandler->getTableNameExist($this->Database, $tmpTable . '_invalid');
        if (!$flag) {
            $this->DbHandler->createTableKvsEkatteInvalid($tmpTable);
        }
    }

    public function dropTableKvsEkatteInvalid($tmpTable)
    {
        $flag = $this->DbHandler->getTableNameExist($this->Database, $tmpTable . '_invalid');
        if ($flag) {
            $this->DbHandler->dropTableKvsEkatteInvalid($tmpTable);
        }
    }

    public function dropTableKvsEkatte($tmpTable, $cascade = false)
    {
        $this->DbHandler->dropTableKvsEkatte($tmpTable, $cascade);
    }

    public function dropMaterializedView($view)
    {
        $this->DbHandler->dropMaterializedView($view);
    }

    /**
     * @deprecated Use method createTableForLayer instead
     */
    public function createTableZP($tablename)
    {
        $flag = $this->DbHandler->getTableNameExist($this->Database, $tablename);

        if (!$flag) {
            $this->DbHandler->createTableZP($tablename);
        }
    }

    public function updateGeometry($tableGPS, $array)
    {
        $count = count($array);
        for ($i = 0; $i < $count; $i++) {
            $this->DbHandler->updateGeometry($tableGPS, $array[$i]->geometry, $array[$i]->id);
        }
    }

    public function updateAllowableArea($tablename, $plotIdsArray)
    {
        $this->DbHandler->updateAllowableArea($tablename, $plotIdsArray);
    }

    public function updateGeometryWithInfoForGps($tableGPS, $array, $plot_name, $plot_info)
    {
        $count = count($array);
        for ($i = 0; $i < $count; $i++) {
            $this->DbHandler->updateGeometryWithInfoForGps($tableGPS, $array[$i]->geometry, $array[$i]->id, $plot_name, $plot_info);
        }
    }

    public function updateGeometryWithInfoForGpsFromGeoJson($tableGPS, $geometry, $gid, $plotName, $plotInfo, $srcProj = 3857, $dstProj = 32635)
    {
        $this->DbHandler->updateGeometryWithInfoForGpsFromGeoJson($tableGPS, $geometry, $gid, $plotName, $plotInfo, $srcProj, $dstProj);
    }

    public function updateGeometryWithInfoForIsak($table, $array, $prc_name, $ekatte)
    {
        $count = count($array);
        for ($i = 0; $i < $count; $i++) {
            $this->DbHandler->updateGeometryWithInfoForIsak($table, $array[$i]->geometry, $array[$i]->id, $prc_name, $ekatte);
        }
    }

    public function updateDataForIsak($table, $array)
    {
        $count = count($array);
        for ($i = 0; $i < $count; $i++) {
            $this->DbHandler->updateDataForIsak($table, $array[$i]->id);
        }
    }

    public function addGeometry($tableName, $array, ?array $fieldParams = [])
    {
        $count = count($array);
        for ($i = 0; $i < $count; $i++) {
            $fieldParam = $fieldParams[$i] ?? null;
            $this->DbHandler->addGeometry($tableName, $array[$i]->geometry, $fieldParam);
        }
    }

    /**
     * @param string $prefix
     *
     * @throws TDbException
     */
    public function addLayers(string $table, array $newGeometries, array $sourceGeometry, ?int $prefix = null, bool $preserveLayers = false)
    {
        $data = [];
        foreach ($newGeometries as $key => $geometry) {
            if (!isset($gidArr[$geometry->gid])) {
                $gidArr[$geometry->gid] = 0;
            }

            if (empty($geometry->geometry) || 0 === $geometry->geometries_num) {
                if (!$preserveLayers) {
                    $this->DbHandler->removeGeometry($table, $geometry->gid, 'gid');
                }

                continue;
            }

            foreach ($sourceGeometry as $info) {
                if ($geometry->gid === $info['gid']) {
                    unset($info['gid']);
                    $data[$key] = $info;
                    $data[$key]['geom'] = $geometry->geometry;
                }
            }

            // Don't change the information if the geometry is fully contained in the selected layer or geometry contains more than 1 polygon. In this case the name will be changed in processMultiPolygons method
            if ($geometry->is_contains || $geometry->geometries_num > 1) {
                continue;
            }

            if (!is_null($prefix) && is_array($data[$key]) && array_key_exists('name', $data[$key])) {
                $data[$key]['name'] = $data[$key]['name'] . '-' . $gidArr[$geometry->gid] . '-' . $prefix;
            } elseif (!is_null($prefix) && is_array($data[$key]) && array_key_exists('plot_name', $data[$key])) {
                $data[$key]['plot_name'] = $data[$key]['plot_name'] . '-' . $gidArr[$geometry->gid] . '-' . $prefix;
            }

            ++$gidArr[$geometry->gid];
        }

        if (!empty($data)) {
            $this->DbHandler->addLayers($table, $data);
        }
    }

    public function addGeometryForIsakAtrInfo(string $table, string $idNameA, array $array, array $resultsAtrInfo, ?int $prefix = null)
    {
        $count = count($array);
        for ($i = 0; $i < $count; $i++) {
            $arrAtrInfo = $this->getForIsakAtrInfoArray($resultsAtrInfo, $array[$i]->$idNameA, $idNameA);

            if ($array[$i]->geometry) {
                $this->DbHandler->addGeometryForIsakAtrInfo($table, $array[$i]->geometry, $arrAtrInfo, $i, $prefix);
            }
        }
    }

    public function getForIsakAtrInfoArray($resultsAtrInfo, $searchKey, $idNameA)
    {
        foreach ($resultsAtrInfo as $key => $aValue) {
            $find = array_search($searchKey, $aValue);

            if ($find == $idNameA) {
                return $aValue;
            }
        }
    }

    public function addGeometryWithInfoForIsak($tableIsak, $array, $plot_name, $ekatte)
    {
        $count = count($array);
        for ($i = 0; $i < $count; $i++) {
            $this->DbHandler->addGeometryWithInfoForIsak($tableIsak, $array[$i]->geometry, $plot_name, $ekatte);
        }
    }

    public function addGeometryWithInfoForGps($tableGPS, $array, $plot_name, $plot_info)
    {
        $count = count($array);
        for ($i = 0; $i < $count; $i++) {
            $this->DbHandler->addGeometryWithInfoForGps($tableGPS, $array[$i]->geometry, $plot_name, $plot_info);
        }
    }

    public function removeAllGeometries($tablename)
    {
        $this->DbHandler->removeAllGeometries($tablename);
    }

    public function removeGeometry($tableGPS, $id, $id_name)
    {
        $this->DbHandler->removeGeometry($tableGPS, $id, $id_name);
    }

    public function removeMultipleGeometries($tableGPS, $array, $id_name)
    {
        $count = count($array);
        for ($i = 0; $i < $count; $i++) {
            $this->removeGeometry($tableGPS, $array[$i], $id_name);
        }
    }

    public function emptyTable($tableGPS)
    {
        $this->DbHandler->emptyTable($tableGPS);
    }

    public function addItem($options)
    {
        return $this->DbHandler->addItem($options);
    }

    public function getPlotsOwnres($pcRelIds = [])
    {
        return $this->DbHandler->getPlotsOwnres($pcRelIds);
    }

    public function getParentPath($parentPath)
    {
        return $this->DbHandler->getParentPath($parentPath);
    }

    public function addItems($options)
    {
        $this->DbHandler->addItems($options);
    }

    public function editItem($options)
    {
        return $this->DbHandler->editItem($options);
    }

    public function getIsakLayerNumberExist($tablename, $isak_number)
    {
        return $this->DbHandler->getIsakLayerNumberExist($tablename, $isak_number);
    }

    public function getIsakLayerGeomByNumber($tablename, $isak_number)
    {
        return $this->DbHandler->getIsakLayerGeomByNumber($tablename, $isak_number);
    }

    public function getPolygonDataById($options)
    {
        $tablename = $options['tablename'];
        $whereFields = array_keys($options['where']);
        $whereValues = array_values($options['where']);
        $return = $options['return'];

        return $this->DbHandler->getPolygonDataById($tablename, $whereFields, $whereValues, $return);
    }

    public function getForIsakSchemas($result)
    {
        $schemas = [];
        $schemas[] = (($result['sepp']) ? $GLOBALS['Farming']['schema'][Config::SEPP]['name'] : null);
        $schemas[] = (($result['zdp']) ? $GLOBALS['Farming']['schema'][Config::ZDP]['name'] : null);
        $schemas[] = (($result['pndp']) ? $GLOBALS['Farming']['schema'][Config::PNDP]['name'] : null);
        $schemas[] = (($result['nr1']) ? $GLOBALS['Farming']['lfa_schema_types'][Config::NR1]['name'] : null);
        $schemas[] = (($result['nr2']) ? $GLOBALS['Farming']['lfa_schema_types'][Config::NR2]['name'] : null);
        foreach ($schemas as $key => $schema) {
            if (empty($schema)) {
                unset($schemas[$key]);
            }
        }
        $schema = implode(',', $schemas);

        return $schema;
    }

    public function getTableNameExist(string $tablename)
    {
        return $this->DbHandler->getTableNameExist($this->Database, $tablename);
    }

    public function getTableColumnsList(string $tablename, $schema = 'public')
    {
        return $this->DbHandler->getTableColumnsList($tablename, $schema);
    }

    public function getAllUserTables()
    {
        return $this->DbHandler->getAllUserTables($this->Database);
    }

    public function getAllUsedZPLayerTables()
    {
        return $this->DbHandler->getAllUsedZPLayerTables($this->Database);
    }

    public function getColumnNameExist($tablename, $columnName)
    {
        return $this->DbHandler->getColumnNameExist($this->Database, $tablename, $columnName);
    }

    public function getExportItemsForTrimble($tablename, $id = false, $field = 'gid')
    {
        return $this->DbHandler->getExportItemsForTrimble($tablename, $id, $field);
    }

    public function setLayerGPSGeom()
    {
        $this->DbHandler->setLayerGPSGeom();
    }

    public function getOverlapsMapQuery($overlapid)
    {
        $tablename = $this->DbHandler->tableOverlapsData;
        $tableKVS = $this->DbHandler->tableKVS;
        $tablePlotsRel = $this->DbHandler->contractsPlotsRelTable;
        $tableContracts = $this->DbHandler->tableContracts;

        return "SELECT kvs.gid, kvs.geom, kvs.kad_ident, cont.nm_usage_rights FROM {$tableKVS} kvs
            INNER JOIN {$tablename} t ON (kvs.gid = t.gid)
            LEFT JOIN {$tablePlotsRel} rel ON (rel.plot_id = kvs.gid)
            LEFT JOIN {$tableContracts} cont ON (rel.contract_id = cont.id AND ((cont.due_date>='" . date('Y-m-d') . "' AND cont.start_date<='" . date('Y-m-d') . "' AND cont.active=TRUE AND cont.parent_id=0) OR cont.nm_usage_rights = 1))
            WHERE TRUE AND t.overlap_id = " . $overlapid . ' AND t.has_match = TRUE
            GROUP BY kvs.gid, kvs.geom, kvs.kad_ident, cont.nm_usage_rights';
    }

    public function getZPIntersectKvs($options, $tableNameZP)
    {
        $tablename = $this->DbHandler->tableOverlapsData;
        $tableKVS = $this->DbHandler->tableKVS;
        $tablePlotsRel = $this->DbHandler->contractsPlotsRelTable;
        $tableContracts = $this->DbHandler->tableContracts;

        $sql = "SELECT kvs.gid, kvs.geom, kvs.kad_ident, cont.nm_usage_rights FROM {$tableKVS} kvs
            INNER JOIN {$tableNameZP} b ON ST_Intersects(kvs.geom, b.geom)
            LEFT JOIN {$tablePlotsRel} rel ON (rel.plot_id = kvs.gid)
            LEFT JOIN {$tableContracts} cont ON (rel.contract_id = cont.id AND ((cont.due_date>='" . date('Y-m-d') . "' AND cont.start_date<='" . date('Y-m-d') . "' AND cont.active=TRUE AND cont.parent_id=0) OR cont.nm_usage_rights = 1))
            WHERE TRUE AND ST_Area(ST_Intersection(kvs.geom,b.geom)) > 10";
        if ($options) {
            $sql = $this->DbHandler->createWhereSQL($sql, $options, true);
        }

        $sql .= ' GROUP BY kvs.gid, kvs.geom, kvs.kad_ident, cont.nm_usage_rights';

        return $sql;
    }

    public function getAgreementsMapQuery($agreementid)
    {
        $tablename = $this->DbHandler->tableAgreementsData;
        $tableKVS = $this->DbHandler->tableKVS;

        return "SELECT kvs.geom, kvs.gid, kvs.kad_ident FROM {$tableKVS} kvs INNER JOIN {$tablename} t ON (kvs.gid = t.gid) WHERE TRUE AND t.agreement_id  = " . $agreementid . ' AND t.has_match = TRUE';
    }

    /**
     * @return int
     */
    public function deleteItemsByParams($options)
    {
        return $this->DbHandler->deleteItemsByParams($options);
    }

    public function getItemsByParams(array $options, bool $counter = false, bool $returnOnlySQL = false, array $bindingParams = [])
    {
        return $this->DbHandler->getItemsByParams($options, $counter, $returnOnlySQL, $bindingParams);
    }

    public function getLayersByParams($options, $counter = false, $returnOnlySQL = false, $bindingParams = [])
    {
        return $this->DbHandler->getLayersByParams($options, $counter, $returnOnlySQL, $bindingParams);
    }

    public function multiEdit($options)
    {
        return $this->DbHandler->multiEdit($options);
    }

    public function getChargedRenta($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getChargedRenta($options, $counter, $returnOnlySQL);
    }

    public function updateSequenceKVS()
    {
        return $this->DbHandler->updateSequenceKVS();
    }

    public function updateItemStatus($options)
    {
        $this->DbHandler->updateItemStatus($options);
    }

    public function getOwnership($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getOwnership($options, $counter, $returnOnlySQL);
    }

    public function getCooperators($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getCooperators($options, $counter, $returnOnlySQL);
    }

    public function transformColumnCRS($tablename, $tocrs)
    {
        $this->DbHandler->transformColumnCRS($tablename, $tocrs);
    }

    public function getDataByQuery(string $query, array $params = [])
    {
        return $this->DbHandler->getDataByQuery($query, $params);
    }

    public function updateLayerProjection($tablename, $projection = '32635')
    {
        $this->DbHandler->updateLayerProjection($tablename, $projection);
    }

    public function getTableName($database, $tablename)
    {
        return $this->DbHandler->getTableName($database, $tablename);
    }

    public function addColumn($tablename, $column, $columnType = 'character varying(255)')
    {
        return $this->DbHandler->addColumn($tablename, $column, $columnType);
    }

    public function addColumnsToWorkLayer(UserLayers $layer, array $columnDefinitions)
    {
        return $this->DbHandler->addColumnsToWorkLayer($layer, $columnDefinitions);
    }

    public function setWorkLayerColumnsByDefinitions(UserLayers $layer, array $columnDefinitions)
    {
        $regenerateMapFile = $this->DbHandler->setWorkLayerColumnsByDefinitions($layer, $columnDefinitions);

        $regenerateMapFile = true;
        if ($regenerateMapFile) {
            $options = [
                'user_id' => $layer->group_id,
                'database' => $this->Database,
            ];

            $LayersController = new LayersController('Layers');
            $LayersController->generateMapFile($options);
        }
    }

    /**
     * @deprecated Use method createTableForLayer instead
     */
    public function createTableForISAK($tablename)
    {
        $this->DbHandler->createTableForISAK($tablename);
    }

    public function createTableEnpData()
    {
        $this->DbHandler->createTableEnpData();
    }

    public function getPolygonDataByPointFromLogin3($tablename, $lon, $lat, array $extent = [], $idField = 'gid', $geomField = 'geom')
    {
        return $this->DbHandler->getPolygonDataByPointFromLogin3($tablename, $lon, $lat, $extent, $idField, $geomField);
    }

    public function stMakeValid($tablename, $geomField = 'geom')
    {
        $this->DbHandler->stMakeValid($tablename, $geomField);
    }

    public function removeGeometryCollection($tablename, $geomField = 'geom')
    {
        $this->DbHandler->removeGeometryCollection($tablename, $geomField);
    }

    public function createAllowableFromIsakReportView($layerId)
    {
        if (DEFAULT_DB_VERSION >= 9.3) {
            $this->DbHandler->createAllowableFromIsakReportView($layerId, $this->Database);

            return;
        }
    }

    public function createSEPPReportView($layerId)
    {
        if (DEFAULT_DB_VERSION >= 9.3) {
            $this->DbHandler->createSEPPReportView($layerId, $this->Database);

            return;
        }
        $this->createSEPPReportFakeView($layerId);
    }

    public function createPZPReportView($layerId)
    {
        if (DEFAULT_DB_VERSION >= 9.3) {
            $this->DbHandler->createPZPReportView($layerId, $this->Database);

            return;
        }
        $this->createPZPReportFakeView($layerId);
    }

    public function createKvsContractsUpdateView($id, $ekate, $dateUploaded)
    {
        $this->DbHandler->createKvsContractsUpdateView($id, $ekate, $dateUploaded, $this->Database);
    }

    public function createCsdMatView(string $viewName, string $ekatte, string $csdDatayear, array $coloringOptions = [], string $coloringType = LayerStyles::SINGLE_COLORING_TYPE)
    {
        $this->DbHandler->createCsdMatView($viewName, $ekatte, $csdDatayear, $coloringOptions, $coloringType);
    }

    public function findEkatteByCode(string $ekatteCode): ?EkatteDto
    {
        return $this->DbHandler->findEkatteByCode($ekatteCode);
    }

    public function refreshView($viewName)
    {
        $this->DbHandler->refreshView($viewName);
    }

    public function getViewNameExists(string $viewName)
    {
        return $this->DbHandler->getViewNameExists($viewName);
    }

    public function checkIfIndexExists($indexName)
    {
        return $this->DbHandler->checkIfIndexExists($this->Database, $indexName);
    }

    public function createSEPPReportFakeView($layerId)
    {
        $this->DbHandler->createSEPPReportFakeView($layerId, $this->Database);
    }

    public function refreshSEPPReportFakeView($layerId)
    {
        $this->DbHandler->refreshSEPPReportFakeView($layerId);
    }

    public function createPZPReportFakeView($layerId)
    {
        $this->DbHandler->createPZPReportFakeView($layerId, $this->Database);
    }

    public function refreshPZPReportFakeView($layerId)
    {
        $this->DbHandler->refreshPZPReportFakeView($layerId);
    }

    public function getCropsByYears($id, $oldCrop, $years)
    {
        return $this->DbHandler->getCropsByYears($id, $oldCrop, $years);
    }

    public function getExtentOfMultipleLayers($queryParams, $counter = false, $returnOnlySQL = false)
    {
        $innerSql = [];
        foreach ($queryParams as $layer) {
            $innerSql[] = 'SELECT geom from ' . $layer['layer_name']
            . (('' != $layer['filtered_plots']) ? ' where ' . $layer['id'] . ' in (' . $layer['filtered_plots'] . ')' : ' ');
        }
        $innerSqlText = implode(' UNION ', $innerSql);

        return $this->DbHandler->getExtentOfMultipleLayers($innerSqlText, $counter, $returnOnlySQL);
    }

    public function getExtentOfRemoteMultipleLayers($queryParams, $counter = false, $returnOnlySQL = false)
    {
        $innerSql = [];
        foreach ($queryParams as $layer) {
            $innerSql[] = 'SELECT geom from ' . $layer['layer_name']
            . (('' != $layer['filtered_plots']) ? ' where ' . $layer['id'] . ' in (' . $layer['filtered_plots'] . ')' : ' ');
        }
        $innerSqlText = implode(' UNION ', $innerSql);

        return $this->DbHandler->getExtentOfRemoteMultipleLayers($innerSqlText, $counter, $returnOnlySQL);
    }

    public function getMissingKVSPlots($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getMissingKVSPlots($options, $counter, $returnOnlySQL);
    }

    /**
     * @param string $tmpTable
     * @param array $columns
     *
     * @throws TDbException
     *
     * @return int
     */
    public function updateKvsFromOszFile($tmpTable, $columns)
    {
        $tpmTableChunk = explode('_', $tmpTable);
        $ekate = end($tpmTableChunk);

        return $this->DbHandler->updateKvsFromOszFile($tmpTable, $ekate, $columns);
    }

    public function removeInactivePlotsKvs($tmpTable)
    {
        $tpmTableChunk = explode('_', $tmpTable);
        $ekate = end($tpmTableChunk);

        return $this->DbHandler->removeInactivePlotsKvs($tmpTable, $ekate);
    }

    public function reactivateOldPlotsKvs($tmpTable)
    {
        $tpmTableChunk = explode('_', $tmpTable);
        $ekate = end($tpmTableChunk);

        return $this->DbHandler->reactivateOldPlotsKvs($tmpTable, $ekate);
    }

    public function updateKVSDocumentArea($tmpTable, $ekate)
    {
        return $this->DbHandler->updateKVSDocumentArea($tmpTable, $ekate);
    }

    public function addKvsOszPlots($tmpTable)
    {
        return $this->DbHandler->addKvsOszPlots($tmpTable);
    }

    public function addKvsBordersFromTable(array $tableOptions)
    {
        return $this->DbHandler->addKvsBordersFromTable($tableOptions);
    }

    public function updateKvsBorders(array $ekattes)
    {
        return $this->DbHandler->updateKvsBorders($ekattes);
    }

    public function addInvalidKvsOszPlots($tmpTable)
    {
        return $this->DbHandler->addInvalidKvsOszPlots($tmpTable);
    }

    public function deleteInvalidKvsOszPlots($tmpTable)
    {
        return $this->DbHandler->deleteInvalidKvsOszPlots($tmpTable);
    }

    public function updateTemporaryKVSLayerIsSystemColumn($tmpTable, $whereStatement = null, $returnOnlySQL = false)
    {
        if (!$this->getColumnNameExist($tmpTable, 'is_system')) {
            $this->createColumnIsSystem($tmpTable);
        }

        $this->DbHandler->updateColumnIsSystem($tmpTable, $whereStatement, $returnOnlySQL);
    }

    public function updateColumnType(string $tablename, string $column, string $columnType)
    {
        return $this->DbHandler->updateColumnType($tablename, $column, $columnType);
    }

    public function createColumnIsSystem($tablename)
    {
        return $this->DbHandler->createColumnIsSystem($tablename);
    }

    public function getNewPlotsForHistoryLog($tablename, $ekate)
    {
        return $this->DbHandler->getNewPlotsForHistoryLog($tablename, $ekate);
    }

    public function getInvalidKvsOszPlots($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getInvalidKvsOszPlots($options, $counter, $returnOnlySQL);
    }

    public function saveKvsOszInvalidPlotsChanges($rpcParam)
    {
        return $this->DbHandler->saveKvsOszInvalidPlotsChanges($rpcParam);
    }

    public function addDumpedKvsOszPlots($tmpTable)
    {
        return $this->DbHandler->addDumpedKvsOszPlots($tmpTable);
    }

    public function removeInvalidAndLineStringsKvsOszPlots($tmpTable, $field)
    {
        return $this->DbHandler->removeInvalidAndLineStringsKvsOszPlots($tmpTable, $field);
    }

    public function setDataTypeGeometry($tmpTable)
    {
        return $this->DbHandler->setDataTypeGeometry($tmpTable);
    }

    public function refreshTopicLayerKVSViews()
    {
        return $this->DbHandler->refreshTopicLayerKVSViews();
    }

    public function getOszInfoForPlot($plotGid)
    {
        return $this->DbHandler->getOszInfoForPlot($plotGid);
    }

    public function getMaxExtentForEkate($tablename, $ekate)
    {
        return $this->DbHandler->getMaxExtentForEkate($tablename, $ekate);
    }

    public function getSoldPlotsAfterDate($start_date)
    {
        return $this->DbHandler->getSoldPlotsAfterDate($start_date);
    }

    public function addPlotToLayerGps(array $data, int $srcProj = 3857, int $dstProj = 32635)
    {
        return $this->DbHandler->addPlotToLayerGps($data, $srcProj, $dstProj);
    }

    public function getGeoJsonFeatures($rows, $sort, $order)
    {
        return $this->DbHandler->getGeoJsonFeatures($rows, $sort, $order);
    }

    public function refreshAndSelectView($viewName)
    {
        return $this->DbHandler->refreshAndSelectView($viewName);
    }

    public function equalizeContractAndPlotAreas($plotsForSkipping)
    {
        return $this->DbHandler->equalizeContractAndPlotAreas($plotsForSkipping);
    }

    public function getPlotsForEqualizeContractAndPlotAreasAndRents()
    {
        return $this->DbHandler->getPlotsForEqualizeContractAndPlotAreasAndRents();
    }

    public function getSinglePlotPricePerContract($contract_id)
    {
        return $this->DbHandler->getSinglePlotPricePerContract($contract_id);
    }

    public function setSinglePricePerContract($contract_id, $single_price)
    {
        return $this->DbHandler->setSinglePricePerContract($contract_id, $single_price);
    }

    public function addFarmingAsOwnerForPcRelId($pc_rel_id, $farming_id)
    {
        return $this->DbHandler->addFarmingAsOwnerForPcRelId($pc_rel_id, $farming_id);
    }

    public function transformColumnToGeometry($tablename)
    {
        return $this->DbHandler->transformColumnToGeometry($tablename);
    }

    public function wktToGeoJSON(string $wkt): string
    {
        return $this->DbHandler->wktToGeoJSON($wkt);
    }

    public function addPrimaryKeyAndGidSequence($tablename)
    {
        $this->DbHandler->addPrimaryKeyAndGidSequence($tablename);
    }

    public function deleteTable($tablename)
    {
        $this->DbHandler->deleteTable($tablename);
    }

    public function deleteDuplicatedRecordsFromOSZ($ekatte)
    {
        return $this->DbHandler->deleteDuplicatedRecordsFromOSZ($ekatte);
    }

    public function updateGeometryUniversal($tablename, $geometry, $id, $id_name = 'gid')
    {
        return $this->DbHandler->updateGeometryUniversal($tablename, $geometry, $id, $id_name);
    }

    public function refreshEkateCombobox()
    {
        return $this->DbHandler->refreshEkateCombobox();
    }

    public function refreshOszEkateCombobox()
    {
        return $this->DbHandler->refreshOszEkateCombobox();
    }

    public function refreshRentaViews()
    {
        return $this->DbHandler->refreshRentaViews();
    }

    public function getEkateAreaForCPRID($ids)
    {
        return $this->DbHandler->getEkateAreaForCPRID($ids);
    }

    public function deleteTmpKvsTable($id)
    {
        return $this->DbHandler->deleteTmpKvsTable($id);
    }

    public function disableRentaMatViewTriggers()
    {
        return $this->DbHandler->disableRentaMatViewTriggers();
    }

    public function enableRentaMatViewTriggers()
    {
        return $this->DbHandler->enableRentaMatViewTriggers();
    }

    public function intersectKvsWithCustomTable($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->intersectKvsWithCustomTable($options, $counter, $returnOnlySQL);
    }

    public function updateAreaForRentToAllowableArea($contractIds, $plotsForSkipping)
    {
        return $this->DbHandler->updateAreaForRentToAllowableArea($contractIds, $plotsForSkipping);
    }

    public function updateAreaForRentToContractArea($contractIds, $plotsForSkipping)
    {
        return $this->DbHandler->updateAreaForRentToContractArea($contractIds, $plotsForSkipping);
    }

    public function updateAreaForRentToArableArea($contractIds, $plotsForSkipping)
    {
        return $this->DbHandler->updateAreaForRentToArableArea($contractIds, $plotsForSkipping);
    }

    public function endUpdate($file_id, $ekate)
    {
        $UsersController = new UsersController('Users');
        $LayersController = new LayersController('Layers');

        // check if table exists
        $contractsUpdateTableExists = $this->getViewNameExists('kvs_contracts_update_' . $ekate);
        $kadidents = '';
        if ($contractsUpdateTableExists) {
            $options = [
                'tablename' => 'kvs_contracts_update_' . $ekate,
                'return' => ['DISTINCT new_kad_idents'],
            ];

            $not_updated_kadidents = $this->getItemsByParams($options, false, false);

            if (isset($not_updated_kadidents[0])) {
                $kadidents = trim($not_updated_kadidents[0]['new_kad_idents'], '{}');
            }
        }
        $new_kvs_name = 'layer_tmp_kvs_' . $ekate;

        $transaction = $UsersController->startTransaction();

        try {
            $LayersController->setFilesProcessingStatus($file_id, SUCCESSFULLY_TREATED);
            $this->refreshEkateCombobox();
            if (!empty($kadidents)) {
                $options = [
                    'tablename' => $this->DbHandler->tableKVS,
                    'mainData' => [
                        'is_edited' => true,
                        'edit_date' => date('Y-m-d'),
                        'edit_active_from' => date('Y-m-d'),
                        'waiting_update' => false,
                    ],
                    'where' => [
                        'ekate' => $ekate,
                    ],
                    'id_name' => 'kad_ident',
                    'id_string' => $kadidents,
                ];

                $this->editItem($options);
            }

            $this->dropMaterializedView('kvs_contracts_update_' . $ekate);

            $this->dropTableKvsEkatte($new_kvs_name);
            $this->dropTableKvsEkatteInvalid($new_kvs_name);

            $transaction->commit();
        } catch (Exception $e) {
            $transaction->rollBack();

            throw $e;
        }

        $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, ['file_id' => $file_id], [], 'End Update');
    }

    public function logFileDeletion($fileId, $contractId, $isHardDeletion, $userId)
    {
        $date = date('Y F d H:i:s');

        $userIP = $_SERVER['REMOTE_ADDR'];
        if (array_key_exists('HTTP_X_REAL_IP', $_SERVER)) {
            $userIP = $_SERVER['HTTP_X_REAL_IP'];
        }

        $options = [
            'tablename' => $this->DbHandler->filesDeletionsLog,
            'mainData' => [
                'user_id' => $userId,
                'time_of_deletion' => $date,
                'ip' => $userIP,
                'file_id' => $fileId,
                'contract_id' => $contractId,
                'is_hard_deletion' => $isHardDeletion,
            ],
        ];

        return $this->DbHandler->addItem($options);
    }

    /**
     * @param bool $includeLineFeature
     * @param int $offset
     *
     * @throws TDbException
     *
     * @return array|false
     */
    public function getGeoJSON($options, $includeLineFeature = false, $offset = 0, $bindingParams = [])
    {
        return $this->DbHandler->getGeoJSON($options, $includeLineFeature, $offset, $bindingParams);
    }

    /**
     * @param int $offset
     *
     * @throws TDbException
     *
     * @return array|false
     */
    public function getLineFeatureGeoJson($options, $offset)
    {
        return $this->DbHandler->getLineFeatureGeoJson($options, $offset);
    }

    public function getKvsMapFileQuery()
    {
        return $this->DbHandler->getKvsMapFileQuery();
    }

    /**
     * @param string $ekatte
     *
     * @throws TDbException
     *
     * @return array
     */
    public function getEkatteContracts($ekatte)
    {
        return $this->DbHandler->contractsForEkatte($ekatte);
    }

    /**
     * @throws TDbException
     *
     * @return int
     */
    public function deleteEkatte($ekatte)
    {
        $this->disableRentaMatViewTriggers();
        $rows = $this->DbHandler->deleteEkatte($ekatte);
        $this->enableRentaMatViewTriggers();
        $this->DbHandler->refreshEkateCombobox();

        return $rows;
    }

    /**
     * @param null|mixed $options
     *
     * @return TDbTransaction
     */
    public function startTransaction($options = null)
    {
        return $this->DbHandler->startTransaction($options);
    }

    public function loadMapLayerTemplate($data)
    {
        return $this->StringHelper->loadTemplate($GLOBALS['Templates'][2]['template'], $data);
    }

    /**
     * @throws TDbException
     */
    public function getAdaptTFPluginFieldBoundaries($options)
    {
        $res = $this->DbHandler->getAdaptTFPluginFieldBoundaries($options);

        return json_decode($res['data'], true);
    }

    public function getAdaptTFPluginGuidance($options)
    {
        $res = $this->DbHandler->getAdaptTFPluginGuidanceInfo($options);

        return json_decode($res['data'], true);
    }

    /**
     * @throws TDbException
     */
    public function getAdaptTFPluginFieldsInfo($options, $farmName, $userName)
    {
        $res = $this->DbHandler->getAdaptTFPluginFieldsInfo($options, $farmName, $userName);

        return json_decode($res['data'], true);
    }

    public function getSubleasedPlotsOfContract($params, $counter = false, $returnOnlySQL = false)
    {
        $options = [
            'tablename' => $this->DbHandler->contractsPlotsRelTable . ' scpr',
            'return' => ["lk.kad_ident || '(' || sc.c_num || ')'  as res, sc.c_num as sublease_c_num, sspcr.pc_rel_id as sub_rels, sc.id as sublease_id, lk.gid as plot_id, sc.due_date as sublease_contract_end_date"],
            'where' => [
                'active' => ['column' => 'active', 'compare' => '=', 'prefix' => 'sc', 'value' => 'TRUE'],
            ],
            'joins' => [
                'inner join su_subleases_plots_contracts_rel sspcr on sspcr.pc_rel_id = scpr.id',
                'left join su_contracts sc on sc.id = sspcr.sublease_id',
                'left join layer_kvs lk on lk.gid = scpr.plot_id',
            ],
        ];

        if (!empty($params['contract_id'])) {
            $options['where']['contract_id'] = ['column' => 'contract_id', 'prefix' => 'scpr', 'compare' => '=', 'value' => $params['contract_id']];
        }

        if (!empty($params['plot_id'])) {
            $options['where']['plot_id'] = ['column' => 'plot_id', 'prefix' => 'scpr', 'compare' => '=', 'value' => $params['plot_id']];
        }

        return $this->getItemsByParams($options, $counter, $returnOnlySQL);
    }

    /**
     * @return mixed|void
     */
    public function getTableInfo($table)
    {
        return $this->DbHandler->getTableInfo($table);
    }

    public function transformGeom($geom, $fromCrs, $toCrs)
    {
        return $this->DbHandler->transformGeom($geom, $fromCrs, $toCrs);
    }

    public function multiUpdate(string $tableName, array $arrForUpdate, string $keyName, array $columnsToUpdate, array $columnCasts = []): array
    {
        return $this->DbHandler->multiUpdate($tableName, $arrForUpdate, $keyName, $columnsToUpdate, $columnCasts);
    }

    public function getPlotGeomInvalidContracts(DateTime $editActiveFrom, string $kadIdent)
    {
        return $this->DbHandler->getPlotGeomInvalidContracts($editActiveFrom, $kadIdent);
    }

    /**
     * Copy items from one layer to another.
     *
     * @param UserLayers $srcLayer Source layer
     * @param UserLayers $dstLayer Destination layer
     * @param array $gidsOrData An array containing data to copy from the source layer.
     *                          - When $copyFromSrcTable is true, this array should contain the GIDs of the source layer items to copy.
     *                          - When $copyFromSrcTable is false, this array should contain the data to copy.
     * @param bool $mergeNeighbourFeatures Whether to merge neighbour features
     * @param bool $copyFromSrcTable Whether to copy from the source table or from $data
     *
     * @throws TDbException
     */
    public function copyLayerItems(UserLayers $srcLayer, UserLayers $dstLayer, array $gidsOrData, bool $mergeNeighbourFeatures = false, bool $copyFromSrcTable = true)
    {
        $this->DbHandler->copyLayerItems($srcLayer, $dstLayer, $gidsOrData, $mergeNeighbourFeatures, $copyFromSrcTable);
    }

    /**
     * Get all owner payments amount by transaction ID. Grouped by owner ID, contract ID and Farming year.
     *
     * @param UserLayers $srcLayer Source layer
     * @param UserLayers $dstLayer Destination layer
     * @param array $gids Array of GIDs to copy
     *
     * @throws TDbException
     */
    public function getAllOwnerPaymentsAmountByTransactionId(int $transactionId)
    {
        return $this->DbHandler->getAllOwnerPaymentsAmountByTransactionId($transactionId);
    }

    public function getLayerColoringLegend(UserLayers $layer, string $ekatte = null): array
    {
        return $this->DbHandler->getLayerColoringLegend($layer, $ekatte);
    }

    public function deleteContractRentType($rentaTypeIds)
    {
        if (is_array($rentaTypeIds)) {
            $rentaTypeIds = implode(',', $rentaTypeIds);
        }
        $deleteOptions = [
            'tablename' => $this->DbHandler->contractsRentsTypesTable,
            'id_name' => 'id',
            'id_string' => $rentaTypeIds,
        ];

        $this->DbHandler->deleteItemsByParams($deleteOptions);
    }

    public function addContractRentTypes(int $contractId, array $rentaTypes)
    {
        foreach ($rentaTypes as $rentaType) {
            $this->addContractRentType($contractId, $rentaType);
        }
    }

    public function addContractRentType(int $contractId, array $rentaType)
    {
        $options = [
            'tablename' => $this->DbHandler->contractsRentsTypesTable,
            'mainData' => [
                'contract_id' => $contractId,
                'type' => $rentaType['type'],
                'value' => $rentaType['value'],
                'rents' => json_encode($rentaType['rents']),
            ],
        ];

        return $this->addItem($options);
    }

    public function hasDuplicatedContractRentType(array $rentTypes)
    {
        $rentTypesCatVal = [];
        foreach ($rentTypes as $rentType) {
            if (!isset($rentTypesCatVal[$rentType['type']][$rentType['value']])) {
                $rentTypesCatVal[$rentType['type']][$rentType['value']] = 1;
            } else {
                throw new MTRpcException('Duplicated rent type found: ' . $rentType['type'] . ' with value: ' . $rentType['value'], -32603);
            }
        }
    }

    public function hasAreaForRentType(array $rentTypeIds)
    {
        $UserDbContractsController = new UserDbContractsController($this->Database);
        $plotRents = $UserDbContractsController->getPlotsRents(['rent_type_ids' => $rentTypeIds]);
        $usedRentTypesInfo = [];
        foreach ($plotRents as $plotRent) {
            if ($plotRent['area'] > 0) {
                $usedRentTypesInfo[] = $plotRent['kad_ident'] . ' (' . $plotRent['area'] . ' дка)';
            }
        }

        if (empty($usedRentTypesInfo)) {
            return false;
        }

        if (1 === count($usedRentTypesInfo)) {
            $message = 'Рентата, която се опитвате да изтриете се използва в имот: <br><b>' . (implode('<br>', $usedRentTypesInfo)) . '</b><br> Моля занулете площта за избраната рента в горния имот, за да я изтриете.';
        } else {
            $message = 'Рентата, която се опитвате да изтриете се използва в следните имоти: <br><b>' . (implode('<br>', $usedRentTypesInfo)) . '</b><br> Моля занулете площта за избраната рента в горните имоти, за да я изтриете.';
        }

        throw new MTRpcException($message, -34070);
    }

    public function getContractRentTypes(int $contractId)
    {
        $result = [];

        $options = [
            'tablename' => $this->DbHandler->contractsRentsTypesTable . ' crt',
            'return' => [
                'crt.id',
                'contract_id',
                'type',
                'crt.value',
                'rents',
                'rto.title as typetext',
            ],
            'where' => [
                'contract_id' => ['column' => 'contract_id', 'compare' => '=', 'value' => $contractId],
            ],
            'joins' => [
                'LEFT JOIN su_renta_types_options rto ON rto.value = crt.type',
            ],
        ];

        $rentTypesResult = $this->getItemsByParams($options);

        foreach ($rentTypesResult as $rentType) {
            $rents = json_decode($rentType['rents'], true);
            $result[] = [
                'id' => $rentType['id'],
                'contract_id' => $rentType['contract_id'],
                'type' => $rentType['type'],
                'typeText' => $rentType['typetext'],
                'value' => $rentType['value'],
                'rent' => $rents['money'],
                'rent_text' => BGNtoEURO($rents['money']),
                'rent_nature' => $rents['rent_nature'],
                'rents' => $rents,
            ];
        }

        return $result;
    }

    public function updateContractRentType(array $rentaType)
    {
        $editOptions = [
            'tablename' => $this->DbHandler->contractsRentsTypesTable,
            'mainData' => [
                'type' => $rentaType['type'],
                'value' => $rentaType['value'],
                'rents' => json_encode($rentaType['rents']),
            ],
            'where' => [
                'id' => $rentaType['rentTypeId'],
            ],
        ];

        $this->editItem($editOptions);
    }

    public function dropViewIfExists(string $viewName)
    {
        $this->DbHandler->dropViewIfExists($viewName);
    }

    public function createUserFarmingPermissionsView(int $groupId)
    {
        $this->DbHandler->createUserFarmingPermissionsView($groupId);
    }

    /** From ZP to FOR_ISAK , ISAK, KMS,PERMANETELY_GREEN_AREAS, KVS, VPS_ORLI_LESHOYADI, GPS, WORK_LAYER.
     * @param null|mixed $ekate
     * @param null|mixed $definitions
     *
     *@deprecated Use method copyLayerItems instead
     */
    private function copyDataFromToLayerZP($tablenameFrom, $tablenameTo, $fromTableType, $idArray = [], $ekate = null, $definitions = null)
    {
        $toTableType = Config::LAYER_TYPE_ZP;
        $allowedFromTableTypes = [
            Config::LAYER_TYPE_FOR_ISAK, Config::LAYER_TYPE_ISAK, Config::LAYER_TYPE_KMS, Config::LAYER_TYPE_PERMANETELY_GREEN_AREAS,
            Config::LAYER_TYPE_KVS, Config::LAYER_TYPE_VPS_ORLI_LESHOYADI, Config::LAYER_TYPE_GPS, Config::LAYER_TYPE_WORK_LAYER,
            Config::LAYER_TYPE_ALLOWABLE_FINAL, Config::LAYER_TYPE_PHYSICAL_BLOCKS,
        ];

        if (!in_array($fromTableType, $allowedFromTableTypes)) {
            return false;
        }

        if (!$this->getTableNameExist($tablenameTo)) {
            $this->createTableZP($tablenameTo);
        }

        $this->DbHandler->insertIntoLayerZP($tablenameFrom, $fromTableType, $tablenameTo, 'gid', $idArray, $ekate, $definitions);
    }

    /**
     * @deprecated Use method copyLayerItems instead
     *
     * @param null|mixed $ekate
     * @param null|mixed $definitions
     */
    private function copyDataFromToWorkLayer($tablenameFrom, $tablenameTo, $fromTableType, $idArray = [], $ekate = null, $definitions = null)
    {
        $this->DbHandler->insertIntoWorkLayer($tablenameFrom, $tablenameTo, $fromTableType, $idArray, $ekate, $definitions);
    }

    /**
     * @deprecated Use method copyLayerItems instead
     *
     * @param null|mixed $ekate
     */
    private function copyDataFromToLayerForIsak($tablenameFrom, $tablenameTo, $fromTableType, $idArray = [], $farmingYear, $ekate = null)
    {
        $this->FarmingController = new FarmingController('Farming');
        $this->UsersController = new UsersController('Users');
        $toTableType = Config::LAYER_TYPE_FOR_ISAK;

        $idField = 'gid';
        if (Config::LAYER_TYPE_ZP == $fromTableType) {
            $idField = 'id';
        }

        // create tabele if the table does not exist
        if (!$this->getTableNameExist($tablenameTo)) {
            $this->createTableForISAK($tablenameTo);
        }

        $options = [
            'tablename' => 'su_crop_codes',
            'return' => ['crop_code'],
            'where' => [
                'year' => ['column' => 'year', 'compare' => '=', 'value' => $farmingYear],
            ],
        ];

        $idCropsArray = $this->UsersController->getItemsByParams($options);

        $this->DbHandler->insertIntoLayerForIsak($tablenameFrom, $fromTableType, $tablenameTo, $idField, $idArray, $idCropsArray, $ekate);

        // check if ZP or KMS crop_code exist but crop_name is NULL
        if (Config::LAYER_TYPE_ZP == $fromTableType || Config::LAYER_TYPE_KMS == $fromTableType) {
            $optionsSelect = [
                'tablename' => "{$tablenameTo}",
                'return' => ['cropcode, gid'],
                'where' => [
                    'cropname' => ['column' => 'cropname', 'compare' => 'IS', 'value' => 'NULL'],
                    'cropcode' => ['column' => 'cropcode', 'compare' => 'IS NOT', 'value' => 'NULL'],
                ],
            ];

            $results = $this->DbHandler->getItemsByParams($optionsSelect, false, false);
            $resultsCount = count($results);
            for ($i = 0; $i < $resultsCount; $i++) {
                $options['return'] = ['virtual_crop_name'];
                $options['where'] = ['crop_code' => ['column' => 'crop_code', 'compare' => '=', 'value' => $results[$i]['cropcode']]];

                $cropNames = $this->UsersController->getItemsByParams($options);

                $optionsUp = [
                    'tablename' => "{$tablenameTo}",
                    'where' => [
                        'gid' => $results[$i]['gid'],
                    ],
                    'mainData' => [
                        'cropname' => $cropNames[0]['virtual_crop_name'],
                    ],
                ];

                $this->DbHandler->editItem($optionsUp);
            }
        }
    }
}
