<?php

namespace TF\Engine\Plugins\Core\UserDbPlots;

use Exception;
use TF\Engine\Kernel\MTRpcException;
use TF\Engine\Plugins\Core\Farming\FarmingController;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\UserDbContracts\UserDbContractsController;

class UserDbPlotsController extends UserDbController
{
    /**
     * @var UserDbPlotsModel
     */
    public $DbHandler;
    public $Database;

    public function __construct($database)
    {
        $this->DbHandler = new UserDbPlotsModel($database);
        $this->Database = $database;
    }

    public function getPlotsContractsRelations($plotId, $includeRemovedPlots = false)
    {
        $FarmingController = new FarmingController('Farming');
        $userFarmings = $FarmingController->getUserFarmings();
        $userFarmingIds = array_keys($userFarmings);

        return $this->DbHandler->getPlotsContractsRelations($plotId, $userFarmingIds, $includeRemovedPlots);
    }

    public function getPlotDataByContractFilter($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getPlotDataByContractFilter($options, $counter, $returnOnlySQL);
    }

    public function getContractsDataForPlots($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getContractsDataForPlots($options, $counter, $returnOnlySQL);
    }

    public function KVSMultiEdit($options)
    {
        $this->DbHandler->KVSMultiEdit($options);
    }

    public function getPlotData($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getPlotData($options, $counter, $returnOnlySQL);
    }

    public function getPlotAreaReport($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getPlotAreaReport($options, $counter, $returnOnlySQL);
    }

    public function getPlotsArea($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getPlotsArea($options, $counter, $returnOnlySQL);
    }

    public function getPlotDataForDeclaration($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getPlotDataForDeclaration($options, $counter, $returnOnlySQL);
    }

    public function updatePlotContractStatus($id_string, $status)
    {
        $this->DbHandler->updatePlotContractStatus($id_string, $status);
    }

    public function getFullPlotData($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getFullPlotData($options, $counter, $returnOnlySQL);
    }

    public function getSubleasedPlotData($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getSubleasedPlotData($options, $counter, $returnOnlySQL);
    }

    public function getFullPlotDataForReport($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getFullPlotDataForReport($options, $counter, $returnOnlySQL);
    }

    public function getSubleasedPlotsReport($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getSubleasedPlotsReport($options, $counter, $returnOnlySQL);
    }

    public function getHypothecsPlotsReport($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getHypothecsPlotsReport($options, $counter, $returnOnlySQL);
    }

    public function getPlotsData($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getPlotsData($options, $counter, $returnOnlySQL);
    }

    public function getDataForPlotsTree($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getDataForPlotsTree($options, $counter, $returnOnlySQL);
    }

    public function getDataForPlotsExtent($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getDataForPlotsExtent($options, $counter, $returnOnlySQL);
    }

    public function getDataForPlotsTreeCounts($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getDataForPlotsTreeCounts($options, $counter, $returnOnlySQL);
    }

    public function getEditedPlots($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getEditedPlots($options, $counter, $returnOnlySQL);
    }

    public function getFullPlotDataForDeclaration($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getFullPlotDataForDeclaration($options, $counter, $returnOnlySQL);
    }

    public function getAgreementsPlotsFromList($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getAgreementsPlotsFromList($options, $counter, $returnOnlySQL);
    }

    public function getExistingEkateInKMS($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getExistingEkateInKMS($options, $counter, $returnOnlySQL);
    }

    public function getPlotNeighbours($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getPlotNeighbours($options, $counter, $returnOnlySQL);
    }

    public function getPlotSalesContractData($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getPlotSalesContractData($options, $counter, $returnOnlySQL);
    }

    public function getPlotsInSubleases($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getPlotsInSubleases($options, $counter, $returnOnlySQL);
    }

    public function getAvailablePlotsToSublease($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getAvailablePlotsToSublease($options, $counter, $returnOnlySQL);
    }

    public function getOwnerlessPlots($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getOwnerlessPlots($options, $counter, $returnOnlySQL);
    }

    public function getHistoricalPlotsReportData($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getHistoricalPlotsReportData($options, $counter, $returnOnlySQL);
    }

    public function getRentedPlotsForReport($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getRentedPlotsForReport($options, $counter, $returnOnlySQL);
    }

    public function getOwnPlotsForReport($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getOwnPlotsForReport($options, $counter, $returnOnlySQL);
    }

    public function getUsedPlotsForReport($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getUsedPlotsForReport($options, $counter, $returnOnlySQL);
    }

    public function getContractDetailsForPlotForReport($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getContractDetailsForPlotForReport($options, $counter, $returnOnlySQL);
    }

    public function getPlotOwnerDataByPlotID($plot_id)
    {
        return $this->DbHandler->getPlotOwnerDataByPlotID($plot_id);
    }

    public function getPlotDetailsFrom($options, $counter, $returnOnlySQL)
    {
        return $this->DbHandler->getPlotDetailsFrom($options, $counter, $returnOnlySQL);
    }

    public function getSubleasedPlotGidsForPeriod($start_date = '', $due_date = '')
    {
        return $this->DbHandler->getSubleasedPlotGidsForPeriod($start_date, $due_date);
    }

    public function getThematicMapInfoById($mapID)
    {
        return $this->DbHandler->getThematicMapInfoById($mapID);
    }

    public function getThematicMapInfoForKVSLayerWithContracts($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getThematicMapInfoForKVSLayerWithContracts($options, $counter, $returnOnlySQL);
    }

    public function getThematicMapInfoForKVSLayer($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getThematicMapInfoForKVSLayer($options, $counter, $returnOnlySQL);
    }

    public function getSoldPlotsData($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getSoldPlotsData($options, $counter, $returnOnlySQL);
    }

    public function updatePFRValues()
    {
        return $this->DbHandler->updatePFRValues();
    }

    public function getDataForTotalAreaReport($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getDataForTotalAreaReport($options, $counter, $returnOnlySQL);
    }

    public function getPlotsInExpiringContractsForReport($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getPlotsInExpiringContractsForReport($options, $counter, $returnOnlySQL);
    }

    public function getSubleasePcRelIds($start_date, $due_date)
    {
        return $this->DbHandler->getSubleasePcRelIds($start_date, $due_date);
    }

    public function getEkateNamesForContract($contractID)
    {
        return $this->DbHandler->getEkateNamesForContract($contractID);
    }

    public function getPlotsInSublease($params, $page = null, $rows = null, $sort = null, $order = null, $counter = false, $returnOnlySQL = false, $includeEditedPlots = false)
    {
        return $this->DbHandler->getPlotsInSublease($params, $page, $rows, $sort, $order, $counter, $returnOnlySQL, $includeEditedPlots);
    }

    public function getChosenPlotsForDecl69($rpcParams, $page = null, $rows = null, $sort = null, $order = null)
    {
        $farmingController = new FarmingController('Farming');

        $year = $GLOBALS['Farming']['years'][$rpcParams['year']]['year'];
        $farming_start_date = ($year - 1) . '-10-01';
        $farming_due_date = $year . '-09-30';

        $options = [
            'return' => [
                'gid',
                'kad_ident',
                'virtual_ntp_title as area_type',
                'area_type as area_type_code',
                'include',
                'participate',
                'white_spots',
                'round((pc.contract_area - sum(coalesce(sspa.contract_area, 0)) - sum(coalesce(sscpr.contract_area_for_sale, 0)))::numeric, 3) as area',
                'ekate',
                'virtual_ekatte_name as ekatte_name',
                'masiv',
                'number',
                'c.c_num',
                'TO_CHAR(c.c_date, \'DD.MM.YYYY\') as c_date',
                'pc.id::text as pc_rel_id',
                'c.id',
                'c.nm_usage_rights',
                'c.virtual_contract_type as c_type',
                'null as subleases_c_nums',
            ],
            'where' => [
                'ekate' => ['column' => 'ekate', 'compare' => '=', 'prefix' => 'kvs', 'value' => $rpcParams['ekate']],
                'masiv' => ['column' => 'masiv', 'compare' => '=', 'prefix' => 'kvs', 'value' => $rpcParams['masiv']],
                'imot' => ['column' => 'number', 'compare' => '=', 'prefix' => 'kvs', 'value' => $rpcParams['imot']],
                'contract_type' => ['column' => 'nm_usage_rights', 'compare' => 'IN', 'prefix' => 'c', 'value' => [1]],
                'farming' => ['column' => 'farming_id', 'compare' => '=', 'prefix' => 'c', 'value' => $rpcParams['farming']],
                'active' => ['column' => 'active', 'compare' => '=', 'prefix' => 'c', 'value' => 'TRUE'],
                'is_sublease' => ['column' => 'is_sublease', 'compare' => '=', 'prefix' => 'c', 'value' => 'FALSE'],
                'is_annex' => ['column' => 'is_annex', 'compare' => '=', 'prefix' => 'c', 'value' => 'FALSE'],
                'annex_action' => ['column' => 'annex_action', 'compare' => '=', 'prefix' => 'pc', 'value' => 'added'],
                'area_type' => ['column' => 'area_type', 'compare' => 'IN', 'prefix' => 'kvs', 'value' => $farmingController->ArrayHelper->filterEmptyStringArr($rpcParams['ntp'])],
                'c_date_from' => ['column' => 'c_date', 'compare' => '>=', 'prefix' => 'c', 'value' => $rpcParams['from_date']],
                'c_date_to' => ['column' => 'c_date', 'compare' => '<=', 'prefix' => 'c', 'value' => $rpcParams['to_date']],
                'archited_plot' => ['column' => '(kvs.is_edited = false or kvs.edit_active_from > \'' . $farming_due_date . '\')', 'compare' => '=', 'value' => 'true'],
            ],
            'joins' => [
                'left join su_contracts as scs on ( scs.id = spcr.sublease_id)',
                "left join su_subleases_plots_area as sspa on ( sspa.sublease_id = scs.id and sspa.plot_id = pc.plot_id and scs.active = true and scs.start_date <= '" . $farming_due_date . "' and scs.due_date >= '" . $farming_start_date . "' )",
                'left join su_sales_contracts_plots_rel sscprp on (sscprp.pc_rel_id = pc.id)',
                "left join su_sales_contracts as ssc on (ssc.id = sscprp.sales_contract_id and ssc.active = true and ssc.start_date <= '" . $farming_start_date . "')",
                'left join su_sales_contracts_plots_rel sscpr on (sscpr.pc_rel_id = pc.id and sscpr.sales_contract_id = ssc.id)',
            ],
            'group' => 'gid, pc.id, c.id',
            'having' => 'having sum(coalesce(sspa.contract_area, 0))::numeric(9, 3) + sum(coalesce(sscpr.contract_area_for_sale, 0))::numeric(9, 3) < pc.contract_area::numeric(9, 3)',
            'custom_counter' => 'COUNT(DISTINCT(pc.id))',
            'farming_start_date' => $farming_start_date,
            'farming_due_date' => $farming_due_date,
            'include_subleased_contracts' => true,
        ];

        if ('-1' == $options['where']['area_type']['value']) {
            $options['where']['area_type'] = ['column' => 'area_type', 'compare' => 'is', 'prefix' => 'kvs', 'value' => 'NULL'];
        }

        switch ($sort) {
            case 'kad_ident':
                $options['sort'] = 'kad_ident COLLATE "alpha_numeric_bg"';
                $options['order'] = $order;

                break;
            case 'area':
                $options['sort'] = 'area';
                $options['order'] = $order;

                break;
            case 'area_type':
                $options['sort'] = 'area_type COLLATE "alpha_numeric_bg"';
                $options['order'] = $order;

                break;
            case 'c_num':
                $options['sort'] = 'c_num COLLATE "alpha_numeric_bg"';
                $options['order'] = $order;

                break;
            case 'c_type':
                $options['sort'] = 'nm_usage_rights';
                $options['order'] = $order;

                break;
            default:
                break;
        }

        if (!empty($_SESSION['decl_array'])) {
            unset($options['where']['contract_type']);
            $subRels = [];
            $rels = [];
            foreach ($_SESSION['decl_array'] as $rel) {
                if (false !== strpos($rel, 'sub_')) {
                    $subRels[] = (int)str_replace('sub_', '', $rel);
                } else {
                    $rels[] = (int)$rel;
                }
            }

            if (!empty($rels)) {
                $options['pc_rel_id_string'] = implode(', ', $rels);
            } else {
                $options['pc_rel_id_string'] = '0';
            }

            if (!empty($subRels)) {
                $options['union']['pc_rel_id_string'] = implode(', ', $subRels);

                $options['union']['return'] = [
                    'gid',
                    'kad_ident',
                    'virtual_ntp_title as area_type',
                    'include',
                    'participate',
                    'white_spots',
                    'round((sum(coalesce(pc.contract_area, 0)) - sum(coalesce(sscpr.contract_area_for_sale, 0)))::numeric, 3) as area',
                    'ekate',
                    'virtual_ekatte_name as ekatte_name',
                    'masiv',
                    'number',
                    'c.c_num as c_num',
                    'TO_CHAR(c.c_date, \'DD.MM.YYYY\') as c_date',
                    "concat('sub_', spcr.id) as pc_rel_id",
                    'scs.id',
                    'c.nm_usage_rights',
                    'c.virtual_contract_type as c_type',
                    "string_agg(scs.c_num::varchar, ',') as subleases_c_nums",
                ];
                $options['union']['having'] = 'having sum(coalesce(sspa.contract_area, 0))::numeric(9, 3) > 0';
                $options['union']['group'] = 'gid, pc.id, spcr.id, scs.id, c.id';
            }
        }

        $plots = $this->getFullPlotDataForDeclaration($options, false, false);

        if (array_key_exists('split_legal_grounds', $rpcParams) && false == $rpcParams['split_legal_grounds']) {
            $plots = $this->sumPlotAreas($plots);
        }

        return $plots;
    }

    public function getChosenPlotsForDecl70($rpcParams, $page = null, $rows = null, $sort = null, $order = null)
    {
        $farmingController = new FarmingController('Farming');
        $year = $GLOBALS['Farming']['years'][$rpcParams['year']]['year'];
        $farming_start_date = ($year - 1) . '-10-01';
        $farming_due_date = $year . '-09-30';

        // options for plots-contracts query
        $options = [
            'return' => [
                'gid',
                'kad_ident',
                'virtual_ntp_title as area_type',
                'area_type as area_type_code',
                'include',
                'participate',
                'white_spots',
                'ROUND((CASE WHEN kvs.document_area IS NULL THEN St_Area(geom)/1000 ELSE kvs.document_area END)::numeric, 3) as document_area',
                'round((pc.contract_area - sum(coalesce(sspa.contract_area, 0)) - sum(coalesce(sscpr.contract_area_for_sale, 0)))::numeric, 3) as area',
                'ROUND((CASE WHEN kvs.document_area IS NULL THEN St_Area(geom)/1000 ELSE kvs.document_area END)::numeric, 3) as plot_area',
                'ekate',
                'virtual_ekatte_name as ekatte_name',
                'masiv',
                'number',
                'c.c_num',
                '(case when a.due_date is not null and a.due_date > c.due_date then TO_CHAR(a.start_date, \'DD.MM.YYYY\') else TO_CHAR(c.start_date, \'DD.MM.YYYY\') end) as start_date',
                '(case when a.due_date is not null and a.due_date > c.due_date then TO_CHAR(a.due_date, \'DD.MM.YYYY\') else TO_CHAR(c.due_date, \'DD.MM.YYYY\') end) as due_date',
                'TO_CHAR(c.c_date, \'DD.MM.YYYY\') as c_date',
                'c.sv_date',
                'c.sv_num',
                'pc.id::text as pc_rel_id',
                'c.nm_usage_rights::text',
                'c.virtual_contract_type as c_type',
                'c.is_declaration_subleased',
                'null as subleases_c_nums',
            ],
            'where' => [
                'ekate' => ['column' => 'ekate', 'compare' => '=', 'prefix' => 'kvs', 'value' => $rpcParams['ekate']],
                'masiv' => ['column' => 'masiv', 'compare' => '=', 'prefix' => 'kvs', 'value' => $rpcParams['masiv']],
                'imot' => ['column' => 'number', 'compare' => '=', 'prefix' => 'kvs', 'value' => $rpcParams['imot']],
                'contract_type' => ['column' => 'nm_usage_rights', 'compare' => 'NOT IN', 'prefix' => 'c', 'value' => [1, 4]],
                'farming' => ['column' => 'farming_id', 'compare' => '=', 'prefix' => 'c', 'value' => $rpcParams['farming']],
                'active' => ['column' => 'active', 'compare' => '=', 'prefix' => 'c', 'value' => 'TRUE'],
                'is_sublease' => ['column' => 'is_sublease', 'compare' => '=', 'prefix' => 'c', 'value' => 'FALSE'],
                'is_annex' => ['column' => 'is_annex', 'compare' => '=', 'prefix' => 'c', 'value' => 'FALSE'],
                'annex_action' => ['column' => 'annex_action', 'compare' => '=', 'prefix' => 'pc', 'value' => 'added'],
                'area_type' => ['column' => 'area_type', 'compare' => 'IN', 'prefix' => 'kvs', 'value' => $farmingController->ArrayHelper->filterEmptyStringArr($rpcParams['ntp'])],
                'c_date_from' => ['column' => 'c_date', 'compare' => '>=', 'prefix' => 'c', 'value' => $rpcParams['from_date']],
                'c_date_to' => ['column' => 'c_date', 'compare' => '<=', 'prefix' => 'c', 'value' => $rpcParams['to_date']],
                'archited_plot' => ['column' => '(kvs.is_edited = false or kvs.edit_active_from > \'' . $farming_due_date . '\')', 'compare' => '=', 'value' => 'true'],
            ],
            'joins' => [
                "left join su_contracts as scs on ( scs.id = spcr.sublease_id and scs.active = true and scs.start_date <= '" . $farming_due_date . "' and scs.due_date >= '" . $farming_start_date . "' )",
                'left join su_subleases_plots_area as sspa on ( sspa.sublease_id = scs.id and sspa.plot_id = pc.plot_id)',
                'left join su_sales_contracts_plots_rel sscprp on (sscprp.pc_rel_id = pc.id)',
                "left join su_sales_contracts as ssc on (ssc.id = sscprp.sales_contract_id and ssc.active = true and ssc.start_date <= '" . $farming_start_date . "')",
                'left join su_sales_contracts_plots_rel sscpr on (sscpr.pc_rel_id = pc.id and sscpr.sales_contract_id = ssc.id)',
            ],
            'group' => 'gid, pc.id, c.id, a.due_date, a.start_date',
            'having' => 'having sum(coalesce(sspa.contract_area, 0))::numeric(9, 3) + sum(coalesce(sscpr.contract_area_for_sale, 0))::numeric(9, 3) < pc.contract_area::numeric(9, 3)',
            'custom_counter' => 'COUNT(DISTINCT(pc.id))',
            'farming_start_date' => $farming_start_date,
            'farming_due_date' => $farming_due_date,
            'include_subleased_contracts' => true,
        ];

        if ('-1' == $options['where']['area_type']['value']) {
            $options['where']['area_type'] = ['column' => 'area_type', 'compare' => 'is', 'prefix' => 'kvs', 'value' => 'NULL'];
        }

        if (!empty($_SESSION['decl_array'])) {
            unset($options['where']['contract_type']);
            $subRels = [];
            $rels = [];

            foreach ($_SESSION['decl_array'] as $rel) {
                if (false !== strpos($rel, 'sub_')) {
                    $subRelArr = explode(',', $rel);
                    foreach ($subRelArr as $subRel) {
                        $subRels[] = (int)str_replace('sub_', '', $subRel);
                    }
                } else {
                    $rels[] = (int)$rel;
                }
            }

            if (!empty($rels)) {
                $options['pc_rel_id_string'] = implode(', ', $rels);
            } else {
                $options['pc_rel_id_string'] = '0';
            }

            if (!empty($subRels)) {
                $options['union']['pc_rel_id_string'] = implode(', ', $subRels);

                $options['union']['return'] = [
                    'gid',
                    'kad_ident',
                    'virtual_ntp_title as area_type',
                    'include',
                    'participate',
                    'white_spots',
                    'ROUND((CASE WHEN kvs.document_area IS NULL THEN St_Area(geom)/1000 ELSE kvs.document_area END)::numeric, 3) as document_area',
                    'round((sum(coalesce((CASE WHEN sspa.contract_area <= pc.contract_area THEN sspa.contract_area ELSE pc.contract_area END), 0)) - sum(coalesce(sscpr.contract_area_for_sale, 0)))::numeric, 3) as area',
                    '(CASE WHEN kvs.document_area IS NULL THEN St_Area(geom)/1000 ELSE kvs.document_area END) as plot_area',
                    'ekate',
                    'virtual_ekatte_name as ekatte_name',
                    'masiv',
                    'number',
                    'string_agg(c.c_num, \',\') as c_num',
                    '(case when a.due_date is not null and a.due_date > c.due_date then TO_CHAR(a.start_date, \'DD.MM.YYYY\') else TO_CHAR(c.start_date, \'DD.MM.YYYY\') end) as start_date',
                    '(case when a.due_date is not null and a.due_date > c.due_date then TO_CHAR(a.due_date, \'DD.MM.YYYY\') else TO_CHAR(c.due_date, \'DD.MM.YYYY\') end) as due_date',
                    'TO_CHAR(scs.c_date, \'DD.MM.YYYY\') as c_date',
                    'max(c.sv_date) as sv_date',
                    'max(c.sv_num) as sv_num',
                    'string_agg(concat(\'sub_\', spcr.id), \',\') as pc_rel_id',
                    'string_agg(c.nm_usage_rights::text, \',\') as nm_usage_rights',
                    'string_agg(c.virtual_contract_type::text, \',\') as c_type',
                    'c.is_declaration_subleased',
                    'string_agg(scs.c_num::text, \',\') as subleases_c_nums',
                ];
                $options['union']['where'] = [
                    // 'contract_type' => ['column' => 'nm_usage_rights', 'compare' => 'NOT IN', 'prefix' => 'c', 'value' => [1, 4]],
                ];
                $options['union']['having'] = 'having sum(coalesce(sspa.contract_area, 0))::numeric(9, 3) > 0';
                $options['union']['group'] = 'gid, scs.id, sspa.id, c.id, a.due_date, a.start_date';
            }
        }
        $plots = $this->getFullPlotDataForDeclaration($options, false, false);

        if (array_key_exists('split_legal_grounds', $rpcParams) && false == $rpcParams['split_legal_grounds']) {
            $plots = $this->sumPlotAreas($plots);
        }

        return $plots;
    }

    public function getChosenPlotsForAnketnaKarta($rpcParams, $page = null, $rows = null, $sort = null, $order = null)
    {
        $farmingController = new FarmingController('Farming');
        $year = $GLOBALS['Farming']['years'][$rpcParams['year']]['year'];
        $farming_start_date = ($year - 1) . '-10-01';
        $farming_due_date = $year . '-09-30';

        // options for plots-contracts query
        $options = [
            'return' => [
                'kvs.gid',
                'kad_ident',
                'area_type as area_type_code',
                'virtual_ntp_title as area_type',
                'max(sat.cat) as area_type_category',
                'include',
                'participate',
                'white_spots',
                '(CASE WHEN kvs.document_area IS NULL THEN St_Area(geom)/1000 ELSE kvs.document_area END) - sum(coalesce(sspa.contract_area, 0)) - sum(coalesce(sscpr.contract_area_for_sale, 0)) as document_area',
                'pc.contract_area::numeric(9, 3) - sum(coalesce(sspa.contract_area, 0))::numeric(9, 3) - sum(coalesce(sscpr.contract_area_for_sale, 0))::numeric(9, 3) as area',
                '(CASE WHEN kvs.document_area IS NULL THEN St_Area(geom)/1000 ELSE kvs.document_area END) as plot_area',
                'kvs.ekate',
                'kvs.virtual_ekatte_name as ekatte_name',
                'kvs.masiv',
                'number',
                'c.c_num',
                'TO_CHAR(c.c_date, \'DD.MM.YYYY\') as c_date',
                'pc.id::text as pc_rel_id',
                'c.start_date as start_date',
                'c.due_date as due_date',
                'c.nm_usage_rights',
                'c.virtual_contract_type as c_type',
                'null as subleases_c_nums',
                'ag.agg_type as agg_type',
            ],
            'where' => [
                'ekate' => ['column' => 'ekate', 'compare' => '=', 'prefix' => 'kvs', 'value' => $rpcParams['ekate']],
                'masiv' => ['column' => 'masiv', 'compare' => '=', 'prefix' => 'kvs', 'value' => $rpcParams['masiv']],
                'imot' => ['column' => 'number', 'compare' => '=', 'prefix' => 'kvs', 'value' => $rpcParams['imot']],
                'farming' => ['column' => 'farming_id', 'compare' => '=', 'prefix' => 'c', 'value' => $rpcParams['farming']],
                'active' => ['column' => 'active', 'compare' => '=', 'prefix' => 'c', 'value' => 'TRUE'],
                'is_sublease' => ['column' => 'is_sublease', 'compare' => '=', 'prefix' => 'c', 'value' => 'FALSE'],
                'is_annex' => ['column' => 'is_annex', 'compare' => '=', 'prefix' => 'c', 'value' => 'FALSE'],
                'annex_action' => ['column' => 'annex_action', 'compare' => '=', 'prefix' => 'pc', 'value' => 'added'],
                'area_type' => ['column' => 'area_type', 'compare' => 'IN', 'prefix' => 'kvs', 'value' => $farmingController->ArrayHelper->filterEmptyStringArr($rpcParams['ntp'])],
                'c_date_from' => ['column' => 'c_date', 'compare' => '>=', 'prefix' => 'c', 'value' => $rpcParams['from_date']],
                'c_date_to' => ['column' => 'c_date', 'compare' => '<=', 'prefix' => 'c', 'value' => $rpcParams['to_date']],
                'archited_plot' => ['column' => '(kvs.is_edited = false or kvs.edit_active_from > \'' . $farming_due_date . '\')', 'compare' => '=', 'value' => 'true'],
            ],
            'joins' => [
                'scs' => 'left join su_contracts as scs on ( scs.id = spcr.sublease_id)',
                'sspa' => "left join su_subleases_plots_area as sspa on ( sspa.sublease_id = scs.id and sspa.plot_id = pc.plot_id and scs.active = true and scs.start_date <= '" . $farming_due_date . "' and scs.due_date >= '" . $farming_start_date . "' )",
                'sscprp' => 'left join su_sales_contracts_plots_rel sscprp on (sscprp.pc_rel_id = pc.id)',
                'ssc' => "left join su_sales_contracts as ssc on (ssc.id = sscprp.sales_contract_id and ssc.active = true and ssc.start_date <= '" . $farming_start_date . "')",
                'sscpr' => 'left join su_sales_contracts_plots_rel sscpr on (sscpr.pc_rel_id = pc.id and sscpr.sales_contract_id = ssc.id)',
                'ag' => 'left join lateral (
                    select sad.gid, sa2.contract_id, sa2.agg_type from su_agreements_data sad
                    inner join su_agreements sa2 on sad.agreement_id = sa2.id
                    where 
                        sad.ekate = kvs.ekate
                        and sa2.farming = c.farming_id
                        and sa2.year = ' . $rpcParams['year'] . '
                    limit 1
                ) ag on true',
            ],
            'group' => 'kvs.gid, pc.id, c.id, ag.contract_id, ag.agg_type',
            'having' => 'having sum(coalesce(sspa.contract_area, 0))::numeric(9, 3) + sum(coalesce(sscpr.contract_area_for_sale, 0))::numeric(9, 3) < pc.contract_area::numeric(9, 3) and (case when max(ag.gid) is not null then max(ag.contract_id) = c.id or kvs.participate = false else true end)',
            'custom_counter' => 'COUNT(DISTINCT(pc.id))',
            'farming_start_date' => $farming_start_date,
            'farming_due_date' => $farming_due_date,
            'include_subleased_contracts' => true,
        ];

        if (!empty($rpcParams['excelExport'])) {
            $options['return'] = [
                'kvs.gid',
                'kad_ident',
                'area_type as area_type_code',
                'virtual_ntp_title as area_type',
                'sat.cat as area_type_category',
                'include',
                'participate',
                'white_spots',
                '(CASE WHEN kvs.document_area IS NULL THEN St_Area(geom)/1000 ELSE kvs.document_area END) - sum(coalesce(sspa.contract_area, 0)) - sum(coalesce(sscpr.contract_area_for_sale, 0)) as document_area',
                'sum(pc.contract_area) - sum(coalesce(sspa.contract_area, 0)) - sum(coalesce(sscpr.contract_area_for_sale, 0)) as area',
                '(CASE WHEN kvs.document_area IS NULL THEN St_Area(geom)/1000 ELSE kvs.document_area END) as plot_area',
                'kvs.ekate',
                'kvs.virtual_ekatte_name as ekatte_name',
                'kvs.masiv',
                'number',
                'max(c.c_num) as c_num',
                "TO_CHAR(MAX(scs.c_date), 'DD.MM.YYYY') AS c_date",
                'max(pc.id)::text as pc_rel_id',
                'max(c.start_date) as start_date',
                'max(c.due_date) as due_date',
                'max(c.nm_usage_rights) as nm_usage_rights',
                'c.virtual_contract_type as c_type',
                'null as subleases_c_nums',
                'ag.agg_type as agg_type',
            ];

            $options['group'] = 'kvs.gid, ag.agg_type, sat.cat, c.virtual_contract_type';
            $options['having'] = '';
        }

        if ('-1' == $options['where']['area_type']['value']) {
            $options['where']['area_type'] = ['column' => 'area_type', 'compare' => 'is', 'prefix' => 'kvs', 'value' => 'NULL'];
        }

        if (!empty($_SESSION['decl_array'])) {
            $options['having'] = '';

            $subRels = [];
            $rels = [];

            foreach ($_SESSION['decl_array'] as $rel) {
                if (false !== strpos($rel, 'sub_')) {
                    $subRels[] = (int)str_replace('sub_', '', $rel);
                } else {
                    $rels[] = (int)$rel;
                }
            }

            if (!empty($rels)) {
                $options['pc_rel_id_string'] = implode(', ', $rels);
            } else {
                $options['pc_rel_id_string'] = '0';
            }
            if (!empty($subRels)) {
                $options['union']['pc_rel_id_string'] = implode(', ', $subRels);

                $options['union']['return'] = [
                    'kvs.gid',
                    'kad_ident',
                    'area_type as area_type_code',
                    'virtual_ntp_title as area_type',
                    'sat.cat as area_type_category',
                    'include',
                    'participate',
                    'white_spots',
                    '(CASE WHEN kvs.document_area IS NULL THEN St_Area(geom)/1000 ELSE kvs.document_area END) - sum(coalesce(sspa.contract_area, 0)) - sum(coalesce(sscpr.contract_area_for_sale, 0)) as document_area',
                    'sum(coalesce(sspa.contract_area, 0)) - sum(coalesce(sscpr.contract_area_for_sale, 0)) as area',
                    '(CASE WHEN kvs.document_area IS NULL THEN St_Area(geom)/1000 ELSE kvs.document_area END) as plot_area',
                    'kvs.ekate',
                    'kvs.virtual_ekatte_name as ekatte_name',
                    'kvs.masiv',
                    'number',
                    'max(scs.c_num) as c_num',
                    "TO_CHAR(MAX(scs.c_date), 'DD.MM.YYYY') AS c_date",
                    "concat('sub_', spcr.id) as pc_rel_id",
                    'max(scs.start_date) as start_date',
                    'max(scs.due_date) as due_date',
                    'max(scs.nm_usage_rights) as nm_usage_rights',
                    'c.virtual_contract_type as c_type',
                    "string_agg(scs.c_num::varchar, ',') as subleases_c_nums",
                    'ag.agg_type as agg_type',
                ];

                $options['union']['having'] = 'having sum(coalesce(sspa.contract_area, 0))::numeric(9, 3) > 0';
                $options['union']['group'] = 'kvs.gid, pc.id, spcr.id, ag.agg_type, sat.cat, c.virtual_contract_type';
            }

            if (!empty($rpcParams['excelExport'])) {
                $options['return'] = [
                    'kvs.gid',
                    'kad_ident',
                    'area_type as area_type_code',
                    'virtual_ntp_title as area_type',
                    'sat.cat as area_type_category',
                    'include',
                    'participate',
                    'white_spots',
                    '(CASE WHEN kvs.document_area IS NULL THEN St_Area(geom)/1000 ELSE kvs.document_area END) - sum(coalesce(sspa.contract_area, 0)) - sum(coalesce(sscpr.contract_area_for_sale, 0)) as document_area',
                    'sum(pc.contract_area) - sum(coalesce(sspa.contract_area, 0)) - sum(coalesce(sscpr.contract_area_for_sale, 0)) as area',
                    '(CASE WHEN kvs.document_area IS NULL THEN St_Area(geom)/1000 ELSE kvs.document_area END) as plot_area',
                    'kvs.ekate',
                    'kvs.virtual_ekatte_name as ekatte_name',
                    'kvs.masiv',
                    'number',
                    'max(c.c_num) as c_num',
                    "TO_CHAR(MAX(scs.c_date), 'DD.MM.YYYY') AS c_date",
                    'null as pc_rel_id',
                    'max(c.start_date) as start_date',
                    'max(c.due_date) as due_date',
                    'max(c.nm_usage_rights) as nm_usage_rights',
                    'c.virtual_contract_type as c_type',
                    "string_agg(c.c_num::varchar, ',') as subleases_c_nums",
                    'ag.agg_type as agg_type',
                ];

                $options['group'] = 'kvs.gid, ag.agg_type, sat.cat, c.virtual_contract_type';
                $options['having'] = '';
            }
        }

        return $this->getFullPlotDataForDeclaration($options, false, false);
    }

    public function getConsolidationSourceData(string $kadIdent)
    {
        return $this->DbHandler->getConsolidationSourceData($kadIdent);
    }

    public function getPlotDocuments(string $kadIdent, int $year, int $page = null, int $rows = null, string $sort = null, string $order = null)
    {
        return $this->DbHandler->getPlotDocuments($kadIdent, $year, $page, $rows, $sort, $order);
    }

    public function savePlotRent($params)
    {
        $UserDbController = new UserDbController($this->Database);
        $UserDbContractsController = new UserDbContractsController($this->Database);
        $editedPlotsRentArea = 0;

        if (empty($params['type'])) {
            throw new MTRpcException('Missing renta type value', -34065);
        }

        if (PLOT_RENT_TYPE_GENERIC != $params['type'] && empty($params['value'])) {
            throw new MTRpcException('Missing renta type value', -34064);
        }

        $genericOptions = [
            'pc_rel_id' => $params['pc_rel_id'],
        ];
        // get plot rents data
        $plotsRents = $UserDbContractsController->getPlotsRents($genericOptions);

        foreach ($plotsRents as $plotsRent) {
            if (PLOT_RENT_TYPE_GENERIC == $plotsRent['type']) {
                $genericPlotsRent = $plotsRent;
            }

            if (empty($params['edit_plot_rent_id']) && $plotsRent['type'] == $params['type'] && $params['value'] == $plotsRent['value']) {
                throw new MTRpcException('This type value already exists', -34066);
            }
        }

        if ($params['edit_plot_rent_id']) {
            $editedPlotRentOptions = [
                'plot_rent_id' => $params['edit_plot_rent_id'],
            ];
            $editedPlotsRent = $UserDbContractsController->getPlotsRents($editedPlotRentOptions);
            $editedPlotsRentArea = $editedPlotsRent[0]['area'];
        }

        if (empty($genericPlotsRent)) {
            throw new MTRpcException('Missing generitic plot rent type', -32603);
        }

        if (!empty($genericPlotsRent['area'] < $params['area'])) {
            throw new MTRpcException('Not enought generic area', -34063);
        }

        try {
            $transaction = $UserDbController->startTransaction();
            // substract the new type area from generic plot area
            $editGeneric = [
                'tablename' => 'su_plots_rents',
                'mainData' => [
                    'area' => $genericPlotsRent['area'] + ($editedPlotsRentArea - $params['area']),
                ],
                'where' => ['id' => $genericPlotsRent['plot_rent_id']],
            ];

            $UserDbController->editItem($editGeneric);

            $rents = [
                'money' => $params['rent'],
                'rent_per_plot' => $params['rent_per_plot'],
                'rent_nature' => $params['rent_nature'] ?? [],
            ];

            if (!empty($params['edit_plot_rent_id'])) {
                $options = [
                    'tablename' => 'su_plots_rents',
                    'mainData' => [
                        'type' => $params['type'],
                        'value' => $params['value'],
                        'area' => $params['area'],
                        'rents' => json_encode($rents),
                    ],
                    'where' => ['id' => $params['edit_plot_rent_id']],
                ];

                $UserDbController->editItem($options);
            } else {
                // add the new plot rent
                $options = [
                    'tablename' => 'su_plots_rents',
                    'mainData' => [
                        'pc_rel_id' => $params['pc_rel_id'],
                        'type' => $params['type'],
                        'value' => $params['value'],
                        'area' => $params['area'],
                        'rents' => json_encode($rents),
                    ],
                ];

                $UserDbController->addItem($options);
            }

            $transaction->commit();
        } catch (Exception $e) {
            $transaction->rollback();

            throw new MTRpcException($e->getMessage(), -33752);
        }
    }

    public function deletePlotRent($params)
    {
        $UserDbController = new UserDbController($this->Database);
        $UserDbContractsController = new UserDbContractsController($this->Database);

        if (PLOT_RENT_TYPE_GENERIC == $params['type']) {
            throw new MTRpcException('Generic type can\'t be deleted.', -34061);
        }

        $options = [
            'pc_rel_id' => $params['pc_rel_id'],
            'rent_type' => 'generic',
        ];
        // get plot rents data
        $plotsRentGeneric = $UserDbContractsController->getPlotsRents($options);

        try {
            $transaction = $UserDbController->startTransaction();

            $options = [
                'tablename' => 'su_plots_rents',
                'id_name' => 'id',
                'id_string' => $params['plot_rent_id'],
            ];
            $UserDbController->deleteItemsByParams($options);

            // add the deleted type area to generic plot area
            $editGeneric = [
                'tablename' => 'su_plots_rents',
                'mainData' => [
                    'area' => $plotsRentGeneric[0]['area'] + $params['area'],
                ],
                'where' => ['id' => $plotsRentGeneric[0]['plot_rent_id']],
            ];

            $UserDbController->editItem($editGeneric);

            $transaction->commit();
        } catch (Exception $e) {
            $transaction->rollback();

            // Check if exeption message contains violates foreign key constraint "su_charged_renta_su_plots_rents_fk" and throw custom exception
            if (false !== strpos($e->getMessage(), 'violates foreign key constraint "su_charged_renta_su_plots_rents_fk"')) {
                throw new MTRpcException('The record has charged rent', -34062);
            }

            throw new MTRpcException($e->getMessage(), -32603);
        }
    }

    /**
     * @return array
     */
    private function sumPlotAreas(array $plots)
    {
        $groupedPlots = [];
        foreach ($plots as $plot) {
            if (!array_key_exists($plot['gid'], $groupedPlots)) {
                $groupedPlots[$plot['gid']] = $plot;

                continue;
            }
            $groupedPlots[$plot['gid']]['area'] += round($plot['area'], 3);
        }

        return array_values($groupedPlots);
    }
}
