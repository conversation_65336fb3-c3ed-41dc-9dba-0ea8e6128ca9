<?php

namespace TF\Engine\Plugins\Core\Layers;

use Exception;
use Prado\Prado;
use Prado\Security\IUser;
use Prado\Web\Services\TJsonRpcProtocol;
use Prado\Web\Services\TRpcServer;
use TF\Application\Common\Config;
use TF\Application\Entity\DefaultColors;
use TF\Application\Entity\LayerStyles;
use TF\Application\Entity\User;
use TF\Application\Entity\UserLayers;
use TF\Engine\APIClasses\Map\MapTools;
use TF\Engine\Kernel\StringHelper;
use TF\Engine\Plugins\Core\Base\BaseController;
use TF\Engine\Plugins\Core\Interfaces\ILayerable;
use TF\Engine\Plugins\Core\UserDb\UserDbController;
use TF\Engine\Plugins\Core\Users\UsersController;

/**
 * PlotsController class file.
 *
 * <AUTHOR> Ltd. <<EMAIL>>
 *
 * @link http://www.devision.bg/
 *
 * @copyright Copyright &copy; 2008 Devision Ltd.
 */
// Prado::using('Plugins.Core.Base.BaseController');
// Prado::using('Plugins.Core.UserDb.UserDbController');

/**
 * LayersController class.
 *
 * This is the controller class for the Base plugin
 *
 * @property LayersModel $DbHandler
 * @property File $File
 * @property StringHelper $StringHelper
 */
class LayersController extends BaseController implements ILayerable
{
    public const LAYER_COLORING_BY_OWNERS_NAME = 1;
    public const LAYER_COLORING_BY_TENANTS_NAME = 2;
    public const LAYER_COLORING_BY_AGREEMENT = 3;
    public const LAYER_COLORING_BY_CATEGORY = 4;
    public const LAYER_COLORING_BY_NTP = 5;
    public const LAYER_COLORING_BY_OWNERSHIP = 6;

    public UserDbController $UserDbController;
    public UsersController $UsersController;
    public $StringHelper;

    private $wrapCharacter = "\n";

    public function __construct($param = '')
    {
        parent::__construct($param);

        $this->StringHelper = new StringHelper();
    }

    public function getHomeItems(&$settings = [])
    {
        $orderby = $this->getOrderBy($settings['place']);
        $ordertype = $this->getOrderType($settings['place']);
        // var_dump($ordertype);
        $options = [
            'return' => $settings['return'],
            'user_id' => $settings['user_id'],
            'farming' => $settings['farming'],
            'farm_id' => $settings['farm_id'],
            'not_system' => $settings['not_system'],
            'layer_type' => $settings['layer_type'],
            'year' => $settings['year'],
            'orderby' => '' != $settings['orderby'] ? $settings['orderby'] : $orderby,
            'ordertype' => '' != $settings['ordertype'] ? $settings['ordertype'] : $ordertype,
            'offset' => $settings['offset'],
            'limit' => $settings['limit'],
            'name' => $settings['name'],
        ];

        if ($settings['keywords']) {
            $keywords = '%' . $settings['keywords'] . '%';
            $options['custom'] = [
                'fields' => ['value'],
                'values' => [$keywords],
            ];
        }

        $result = [];
        if (isset($settings['count'])) {
            $options['count'] = $settings['count'];
            $result[$settings['count'] ? 'count' : 'data'] = $this->DbHandler->getHomeItems($options);
        } else {
            $options['count'] = true;
            $result['count'] = $this->DbHandler->getHomeItems($options);
            $options['count'] = false;
            $result['data'] = $this->DbHandler->getHomeItems($options);
        }

        return $result;
    }

    public function getHomeFilesItems(&$settings = [])
    {
        $orderby = $this->getOrderBy($settings['place']);
        $ordertype = $this->getOrderType($settings['place']);

        $options = [
            'return' => $settings['return'],
            'user_id' => $settings['user_id'],
            'farming' => $settings['farming'],
            'farm_id' => $settings['farm_id'],
            'year' => $settings['year'],
            'orderby' => '' != $settings['orderby'] ? $settings['orderby'] : $orderby,
            'ordertype' => '' != $settings['ordertype'] ? $settings['ordertype'] : $ordertype,
            'offset' => $settings['offset'],
            'limit' => $settings['limit'],
            'name' => $settings['name'],
        ];

        if ($settings['keywords']) {
            $keywords = '%' . $settings['keywords'] . '%';
            $options['custom'] = [
                'fields' => ['value'],
                'values' => [$keywords],
            ];
        }

        $result = [];
        if (isset($settings['count'])) {
            $options['count'] = $settings['count'];
            $result[$settings['count'] ? 'count' : 'data'] = $this->DbHandler->getHomeFilesItems($options);
        } else {
            $options['count'] = true;
            $result['count'] = $this->DbHandler->getHomeFilesItems($options);
            $options['count'] = false;
            $result['data'] = $this->DbHandler->getHomeFilesItems($options);
        }

        return $result;
    }

    public function getFiles($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getFiles($options, $counter, $returnOnlySQL);
    }

    public function getLayers($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getLayers($options, $counter, $returnOnlySQL);
    }

    public function getLayersPosition($source, $target, $point)
    {
        // getting the data for both elements from the database
        $sourceData = $this->DbHandler->getLayerData($source);
        $targetData = $this->DbHandler->getLayerData($target);

        return $this->DbHandler->setLayersPosition($sourceData, $targetData, $point);
    }

    public function addFilesItem(&$settings)
    {
        $table = $this->DbHandler->tableNameFiles;
        $fields = array_keys($settings['mainData']);
        $values = array_values($settings['mainData']);
        $id = $this->DbHandler->addItem($table, $fields, $values);

        return $id;
    }

    public function addLayerItem(&$settings)
    {
        if (!isset($settings['mainData']['layer_type'])) {
            throw new Exception('Invalid layer type');
        }

        $style = LayerStyles::generateDefaultStyle($settings['mainData']['layer_type']);
        $style['color'] = $settings['mainData']['color'] ?? $style['color'];

        $definitions = UserLayers::getDefinitionsByType($settings['mainData']['layer_type']);
        $settings['mainData']['definitions'] = json_encode($definitions);

        $settings['mainData']['color'] = $style['color'];
        $settings['mainData']['border_color'] = $style['border_color'];
        $settings['mainData']['transparency'] = $style['transparency'];
        $settings['mainData']['label_name'] = count($style['label_name']) > 0 ? 'Array' : null;
        $settings['mainData']['style'] = Config::LAYER_TYPE_KVS !== $settings['mainData']['layer_type']
            ? json_encode($style)
            : '{}';

        $table = $this->DbHandler->tableName;
        $fields = array_keys($settings['mainData']);
        $values = array_values($settings['mainData']);
        $id = $this->DbHandler->addItem($table, $fields, $values);

        return $id;
    }

    public function getGreatestLayerPosition($user_id)
    {
        return $this->DbHandler->getGreatestLayerPosition($user_id);
    }

    public function getTableNameByParams($options)
    {
        $farming = $options['farming'];
        $year = $options['year'];
        $group_id = $options['group_id'];
        $layer_type = $options['layer_type'];

        $tableName = $this->DbHandler->tableName;
        if (isset($options['year'])) {
            $arrayKeys = ['farming', 'year', 'group_id', 'layer_type'];
            $arrayValues = [$farming, $year, $group_id, $layer_type];
        } else {
            $arrayKeys = ['farming', 'group_id', 'layer_type'];
            $arrayValues = [$farming, $group_id, $layer_type];
        }
        $data = $this->DbHandler->getItem($tableName, ['table_name'], $arrayKeys, $arrayValues);

        return $data['table_name'];
    }

    public function getLayersIdByParams($options)
    {
        $farming = $options['farming'];
        $year = $options['year'];
        $user_id = $options['user_id'];
        $layer_type = $options['layer_type'];

        if (isset($options['return'])) {
            $return = $options['return'];
        } else {
            $return = ['id'];
        }

        if (isset($options['year'])) {
            $arrayKeys = ['farming', 'year', 'user_id', 'layer_type'];
            $arrayValues = [$farming, $year, $user_id, $layer_type];
        } else {
            $arrayKeys = ['farming', 'user_id', 'layer_type'];
            $arrayValues = [$farming, $user_id, $layer_type];
        }

        $tableName = $this->DbHandler->tableName;
        $data = $this->DbHandler->getItem($tableName, $return, $arrayKeys, $arrayValues);
        if (isset($options['return'])) {
            return $data;
        }

        return $data['id'];
    }

    public function getLayersIdByLayerType($options)
    {
        $group_id = $options['group_id'];
        $layer_type = $options['layer_type'];

        $tableName = $this->DbHandler->tableName;
        $data = $this->DbHandler->getItem($tableName, ['id'], ['group_id', 'layer_type'], [$group_id, $layer_type]);

        return $data['id'];
    }

    public function editItemFiles(&$settings)
    {
        $setFields = array_keys($settings['mainData']);
        $setValues = array_values($settings['mainData']);
        $whereFields = ['id'];
        $whereValues = [$settings['id']];

        $id = $this->DbHandler->editItem($this->DbHandler->tableNameFiles, $setFields, $setValues, $whereFields, $whereValues);

        return $id;
    }

    public function editItemCopyTable(&$settings)
    {
        $setFields = array_keys($settings['mainData']);
        $setValues = array_values($settings['mainData']);
        $whereFields = ['id'];
        $whereValues = [$settings['id']];
        $id = $this->DbHandler->editItem($this->DbHandler->copyLayersTable, $setFields, $setValues, $whereFields, $whereValues);

        return $id;
    }

    /**
     * Deletes items from the db.
     *
     * @param array $arrayID
     */
    public function deleteLayersItems($arrayID = [], $user_id, $database)
    {
        foreach ($arrayID as $id) {
            $table = $this->DbHandler->tableNameFiles;
            $this->DbHandler->deleteItem($table, ['id', 'user_id'], [$id, $user_id]);
        }
    }

    public function getFilesProcessingStatus($id)
    {
        return $this->DbHandler->getFilesProcessingStatus($id);
    }

    public function setFilesProcessingStatus($id, $status)
    {
        return $this->DbHandler->setFilesProcessingStatus($id, $status);
    }

    public function getFilesForProcessing($fordefinition = false)
    {
        return $this->DbHandler->getFilesForProcessing($fordefinition);
    }

    public function getKVSFilesForProcessing($fordefinition)
    {
        return $this->DbHandler->getKVSFilesForProcessing($fordefinition);
    }

    public function getNavigationFilesForProcessing($fordefinition)
    {
        return $this->DbHandler->getNavigationFilesForProcessing($fordefinition);
    }

    public function getOszKVSFilesForProcessing()
    {
        return $this->DbHandler->getOszKVSFilesForProcessing();
    }

    public function getKVSExcelFilesForProcessing()
    {
        return $this->DbHandler->getKVSExcelFilesForProcessing();
    }

    public function getCsdFilesForProcessing()
    {
        return $this->DbHandler->getCsdFilesForProcessing();
    }

    public function getFilesDataById($id)
    {
        return $this->DbHandler->getFilesDataById($id);
    }

    public function setKVSProccessed($id)
    {
        return $this->DbHandler->setKVSProccessed($id);
    }

    public function setCopyProccessed($id)
    {
        return $this->DbHandler->setCopyProccessed($id);
    }

    public function getActiveLayersByUserId($userid)
    {
        return $this->DbHandler->getActiveLayersByUserId($userid);
    }

    public function getActiveLayersWithStyles(User $user)
    {
        return $this->DbHandler->getActiveLayersWithStyles($user);
    }

    public function getTableNameExist(string $tablename)
    {
        return $this->DbHandler->getTableNameExist($tablename);
    }

    public function getViewNameExists(string $viewName)
    {
        return $this->DbHandler->getViewNameExists($viewName);
    }

    public function getItemsByParams(array $options, bool $counter = false, bool $returnOnlySQL = false, array $bindingParams = [])
    {
        return $this->DbHandler->getRemoteLayerData($options, $counter, $returnOnlySQL, $bindingParams);
    }

    /**
     *  @todo This logic should be handled in the UserDbController or in a service used by the UserDb database.
     *
     * @return array|string
     */
    public function updateLayerExtent(UserLayers $layer)
    {
        $user = Prado::getApplication()->getModule('auth')->getUser();
        // init userDbController
        $this->UserDbController = new UserDbController($user->Database);

        $maxExtent = $this->UserDbController->getMaxExtent($layer->table_name);

        if ($maxExtent) {
            $maxExtent = str_replace('BOX(', '', $maxExtent);
            $maxExtent = str_replace(')', '', $maxExtent);
            $maxExtent = str_replace(',', ' ', $maxExtent);
        } else {
            $maxExtent = Config::DEFAULT_MAX_EXTENT;
        }

        $options = [];
        $options['mainData'] = [
            'extent' => $maxExtent,
            'is_exist' => true,
        ];
        $options['id'] = $layer->id;
        $this->editItem($options);

        return $maxExtent;
    }

    public function generateMapFile($options, bool $enableDebug = false)
    {
        $userId = $options['user_id'];
        $database = $options['database'];
        $server = new TRpcServer(new TJsonRpcProtocol());
        $mapTools = new MapTools($server);

        $this->UserDbController = new UserDbController($database);
        $this->UsersController = new UsersController('Users');
        $mapFileContent = 'MAP';

        if ($enableDebug) {
            $logFilePath = WMS_MAP_PATH . 'ms_err.txt';

            if (!file_exists($logFilePath)) {
                touch($logFilePath);
            }

            $mapFileContent .= $this->generateMapDebug($logFilePath);
        }

        $mapFileContent .= $this->generateMapHead();

        /**
         * @var string $kvsQuery
         *
         * @deprecated  $kvsQuery is used for WMS KVS layer. Remove when switch to mvt layers.
         */
        $kvsQuery = $this->UserDbController->getKvsMapFileQuery();
        $kvsExtent = null;
        $kvsColor = null;
        $kvsBorder = null;

        $user = User::finder()->find('id = :user_id', [':user_id' => $userId]);
        $userLayersStyles = $this->getActiveLayersWithStyles($user);

        /**
         * @var string $mapLayersContent Contains the layer data (e.g. query, styles, etc) for each layer (in .map format)
         */
        $mapLayersContent = '';
        foreach ($userLayersStyles as $userLayerStyles) {
            $styles = $userLayerStyles->styles;
            $layer = $userLayerStyles->userLayer;

            [$firstStyle] = $styles;
            $gidDef = $layer->getDefinitionByCategory(Config::LAYER_COLUMN_CATEGORY_GID);

            $data = [];
            $data['gid'] = $gidDef['col_name'];
            $data['layerid'] = $layer->id;
            $data['layername'] = $layer->table_name;
            $data['layertype'] = $layer->layer_type;
            $data['host'] = DEFAULT_DB_HOST;
            $data['dbname'] = $database;
            $data['username'] = DEFAULT_DB_USERNAME;
            $data['password'] = DEFAULT_DB_PASSWORD;
            $data['port'] = DEFAULT_DB_PORT;
            $data['query_columns'] = $this->generateMapColumnsToSelect($layer, $mapTools);
            $data['query'] = $this->generateMapQuery($layer, $data['query_columns']);
            $data['maxextent'] = $layer->extent;
            $data['transparency'] = $firstStyle->transparency;
            $data['tag_label'] = 'label';

            /**
             * @var array data['classes']
             *
             * @deprecated  The 'classes' key is used for defining WMS layers styles using 'CLASS' objects in the map file.
             * Delete them when switch to mvt layers.
             */
            $data['classes'] = [];

            if (Config::LAYER_TYPE_KVS != $layer->layer_type) {
                $data['classes'] = [$this->generateWmsLayerStyleClass($layer, $firstStyle, $this->wrapCharacter)];
            }

            if (Config::LAYER_TYPE_KVS == $layer->layer_type) {
                foreach ($styles as $style) {
                    // For kvs layers_id contain layer_id + ekatte.
                    // This means that if we have more than one style for the same ekatte, the last one will be taken;
                    $data['classes'][$style->layer_id] = $this->generateWmsLayerStyleClass($layer, $style, $this->wrapCharacter);
                }

                $data['classitem'] = 'ekate';
                $data['classes'] = array_values($data['classes']);
                $data['validation'] = [
                    'ekatte' => '^[0-9a-zA-Z,-]+$',
                    'default_ekatte' => 'null',
                    'plot_statuses' => '^[a-zA-Z,]+$',
                    'default_plot_statuses' => 'null',
                    '__keycloak_uid__' => '^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[1-5][0-9a-fA-F]{3}-[89abAB][0-9a-fA-F]{3}-[0-9a-fA-F]{12}$',
                    '__default_keycloak_uid__' => 'null',
                ];

                $layerKvsMvt = $this->StringHelper->loadTemplate($GLOBALS['Templates'][49]['template'], $data);

                @unlink(WMS_MAP_PATH . $userId . '_layer_kvs_mvt.map');
                $fp = fopen(WMS_MAP_PATH . $userId . '_layer_kvs_mvt.map', 'w');
                fwrite($fp, $layerKvsMvt);
                fclose($fp);

                /**
                 * @deprecated
                 * $data['query'] is used for WMS KVS layer (for $GLOBALS['Templates'][2]['template']). Remove when switch to mvt layers.
                 */
                $data['query'] = $kvsQuery;
                $kvsExtent = $data['maxextent'];
                $kvsColor = $data['classes'][0]['color'];
                $kvsBorder = $data['classes'][0]['border_color'];
            }

            $mapLayersContent .= $this->StringHelper->loadTemplate($GLOBALS['Templates'][2]['template'], $data);
        }

        $mapFileContent .= $mapLayersContent;
        $mapFileContent .= "\n";

        // Include the separate map files in the user's map file
        $mapFileContent .= "INCLUDE '" . WMS_MAP_PATH . $userId . "_layer_kvs_mvt.map'\n";
        $mapFileContent .= "INCLUDE '" . WMS_MAP_PATH . $userId . "_layer_kvs.map'\n";
        // Used for Агротехника module
        $mapFileContent .= "INCLUDE '" . WMS_MAP_PATH . $userId . "_layer_zp.map'\n";
        $mapFileContent .= "INCLUDE '" . WMS_MAP_PATH . $userId . "_layer_cov.map'\n";
        $mapFileContent .= "INCLUDE '" . WMS_MAP_PATH . $userId . "_ab_lines.map'\n";
        $mapFileContent .= 'END';

        // Save user's map file
        @unlink(WMS_MAP_PATH . $userId . '.map');
        $fp = fopen(WMS_MAP_PATH . $userId . '.map', 'w');
        fwrite($fp, $mapFileContent);
        fclose($fp);

        // Generate map files used in module Агротехника
        $layer_kvs_map = '';
        $data['layername'] = 'topic_kvs_layer';
        $data['maxextent'] = $kvsExtent;
        $data['host'] = DEFAULT_DB_HOST;
        $data['dbname'] = $database;
        $data['username'] = DEFAULT_DB_USERNAME;
        $data['password'] = DEFAULT_DB_PASSWORD;
        $data['port'] = DEFAULT_DB_PORT;
        $data['query'] = 'layer_kvs';
        $data['gid'] = 'gid';
        $data['transparency'] = '100';
        $data['classes'][0]['name'] = 'topic_kvs_layer';
        $data['classes'][0]['border_color'] = $kvsBorder;
        $data['classes'][0]['color'] = $kvsColor;

        $layer_kvs_map .= $this->StringHelper->loadTemplate($GLOBALS['Templates'][2]['template'], $data);

        @unlink(WMS_MAP_PATH . $userId . '_layer_kvs.map');
        $fp = fopen(WMS_MAP_PATH . $userId . '_layer_kvs.map', 'w');
        fwrite($fp, $layer_kvs_map);
        fclose($fp);

        $data['layername'] = 'topic_zp_layer';
        $data['maxextent'] = $kvsExtent;
        $data['host'] = DEFAULT_DB_HOST;
        $data['dbname'] = $database;
        $data['username'] = DEFAULT_DB_USERNAME;
        $data['password'] = DEFAULT_DB_PASSWORD;
        $data['port'] = DEFAULT_DB_PORT;
        $data['query'] = 'layer_kvs';
        $data['gid'] = 'gid';
        $data['transparency'] = '100';
        $data['classes'][0]['name'] = 'topic_zp_layer';
        $data['classes'][0]['border_color'] = $kvsBorder;
        $data['classes'][0]['color'] = $kvsColor;

        @unlink(WMS_MAP_PATH . $userId . '_layer_zp.map');
        $fp = fopen(WMS_MAP_PATH . $userId . '_layer_zp.map', 'w');
        $layerZpMap = $this->StringHelper->loadTemplate($GLOBALS['Templates'][2]['template'], $data);
        fwrite($fp, $layerZpMap);
        fclose($fp);

        fopen(WMS_MAP_PATH . $userId . '_layer_cov.map', 'w');
        fopen(WMS_MAP_PATH . $userId . '_layer_kvs.map', 'w');
        fopen(WMS_MAP_PATH . $userId . '_ab_lines.map', 'a');
    }

    public function updateUnstyledEkkates($database, $layer_id)
    {
        $UserDbController = new UserDbController($database);
        $UsersController = new UsersController('Users');
        $layer = $this->getLayerData($layer_id);
        $style = json_decode($layer['style'], true);
        $default_label_size = 8;

        $options = [
            'return' => ['DISTINCT(ekate)'],
            'tablename' => $UserDbController->DbHandler->tableKVS,
        ];

        $results = $UserDbController->getItemsByParams($options);
        $resultsCount = count($results);
        for ($i = 0; $i < $resultsCount; $i++) {
            $ekatte = $results[$i]['ekate'];
            if ('' == $ekatte || null == $ekatte) {
                continue;
            }
            if (!array_key_exists($ekatte, $style)) {
                $style[$ekatte] = [
                    'color' => $this->StringHelper->randomColorCode(),
                    'border_color' => $this->StringHelper->randomColorCode(),
                    'transparency' => 50,
                    'border_only' => false,
                    'label_name' => ['kad_ident'],
                    'tags' => true,
                    'label_size' => $default_label_size,
                ];
            }
        }
        $jsonStyle = json_encode($style);

        $options = [
            'id' => $layer_id,
            'mainData' => [
                'style' => $jsonStyle,
            ],
        ];

        $this->editItem($options);
        $UsersController->groupLog($this->User->Name, $this->User->UserID, $this->User->GroupID, $this->module, $this->service_id, __METHOD__, ['new_data' => $options], [], 'Save Layer personalization on kvs upload');
    }

    public function getLayerData($layer_id)
    {
        return $this->DbHandler->getLayerData($layer_id);
    }

    public function testForPolyIntersection($polygonA, $polygonB, $crs)
    {
        return $this->DbHandler->testForPolyIntersection($polygonA, $polygonB, $crs);
    }

    public function isPolyInBulgaria($polygon, $crs = 32635)
    {
        $bulgariaPolygon = [
            32635 => 'POLYGON((118076.45 4567156.59,629789.92 4567156.59,629789.92 4905928.86,118076.45 4905928.86,118076.45 4567156.59))',
            4326 => 'POLYGON((22.4480002327 41.1657654299, 28.5489872674 41.2454035698, 28.6269285661 44.2950281948, 22.2198710176 44.2064873067, 22.4480002327 41.1657654299))',
        ];

        return $this->testForPolyIntersection($polygon, $bulgariaPolygon[$crs], $crs);
    }

    public function copyKVSByEKATTE($ekatte)
    {
        return $this->DbHandler->copyKVSByEKATTE($ekatte);
    }

    public function getTableNameByType($userid, $type)
    {
        return $this->DbHandler->getTableNameByType($userid, $type);
    }

    public function getTmpLayersForProcessing($fordefinition = false)
    {
        return $this->DbHandler->getTmpLayersForProcessing($fordefinition);
    }

    public function getWorkLayersForProcessing($fordefinition = false)
    {
        return $this->DbHandler->getWorkLayersForProcessing($fordefinition);
    }

    public function getAllowableLayerData($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getAllowableLayerData($options, $counter, $returnOnlySQL);
    }

    public function getRemoteLayerData($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getRemoteLayerData($options, $counter, $returnOnlySQL);
    }

    public function getRemoteLayerDataWithoutTable($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getRemoteLayerDataWithoutTable($options, $counter, $returnOnlySQL);
    }

    public function getRemoteLayerOrliLeshoyadiData($options, $counter = false, $returnOnlySQL = false)
    {
        return $this->DbHandler->getRemoteLayerOrliLeshoyadiData($options, $counter, $returnOnlySQL);
    }

    public function getLayersCombobox($options)
    {
        return $this->DbHandler->getLayersCombobox($options);
    }

    public function getFarmingNameByLayerId($layer_id)
    {
        return $this->DbHandler->getFarmingNameByLayerId($layer_id);
    }

    public function getColumnNameExistInDefaultDatabase($tablename, $columnName)
    {
        return $this->DbHandler->getColumnNameExistInDefaultDatabase($tablename, $columnName);
    }

    public function updateUsersFilesEkate($id, $ekate)
    {
        $this->DbHandler->updateUsersFilesEkate($id, $ekate);
    }

    public function updateUsersFilesCrs($id, $ekate)
    {
        return $this->DbHandler->updateUsersFilesCrs($id, $ekate);
    }

    public function getLayersByType($layer_type, $user_id)
    {
        return $this->DbHandler->getLayersByType($layer_type, $user_id);
    }

    /**
     * @param string $name
     *
     * @return string
     */
    public function getNextWorkLayerName($user_id, $name = 'Работен слой')
    {
        $biggerNum = null;
        $workLayersNames = $this->DbHandler->getLayersNames($user_id, 19, $name);
        foreach ($workLayersNames as $layersName) {
            $nameNum = trim(str_replace($name, '', $layersName['name']));
            if (is_numeric($nameNum) && $nameNum > $biggerNum) {
                $biggerNum = $nameNum;
            }
        }

        return !is_null($biggerNum) ? $name . ' ' . ($biggerNum + 1) : $name . ' 1';
    }

    public function getWorkLayers($user_id)
    {
        return $this->DbHandler->getWorkLayers($user_id);
    }

    public function getWorkLayersWithFarmings($user_id, $work_layer_id = 0)
    {
        return $this->DbHandler->getWorkLayersWithFarmings($user_id, $work_layer_id);
    }

    public function deleteWorkLayer($groupID, $layerID, $tablename)
    {
        return $this->DbHandler->deleteWorkLayer($groupID, $layerID, $tablename);
    }

    public function getFarmingYearByLayerID($layerID)
    {
        return $this->DbHandler->getFarmingYearByLayerID($layerID);
    }

    public function createSafeIntersection()
    {
        return $this->DbHandler->createSafeIntersection();
    }

    /**
     * @param bool $includeLineFeature
     * @param int $offset
     *
     * @throws TDbException
     *
     * @return array|false
     */
    public function getGeoJSON($options, $includeLineFeature = false, $offset = 0)
    {
        return $this->DbHandler->getGeoJSON($options, $includeLineFeature, $offset);
    }

    // get all zp layers tablenames
    public function getLayersTablenames()
    {
        $user = Prado::getApplication()->getModule('auth')->getUser();
        $layersController = new LayersController('Layers');
        $layers_tablenames = [];
        $options = [
            'where' => [
                'group_id' => ['column' => 'group_id', 'compare' => '=', 'prefix' => 't', 'value' => $user->GroupID],
                'layer_type' => ['column' => 'layer_type', 'compare' => '=', 'prefix' => 't', 'value' => Config::LAYER_TYPE_ZP],
            ],
        ];
        $layer_results = $layersController->getLayers($options);
        if (empty($layer_results)) {
            return $layers_tablenames;
        }
        foreach ($layer_results as $layer_result) {
            $layers_tablenames[$layer_result['farming'] . '-' . $layer_result['year']] = $layer_result['table_name'];
        }

        return $layers_tablenames;
    }

    public function getExistingLayersTables($layer_tables)
    {
        $user = Prado::getApplication()->getModule('auth')->getUser();
        // init userDbController
        $this->UserDbController = new UserDbController($user->Database);

        $return = [];
        $layer_tables_options = [
            'tablename' => 'pg_class',
            'return' => ['relname AS table_name'],
            'where' => [
                'relname' => ['column' => 'relname', 'compare' => 'IN', 'value' => array_values($layer_tables)],
            ],
        ];
        $existing_layer_tables = $this->UserDbController->getItemsByParams($layer_tables_options, false, false);

        $layerTablesCount = count($existing_layer_tables);
        for ($i = 0; $i < $layerTablesCount; $i++) {
            $zp_key = array_search($existing_layer_tables[$i]['table_name'], $layer_tables);
            // @noinspection OffsetOperationsInspection
            $return[$zp_key] = $existing_layer_tables[$i]['table_name'];
        }

        return $return;
    }

    /**
     * @return mixed|void
     */
    public function getFarmInfoByTable($table)
    {
        return $this->DbHandler->getFarmInfoByTable($table);
    }

    public function getDataByQuery(string $query, array $params = [])
    {
        $cmd = $this->DbHandler->createCommand($query);

        foreach ($params as $key => $value) {
            $cmd->bindParameter(":{$key}", $value);
        }

        return $cmd->query()->readAll();
    }

    /**
     * @see ILayerable::getMaxExtent
     */
    public function getMaxExtent(string $tableName)
    {
        return $this->DbHandler->getMaxExtent($tableName);
    }

    public static function getColoringLayer(int $type)
    {
        $coloringLayers = [
            self::LAYER_COLORING_BY_OWNERS_NAME => $GLOBALS['Templates'][31]['template'],
            self::LAYER_COLORING_BY_TENANTS_NAME => $GLOBALS['Templates'][32]['template'],
            self::LAYER_COLORING_BY_AGREEMENT => $GLOBALS['Templates'][33]['template'],
            self::LAYER_COLORING_BY_CATEGORY => $GLOBALS['Templates'][34]['template'],
            self::LAYER_COLORING_BY_NTP => $GLOBALS['Templates'][35]['template'],
            self::LAYER_COLORING_BY_OWNERSHIP => $GLOBALS['Templates'][36]['template'],
        ];

        return $coloringLayers[$type] ?? null;
    }

    public function getAllowableLayersNtpData()
    {
        return $this->DbHandler->getAllowableLayersNtpData();
    }

    /**
     * Summary of saveLayerPersonalization.
     *
     * @return array
     *
     * @todo This logic should be handled in the UserDbController or in a service used by the UserDb database.
     */
    public function saveLayerPersonalization(UserLayers $layer, $rpcParams, $module, $service_id)
    {
        $user = Prado::getApplication()->getModule('auth')->getUser();
        $this->UserDbController = new UserDbController($user->Database);
        $UsersController = new UsersController('Users');

        if (Config::LAYER_TYPE_WORK_LAYER == $layer->layer_type) {
            $layer->name = $rpcParams['layer_name'] ?? $layer->name;
            $layer->farming = isset($rpcParams['layer_farming']) ? ('' === $rpcParams['layer_farming'] ? null : $rpcParams['layer_farming']) : $layer->farming;
            $layer->year = isset($rpcParams['layer_year']) ? ('' === $rpcParams['layer_year'] ? null : $rpcParams['layer_year']) : $layer->year;
            $layer->save();
        }

        LayerStyles::BY_ATTRIBUTE_COLORING_TYPE == $rpcParams['type']
            ? $this->updateLayerStyleByAttribute($layer, $rpcParams)
            : $this->updateLayerStyleSingle($layer, $rpcParams);

        $UsersController->groupLog($user->Name, $user->UserID, $user->GroupID, $module, $service_id, __METHOD__, ['new_data' => $rpcParams], [], 'Save Layer personalization');

        $rpcParams['database'] = $user->Database;
        $rpcParams['user_id'] = $user->GroupID;

        $this->updateLayerExtent($layer);

        $this->generateMapFile($rpcParams);

        if ($rpcParams['ekatte']) {
            $this->deleteGeneratedImagesForEkatte($rpcParams['ekatte'], $user);
        }

        return [
            'extent' => str_replace(' ', ', ', $layer->extent),
            'layer_name' => $layer->table_name,
        ];
    }

    protected function checkTableExists($dbHandler, $tableName)
    {
        $sql = 'SELECT table_name FROM information_schema.tables WHERE table_name = :tableName';
        $cmd = $dbHandler->DbModule->createCommand($sql);
        $cmd->bindValue(':tableName', $tableName);
        $result = $cmd->queryScalar();

        return false !== $result;
    }

    private function generateMapDebug(string $logFilePath, int $debugLevel = 5)
    {
        return "
            CONFIG \"MS_ERRORFILE\"  \"{$logFilePath}\"
            CONFIG \"CPL_DEBUG\" \"ON\"
            CONFIG \"PROJ_DEBUG\" \"ON\"
            DEBUG {$debugLevel}
        ";
    }

    private function generateMapHead(): string
    {
        return $this->StringHelper->loadTemplate(
            $GLOBALS['Templates'][1]['template'],
            [
                'wmsserver' => WMS_SERVER,
                'wmssrs' => WMS_SRS,
                'wfsserver' => WMS_SERVER,
                'wfssrs' => WMS_SRS,
            ]
        );
    }

    /**
     * This function uses the keys of the given array as column aliases and the values as column names.
     * The aliases are wrapped inside double quotes.
     * The column names are wrapped inside double quotes only if they are not functions (means they don't  contain '(' or ')').
     *
     * @return array the result represents array where the keys are column aliases and the values are column expressions casted to the aliases
     */
    private function formatColumnsForQuery(array $columns, string $prefix = null): array
    {
        $formattedColumns = [];
        $prefix ??= '';

        foreach ($columns as $alias => $columnExpression) {
            if (false !== strpos(strtolower($columnExpression), ' as ')) {
                // the value is a column expression with an alias => no need to add an alias
                $formattedColumns[$alias] = $columnExpression;

                continue;
            }

            // Remove quotes if already present
            $alias = str_replace('\\"', '', $alias);
            $columnExpression = str_replace('\\"', '', $columnExpression);

            $existingPrefix = false !== strpos($columnExpression, '.')
                ? explode('.', $columnExpression)[0]
                : null;

            // If the column expression already has a prefix use it instead of using the $prefix parameter
            $prefixStr = ($existingPrefix ?? $prefix ?? '') . '.';

            // Remove prefix if already present to avoid duplicated prefix
            $columnExpression = str_replace("{$prefixStr}", '', $columnExpression);

            if ($alias === $columnExpression && false === strpos($columnExpression, '(') && false === strpos($columnExpression, ')')) {
                // Column expression is same as the alias and it is not a function => no need to add an alias
                $formattedColumns[$alias] = "{$prefixStr}\\\"{$columnExpression}\\\"";

                continue;
            }

            if (false !== strpos($columnExpression, '(') && false !== strpos($columnExpression, ')')) {
                // the value is a function or a raw value (do not use prefix and quotes for it)
                $formattedColumns[$alias] = "{$columnExpression} as \\\"{$alias}\\\"";

                continue;
            }

            if (false !== strpos($columnExpression, '::')) {
                // has type cast
                [$columnExpression, $typeCast] = explode('::', $columnExpression);
                $formattedColumns[$alias] = "{$prefixStr}\\\"{$columnExpression}\\\"::{$typeCast} as \\\"{$alias}\\\"";

                continue;
            }

            if (false !== strpos($columnExpression, '::')) {
                // has type cast
                [$columnExpression, $typeCast] = explode('::', $columnExpression);
                $formattedColumns[$alias] = "{$prefixStr}\\\"{$columnExpression}\\\"::{$typeCast} as \\\"{$alias}\\\"";

                continue;
            }

            // the value is a column expression
            $formattedColumns[$alias] = "{$prefixStr}\\\"{$columnExpression}\\\" as \\\"{$alias}\\\"";
        }

        return $formattedColumns;
    }

    private function generateMapColumnsToSelect(UserLayers $layer, MapTools $mapTools): array
    {
        $layerDefinitions = $layer->getDefinitions();

        $personalizableOrVisibleColDefs = UserLayers::filterDefinitions($layerDefinitions, [['col_personalizable' => true], ['col_visible' => true]]);
        $personalizableOrVisibleCols = UserLayers::getColumns($personalizableOrVisibleColDefs);
        $personalizableOrVisibleCols = array_combine($personalizableOrVisibleCols, $personalizableOrVisibleCols);

        $referenceCols = array_filter(
            array_column($layerDefinitions, 'col_reference'),
            fn ($col) => !is_null($col)
        );
        $referenceCols = array_combine($referenceCols, $referenceCols);

        $colorColumnDefs = UserLayers::filterDefinitions($layerDefinitions, [['col_category' => Config::LAYER_COLUMN_CATEGORY_COLOR]]);
        $colorColumns = UserLayers::getColumns($colorColumnDefs);
        $colorColumns = array_combine($colorColumns, $colorColumns);
        $gidDef = $layer->getDefinitionByCategory(Config::LAYER_COLUMN_CATEGORY_GID);
        $geomDef = $layer->getDefinitionByCategory(Config::LAYER_COLUMN_CATEGORY_GEOM);
        $labelDef = $layer->getDefinitionByCategory(Config::LAYER_COLUMN_CATEGORY_LABEL);
        $farmName = addslashes($layer->getFarmingName());
        $additionalColumnsByLayerType = $this->getAdditionalMapColumnsToSelectByLayerType($layer, $mapTools);
        $layerName = addslashes($layer->name);

        $layerTableColumns = $this->formatColumnsForQuery(
            array_merge(
                [
                    $gidDef['col_name'] => $gidDef['col_name'], // the column of category gid might have different name than 'gid' e.g. 'id'
                    'gid' => $gidDef['col_name'],
                    'geom' => $geomDef['col_name'],
                    'label' => $labelDef['col_name'],
                    'layer_type' => "({$layer->layer_type})",
                    'layer_label' => "replace('{$layerName}', E'\\', '') as layer_label",
                    'layer_id' => "({$layer->id})",
                    'farm_name' => $farmName
                        ? "replace('{$farmName}', E'\\', '') as farm_name"
                        : 'null as farm_name',
                    'farm_year' => $layer->getFarmingYear()
                        ? "'{$layer->getFarmingYear()}' as farm_year"
                        : 'null as farm_year',
                    'extent' => "JSONB_BUILD_ARRAY(
                        St_Xmin(St_Transform({$layer->table_name}.geom, 3857)), 
                        St_Ymin(St_Transform({$layer->table_name}.geom, 3857)),
                        St_Xmax(St_Transform({$layer->table_name}.geom, 3857)),
                        St_Ymax(St_Transform({$layer->table_name}.geom, 3857))
                    ) as extent",
                ],
                $personalizableOrVisibleCols,
                $colorColumns,
                $referenceCols,
                $additionalColumnsByLayerType
            ),
            $layer->table_name
        );

        $stylesTable = LayerStyles::TABLE;
        $stylesTableColumns = [
            'border_width' => 'border_width',
            'border_only' => 'border_only',
            'transparency' => 'transparency',
            'label_size' => 'label_size',
            'fill_column_name' => 'fill_column_name',
            'border_column_name' => 'border_column_name',
            'coloring_type' => 'type',
            'style_layer_id' => 'layer_id',
        ];
        $stylesTableColumns = $this->formatColumnsForQuery($stylesTableColumns, $stylesTable);

        return
            array_merge(
                $layerTableColumns,
                $stylesTableColumns
            );
    }

    private function getAdditionalMapColumnsToSelectByLayerType(UserLayers $layer, MapTools $mapTools): array
    {
        if (Config::LAYER_TYPE_WORK_LAYER == $layer->layer_type) {
            $columnsList = $this->UserDbController->getTableColumnsList($layer->table_name);

            $definitions = $layer->getDefinitions();
            $defKeys = array_column($definitions, 'col_name');
            $definitions = array_combine($defKeys, $definitions);

            $columns = $mapTools->formatAndTranslateColumns($columnsList, $definitions);

            return [
                'translation' => "'" . str_replace('"', '\"', json_encode($columns)) . "'::JSONB as translation",
            ];
        }

        if (Config::LAYER_TYPE_KVS == $layer->layer_type) {
            $ekatteDef = $layer->getDefinitionByCategory(Config::LAYER_COLUMN_CATEGORY_EKATTE);
            [$virtualEkatteDef] = UserLayers::filterDefinitions($layer->getDefinitions(), [['col_reference' => $ekatteDef['col_name']]]);

            return [
                'number_of_contracts' => 'uccps.contracts_count_by_status AS number_of_contracts',
                'layer_label' => $virtualEkatteDef['col_name'] . " || '({$layer->name})' as layer_label",
                'allow_prec' => "round((case when {$layer->table_name}.allowable_area is not null and {$layer->table_name}.allowable_area > 0 then {$layer->table_name}.allowable_area / (ST_AREA({$layer->table_name}.geom)/1000) * 100 else 0 end)::numeric,2) || ' %' as allow_prec",
                'owners_osz' => "(case when string_agg(tkvs.egn_subekt, ',') is not null then string_agg(distinct tkvs.owner_name || ' (' || tkvs.egn_subekt || ')', ', ') else string_agg(tkvs.owner_name, ', ') end) as owners_osz",
                'farm_name' => 'null as farm_name', // replace the existing value with null because kvs layers shoukd not have farming name
                'farm_year' => 'null as farm_year', // replace the existing value with null because kvs layers shoukd not have farming year
            ];
        }

        return [];
    }

    private function generateMapQuery(UserLayers $layer, array $columnsToSelect, int $srid = 32635): string
    {
        $queryColumns = implode(",\n", array_values($columnsToSelect));
        $gidDef = $layer->getDefinitionByCategory(Config::LAYER_COLUMN_CATEGORY_GID);

        $query = "SELECT
                {$queryColumns}
            FROM
                {$layer->table_name}
            LEFT JOIN su_layer_styles 
                ON su_layer_styles.layer_id = '{$layer->id}'
        ";

        return "({$query}) AS subq USING UNIQUE {$gidDef['col_name']} USING srid={$srid}";
    }

    private function generateWmsLayerStyleClass(UserLayers $layer, LayerStyles $style, string $wrapCharacter): array
    {
        $fillColor = str_replace('#', '', $style->fill_color);
        $borderColor = str_replace('#', '', $style->border_color);

        $styleClass = [
            'color' => ($style->border_only)
                ? false
                : (hexdec(substr($fillColor, 0, 2)) . ' '
                    . hexdec(substr($fillColor, 2, 2)) . ' '
                    . hexdec(substr($fillColor, 4, 2))),
            'border_color' => hexdec(substr($borderColor, 0, 2)) . ' '
                . hexdec(substr($borderColor, 2, 2)) . ' '
                . hexdec(substr($borderColor, 4, 2)),
            'tags' => $style->tags,
            'size' => (null != $style->label_size) ? $style->label_size : Config::LAYER_LABEL_DEFAULT_SIZE,
            'wrap_character' => $wrapCharacter,
            'transparency' => 100 - ($style->transparency ?? 0),
        ];

        if ($this->UserDbController->DbHandler->tableKVS === $style->table_name) {
            $labelDef = $layer->getDefinitionByCategory(Config::LAYER_COLUMN_CATEGORY_LABEL);
            // KVS layer
            [$layerId, $ekatte] = explode('_', $style->layer_id);

            return array_merge(
                $styleClass,
                [
                    'name' => 'ekatte_' . $ekatte,
                    'expression' => '\'' . $ekatte . '\'',
                    'border_width' => $style->border_width ?? Config::LAYER_BOUNDARY_DEFAULT_BORDER_WIDTH,
                    'label_text' => '\'' . "[{$labelDef['col_name']}]" . '\'',
                ]
            );
        }

        // Other layers
        return array_merge(
            $styleClass,
            [
                'name' => $style->table_name,
                'width' => $style->border_width ?? Config::LAYER_BOUNDARY_DEFAULT_BORDER_WIDTH,
            ]
        );
    }

    private function updateLayerStyleSingle(UserLayers $layer, array $rpcParams): void
    {
        if (empty($rpcParams['fill_color']) || empty($rpcParams['border_color'])) {
            throw new Exception('Either the fill_color and border_color parameter must have a non-null value');
        }

        // Update layer style
        $layerId = Config::LAYER_TYPE_KVS == $layer->layer_type ? $layer->id . '_' . $rpcParams['ekatte'] : $layer->id;
        $style = $this->updateLayerStyleProperties($layerId, $rpcParams);

        // Update colors and label in the user's attribute database or view
        $labelSql = $style->generateMapLabelSQL($layer);
        if (Config::LAYER_TYPE_CSD == $layer->layer_type) {
            $modifiedString = str_replace('layer_decl_69_70_', '', $layer->table_name);
            [$ekatte, $farmYear] = explode('_', $modifiedString);

            $rpcParams['fill_color'] = $rpcParams['border_only'] ? null : $rpcParams['fill_color'];
            $rpcParams['label'] = $labelSql;
            $this->UserDbController->createCsdMatView($layer->table_name, $ekatte, $farmYear, $rpcParams);

            return;
        }

        $options = [
            'tablename' => $layer->table_name,
            'mainData' => [
                'fill_color' => $rpcParams['border_only'] ? null : $rpcParams['fill_color'],
                'border_color' => $rpcParams['border_color'],
                'label' => $labelSql,
            ],
            'columnFields' => [$labelSql],
        ];

        if (Config::LAYER_TYPE_KVS == $layer->layer_type) {
            $ekatteDef = $layer->getDefinitionByCategory(Config::LAYER_COLUMN_CATEGORY_EKATTE);
            $options['where'] = [
                $ekatteDef['col_name'] => $rpcParams['ekatte'],
            ];
        }

        $this->UserDbController->editItem($options);
    }

    private function updateLayerStyleByAttribute(UserLayers $layer, array $rpcParams)
    {
        if (empty($rpcParams['fill_column_name']) && empty($rpcParams['border_column_name'])) {
            throw new Exception('Either the fill_column_name or border_column_name parameter must have a non-null value');
        }

        $definitions = $layer->getDefinitions();
        [$fillColumnDefinition] = UserLayers::filterDefinitions($definitions, [['col_name' => $rpcParams['fill_column_name']]]);
        [$borderColumnDefinition] = UserLayers::filterDefinitions($definitions, [['col_name' => $rpcParams['border_column_name']]]);

        // FE can send reference columns for fill or border. These columns may not be visible and we need to escape them.
        $referenceColumn = array_filter($definitions, function ($columnDefinition) {
            return $columnDefinition['col_reference'];
        });
        $referenceColumn = array_column($referenceColumn, 'col_reference');

        $isNotValidFieldColumn = (!empty($fillColumnDefinition) && !$fillColumnDefinition['col_visible']) && !in_array($fillColumnDefinition['col_name'], $referenceColumn);
        $isNotValidBorderColumn = (!empty($borderColumnDefinition) && !$borderColumnDefinition['col_visible']) && !in_array($borderColumnDefinition['col_name'], $referenceColumn);
        if ($isNotValidFieldColumn || $isNotValidBorderColumn) {
            throw new Exception('One of the selected columns fill_column_name or border_column_name does not meet the colouring requirements. The column must be visible');
        }

        if ($fillColumnDefinition['col_virtual'] || $borderColumnDefinition['col_virtual']) {
            throw new Exception('One of the selected columns fill_column_name or border_column_name does not meet the colouring requirements. The column should not be virtual');
        }

        $layerId = Config::LAYER_TYPE_KVS == $layer->layer_type ? $layer->id . '_' . $rpcParams['ekatte'] : $layer->id;

        // Update layer style
        $style = $this->updateLayerStyleProperties($layerId, $rpcParams);

        // Update colors and label in the user's database or view
        $labelSql = $style->generateMapLabelSQL($layer);
        if (Config::LAYER_TYPE_CSD == $layer->layer_type) {
            $modifiedString = str_replace('layer_decl_69_70_', '', $layer->table_name);
            [$ekatte, $farmYear] = explode('_', $modifiedString);
            $coloringOptions = [
                'fill_column_definition' => $fillColumnDefinition,
                'border_column_definition' => $borderColumnDefinition,
                'label' => $labelSql,
                'style_layers_id' => $style->id,
            ];

            $this->UserDbController->createCsdMatView($layer->table_name, $ekatte, $farmYear, $coloringOptions, LayerStyles::BY_ATTRIBUTE_COLORING_TYPE);

            return;
        }

        $this->updateUserAttributeDatabaseColorsAndLabel($layer, $style->id, $rpcParams, $fillColumnDefinition, $borderColumnDefinition, $labelSql);
    }

    private function updateLayerStyleProperties(string $layerId, array $rpcParams): LayerStyles
    {
        $style = LayerStyles::finder()->find('layer_id = :layer_id', [':layer_id' => $layerId]);
        $style->type = $rpcParams['type'];
        $style->transparency = (int)$rpcParams['transparency'];

        if (LayerStyles::SINGLE_COLORING_TYPE === $rpcParams['type']) {
            $style->fill_color = $rpcParams['fill_color'] ?? $style->fill_color;
            $style->border_color = $rpcParams['border_color'] ?? $style->border_color;
        }

        if (LayerStyles::BY_ATTRIBUTE_COLORING_TYPE === $rpcParams['type']) {
            $style->fill_column_name = $rpcParams['fill_column_name'] ?? null;
            $style->border_column_name = $rpcParams['border_column_name'] ?? null;
        }

        $style->border_width = (int)$rpcParams['border_width'];
        $style->border_only = LayerStyles::BY_ATTRIBUTE_COLORING_TYPE === $rpcParams['type'] ? false : (bool)$rpcParams['border_only'];
        $style->labels = $rpcParams['labels'] ?? [];
        $style->label_size = (int)$rpcParams['label_size'];
        $style->tags = (bool)$rpcParams['tags'];
        $style->save();

        return $style;
    }

    private function updateUserAttributeDatabaseColorsAndLabel(UserLayers $layer, int $styleLayersId, array $rpcParams, ?array $fillColumnDefinition, ?array $borderColumnDefinition, string $labelSql): void
    {
        $defaultColorCategories = DefaultColors::getCategories();

        $fillColumnName = $fillColumnDefinition['col_name'];
        $fillOptions = [
            'tablename' => $layer->table_name,
            'mainData' => [
                'fill_color' => isset($rpcParams['fill_column_name']) ? "generate_color_from_value(\"{$fillColumnName}\")" : null,
                'label' => $labelSql,
            ],
            'columnFields' => [$labelSql, "generate_color_from_value(\"{$fillColumnName}\")"],
            'col_category' => $fillColumnDefinition['col_category'],
        ];

        if (in_array($fillColumnDefinition['col_category'], $defaultColorCategories)) {
            $fillOptions = $this->prepareOptionsForDefaultColors($layer, $fillOptions, $fillColumnName, 'fill_color');
        }

        $borderColumnName = $borderColumnDefinition['col_name'];
        $borderOptions = [
            'tablename' => $layer->table_name,
            'mainData' => [
                'border_color' => isset($rpcParams['border_column_name']) ? "generate_color_from_value(\"{$borderColumnName}\")" : Config::LAYER_BOUNDARY_DEFAULT_COLOR,
            ],
            'columnFields' => ["generate_color_from_value(\"{$borderColumnName}\")"],
            'col_category' => $borderColumnDefinition['col_category'],
        ];

        if (in_array($borderColumnDefinition['col_category'], $defaultColorCategories)) {
            $borderOptions = $this->prepareOptionsForDefaultColors($layer, $borderOptions, $borderColumnName, 'border_color');
        }

        if (Config::LAYER_TYPE_KVS == $layer->layer_type) {
            $ekatteDef = $layer->getDefinitionByCategory(Config::LAYER_COLUMN_CATEGORY_EKATTE);
            $fillOptions['where'][$ekatteDef['col_name']] = $rpcParams['ekatte'];
            $borderOptions['where'][$ekatteDef['col_name']] = $rpcParams['ekatte'];
        }

        $this->UserDbController->editItem($fillOptions);
        $this->UserDbController->editItem($borderOptions);
    }

    private function prepareOptionsForDefaultColors(UserLayers $layer, array $options, string $coloringColumnName, string $colorColumnName)
    {
        $dbLink = 'host=' . DBLINK_HOST . ' port=' . DBLINK_PORT . ' dbname=' . DBLINK_DATABASE . ' user=' . DBLINK_USERNAME . ' password=' . DBLINK_PASSWORD;

        $options['mainData'][$colorColumnName] = 'su_def_colors.color';
        $options['from'] = "dblink(
            '{$dbLink}',
            'SELECT category, value, color FROM su_default_colors'
        ) AS su_def_colors(category TEXT, value TEXT, color TEXT)";
        $options['where'] = [
            "({$layer->table_name}.\"{$coloringColumnName}\"::varchar" => 'su_def_colors.value::varchar OR su_def_colors.value ISNULL)',
            'su_def_colors.category' => "'{$options['col_category']}'",
        ];
        $options['columnFields'] = [$options['mainData']['label'], 'su_def_colors.color',  "({$layer->table_name}.\"{$coloringColumnName}\"::varchar", 'su_def_colors.category'];

        return $options;
    }

    private function deleteGeneratedImagesForEkatte(string $ekatte, IUser $user)
    {
        $path = WMS_IMAGE_PATH . $user->GroupID . '/';
        if (!file_exists($path)) {
            mkdir($path, 0777);
        }
        if ($handle = opendir($path)) {
            while (($file = readdir($handle)) !== false) {
                if (preg_match('/^(' . $ekatte . '_\d+_\d+)\.png$/i', $file)) {
                    @unlink($path . $file);
                }
            }
        }
    }
}
