/*jslint browser: true*/
/*global jQuery, EasyUIRPCLoaders.EasyUIGridCustomLoader, onComboMultiSelect, dateboxWithClearButton, onHidePanelMultiSelect, TF */

var contragent_type,
    rentaResult,
    CONTRACT_TYPE_OWN = 1,
    CONTRACT_TYPE_SALES = 6,
    CONTRACT_TYPE_LEASE = 2,
    CONTRACT_TYPE_RENT = 3,
    CONTRACT_TYPE_COOPERATIVE_USAGE = 5,
    originalNumberspinnerField,
    ComboboxData,
    egnBox,
    cardIdBox;

var IsCopy = false;
var businessStartDate;
var businessEndDate;

jQuery(function () {
    initSearchOnEnter();

    TF.Rpc.Common.CombinedComboboxData.read(null, null, {selected: 'current'})
        .done(function (data) {
            ComboboxData = data;
            initSearchComboboxFields();
            initAddEditPanelValidators();
            initMultiEditPanelValidators();
            initAddAnnexFields();
        })
        .fail(function (error) {
            jQuery.messager.alert('Грешка', error.getMessage(), 'warning');
        });

    setUserRights();

    //retrieve GET parameters
    var GET = {filterUserFarmings: true};
    var gridTableID;
    var date = new Date();
    todayDate = date.getFullYear() + '-' + (date.getMonth() + 1) + '-' + date.getDate();
    location.search.substr(1).split("&").forEach(function (item) {
        GET[decodeURIComponent(item.split("=")[0])] = decodeURIComponent(item.split("=")[1]);
    });

    //change URL without refresh if it's possible
    if (history && history.replaceState) {
        history.replaceState(null, null, 'index.php?page=' + GET.page);
    }

    initContractsTree(1, GET);
    initFilesGrid(0);
    initContractsPlotsGrid(0);
    initSearchFieldsComplete();

    jQuery('#eik > input').numberbox({});

    egnBox = new EgnValidateBox('#egn > input');
    cardIdBox = new CardIdValidateBox('#lk_nomer > input');

    jQuery('#add-filter-btn > a').linkbutton({iconCls: 'icon-save'});
    jQuery('#save-owner-button > a').linkbutton({iconCls: 'icon-save'});
    jQuery('#btn-add-payment > a').linkbutton({iconCls: 'icon-save'});
    jQuery('#pp-filter-btn > a').linkbutton({iconCls: 'icon-filter'});
    jQuery('#save-sublease-button > a').linkbutton({iconCls: 'icon-save'});

    jQuery('#search-date-from  > input').datebox();
    jQuery('#search-date-to  > input').datebox();
    jQuery('#search-due-date-from  > input').datebox();
    jQuery('#search-due-date-to  > input').datebox();

    jQuery('#btn-filter-contracts-tree').bind('click', function () {
        initContractsTreeFilter();
    });

    //sorting
    jQuery('#btn-sort-contracts-tree').bind('click', function () {
        initContractsTreeFilter();
        jQuery('#win-sort-contracts').window('close');
    });

    jQuery('#btn-close-sort-contracts-tree').bind('click', function () {
        clearSortContractsTreeFields();
        initContractsTreeFilter();
        jQuery('#win-sort-contracts').window('close');
    });

    jQuery('#btn-clear-filter-contracts').bind('click', function () {
        return clearContractsFilter(initContractsTree);
    });

    jQuery('#btn-files').bind('click', function () {
        if (!hasPlotRightsRW) {
            messagerPlotsWriteRights();
            return false;
        }
        var getChecked = jQuery('#contracts-tables').datagrid('getChecked');
        initFilesGrid(getChecked[0]['id']);
        jQuery('#win-files').window('open');
        return false;
    });

    jQuery('#btn-contracts-groups').bind('click', function () {
        initContractsGroups();
        jQuery('#win-contracts-groups').window('open');
        return false;
    });

    jQuery('#btn-add-contract').bind('click', function () {
        if (!hasPlotRightsRW) {
            messagerPlotsWriteRights();
            return false;
        }
        editContractID = undefined;
        copyContractID = undefined;
        jQuery('#win-add-edit-contracts').window('open');
        clearAddEditPanelFields();
        displayAddEditPanelFields(1);
        addEditRentaField();
        return false;
    });

    jQuery('#btn-edit-contract').bind('click', function () {
        if (!hasPlotRightsRW) {
            messagerPlotsWriteRights();
            return false;
        }
        var getSelected = jQuery('#contracts-tree').tree('getSelected');

        if (!getSelected)
        {
            jQuery.messager.alert('Не е избран договор!');
            return false;
        }
        
        //No Rights to operate with "Договори за собственост"
        if(getSelected.attributes.c_type == CONTRACT_TYPE_OWN && !hasContractsOwnWriteRights) {
            messagerContractsOwnWriteRights();
            return false;
        }

        if (getSelected.attributes.from_sublease > 0) {
            messageContractIsFromSublease(getSelected);
            return false;
        }

        var farmingComboboxData = [];
        ComboboxData.FarmingCombobox.forEach(function (el) {
            if (el.id !== '') {
                farmingComboboxData.push(el);
            }
        });

        farmingComboboxData[0].selected = true;
        jQuery('#contract-farming > input').combobox({
            data: farmingComboboxData,
            disabled: getSelected.attributes.is_annex ? true : false,
            valueField: 'id',
            textField: 'name',
            loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
            loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
        });

        editContractID = getSelected.id;
        copyContractID = undefined;
        TF.Rpc.Contracts.ContractsTree.markForEdit(getSelected.id)
        .done(function (data) {
            if(getSelected.attributes.hasPayment){
                jQuery.messager.confirm('Потвърждение', 'Направените промени могат да бъдат отразени в справката за изплатени ренти!', function (r) {
                    if (r) {
                        jQuery('#win-add-edit-contracts').window('open');
                    }
                });
            } else {
                jQuery('#win-add-edit-contracts').window('open');
            }
            if (data.additionalRentas) {
                rentaResult = jQuery.map(data.additionalRentas, function (value, index) {
                    return [value];
                });
            } else {
                rentaResult = [];
            }
            clearAddEditPanelFields();
            setAddEditCopyPanelData(data);

        })
        .fail(function (errorObj) {
            if (errorObj.is(TF.Rpc.ExceptionsList.INVALID_USER_CONTRACTS_RIGHTS_OWN_CONTRACTS)) {
                messagerContractsOwnWriteRights();
                return false;
            }
            if (errorObj.is(TF.Rpc.ExceptionsList.NO_CONTRACT_PERMISSION)) {
                jQuery.messager.alert('Грешка', errorObj.getMessage(), 'warning');          
                return false;
            }
        });
    });

    jQuery('#btn-multiedit-contract').bind('click', function () {
        if (!hasPlotRightsRW) {
            messagerPlotsWriteRights();
            return false;
        }
        var hasPayment = false;
        var contracts = jQuery('#contracts-tree').tree('getRoots'),
            obj = getFilterContractsTreeParams();
        for (i = 0; i < contracts.length; i++) {
            if(contracts[i].attributes.hasPayment){
                hasPayment = true;
            }
        }
        if(hasPayment){
            jQuery.messager.confirm('Потвърждение', 'Направените промени могат да бъдат отразени в справката за изплатени ренти!', function (r) {
                if (r) {
                    jQuery('#win-multiedit-contracts').window('open');
                    initMultiEditContracts();
                    clearMultiEditPanelFields();
                }
            });
        }else{
            jQuery('#win-multiedit-contracts').window('open');
            initMultiEditContracts();
            clearMultiEditPanelFields();
        }

        return false;
    });

    jQuery('#btn-delete-contract').bind('click', function () {
        if (!hasPlotRightsRW) {
            messagerPlotsWriteRights();
            return false;
        }
        var getSelected = jQuery('#contracts-tree').tree('getSelected');
        if (getSelected) {

            //No Rights to operate with "Договори за собственост"
            if(getSelected.attributes.c_type == CONTRACT_TYPE_OWN && !hasContractsOwnWriteRights) {
                messagerContractsOwnWriteRights();
                return false;
            }

            if (getSelected.attributes.from_sublease > 0) {
                messageContractIsFromSublease(getSelected);
                return false;
            }

            var subleasesExistMsg = generateSubleasesExistMsg();

            if (subleasesExistMsg !== '') {
                jQuery.messager.alert('Грешка', subleasesExistMsg, 'warning');
            } else {
                jQuery.messager.confirm('Потвърждение', 'Сигурни ли сте, че искате да премахнете този запис?', function (r) {
                    if (r) {
                        editContractID = undefined;
                        var params = {
                            contract_id: getSelected.id,
                            confirm: false,
                        };
                        deleteContract(params);
                    }
                });
            }
        } else {
            jQuery.messager.alert('Грешка', 'Моля изберете договори, който да бъдат премахнати.');
        }
        return false;
    });

    function deleteContract(params){
        TF.Rpc.Contracts.ContractsTree.deleteContracts(params)
            .done(function (data) {
                onCompleteActionContractsRights(data);
            })
            .fail(function (data) {
                if (data.getCode() == -33754) {
                    jQuery.messager.alert('Грешка', data.getOriginalMessage(),'warning');
                } 
                else if (data.is(TF.Rpc.ExceptionsList.CONTRACTS_HAS_PAYMENTS_EXCEPTION)) {        
                    jQuery('#win-has-payments-error').window('open');  
                    initUnsuccessfullyEditContract(
                        data.getOriginalMessage()
                    );
                }
                else if (data.is(TF.Rpc.ExceptionsList.CONTRACT_TYPE_MODIFY_DENIED_SALES_RELATION)) {
                    jQuery.messager.alert('Грешка', generateSalesContractExistMsg(data.getOriginalMessage()),'error');
                } 
                else {
                    jQuery.messager.alert('Грешка','Възникна грешка при обработка на данните.');
                }
            })
    }

    jQuery('#btn-filter-contracts').bind('click', function () {
        jQuery('#win-filter-contracts').window('resize', {
            height: getZoomedWindowHeight(720, 1.09),
            width: 850
        });

        jQuery('#win-filter-contracts').window('open');
        return false;
    });

    jQuery('#btn-sort-contracts').bind('click', function () {
        jQuery('#win-sort-contracts').window('open');
        return false;
    });

    jQuery('#btn-change-contract-status').bind('click', function (e) {
        e.preventDefault();
        if (!hasPlotRightsRW) {
            messagerPlotsWriteRights();
            return false;
        }
        var getSelected = jQuery('#contracts-tree').tree('getSelected');
        var comment = '';
        var alert_text = '';
        var obj = {};

        if (!getSelected) {
            jQuery.messager.alert('Грешка', 'Моля изберете договор, който искате да анулирате!');
            return false;
        }

        //No Rights to operate with "Договори за собственост"
        if(getSelected.attributes.c_type == CONTRACT_TYPE_OWN && !hasContractsOwnWriteRights) {
            messagerContractsOwnWriteRights();
            return false;
        }

        if (getSelected.attributes.from_sublease > 0) {
            messageContractIsFromSublease(getSelected);
            return false;
        }

        if (getSelected.attributes.comment != '-') {
            comment = getSelected.attributes.comment;
        }

        let contract_has_subleased_plots = false;
        if (getSelected.attributes.active == 0) {
            alert_text = 'Сигурни ли сте, че искате да маркирате този договор като действащ?';
            obj.status = true;
        } else {
            alert_text = generateSubleasesExistMsg();
            if (alert_text !== '') {
                contract_has_subleased_plots = true;
                alert_text += '<br>Нужно е първо имотите да се премахнат от договора за преотдаване.<br><br>';
            }       
            obj.status = false;
        }

        alert_text += '<table width="100%" cellspacing="0" cellpadding="0" style="margin-top:10px;">';
        alert_text += '<tr><td style="padding-left: 10px;"><b>Забележка</b></td></tr>';
        alert_text += '<tr><td style="padding-left: 10px;">';
        alert_text += '<textarea id="contract-status-comment" style="resize: none; width:190px; height:60px;"></textarea>';
        alert_text += '</td></tr>';
        alert_text += '</table>';

        if (contract_has_subleased_plots === true) {
            jQuery.messager.alert({
                title: 'Потвърждение',
                msg: alert_text
            });
        } else {
            jQuery.messager.confirm('Потвърждение', alert_text, function (r) {
                if (r) {
                    editContractID = getSelected.id;
                    obj.id = getSelected.id;
                    obj.comment = tinyMCE.get('contract-status-comment').getContent();
                    TF.Rpc.Contracts.ContractsTree.changeActiveStatus(obj)

                    .done(function (data) {
                        onCompleteActionContractsRights(data);
                        editContractID = undefined;
                    })
                    .fail(function (errorObj) {
                        if (errorObj.is(TF.Rpc.ExceptionsList.CONTRACTS_AVAILABLE_PLOT_AREA_EXCEPTION)) {
                            const contracts_info = errorObj.getOriginalMessage().flatMap(item => JSON.parse(item.contracts_info));

                            jQuery('#win-contracts-available-area-error').window('open');  
                            initContractsAvailablePlotAreaErrorModal(
                                contracts_info
                            );
                            return false;
                        } 
                        else if (errorObj.is(TF.Rpc.ExceptionsList.CONTRACTS_HAS_PAYMENTS_EXCEPTION)) {        
                            jQuery('#win-has-payments-error').window('open');  
                            initUnsuccessfullyEditContract(
                                errorObj.getOriginalMessage()
                            );
                        }
                        else if (errorObj.is(TF.Rpc.ExceptionsList.CONTRACT_PLOT_DUPLICATION)) {
                            jQuery('#win-copy-filtered-contracts-plots-error').window('open');  
                            initUnsuccessfullyCopiedContracts(
                                errorObj.getOriginalMessage()
                            );
                            return false;  
                        } else if (errorObj.is(TF.Rpc.ExceptionsList.CONTRACT_TYPE_MODIFY_DENIED_SALES_RELATION)) {
                            jQuery.messager.alert('Грешка', generateSalesContractExistMsg(errorObj.getOriginalMessage()),'error');
                        } else {
                            jQuery.messager.alert('Грешка', errorObj.getMessage(), 'warning');  
                        }
                    });
                }

            });
        }
        if (tinymce.EditorManager.editors['contract-status-comment'] !== undefined) {
            tinymce.EditorManager.editors['contract-status-comment'].destroy();
        }
        tinymce.init({
            selector: '#contract-status-comment',
            theme: "modern",
            language: 'bg_BG',
            invalid_elements: "script",
            relative_urls: false,
            statusbar: false,
            toolbar: "forecolor",
            menubar: false,
            style_formats_merge: true,
            forced_root_block: false,
            plugins: "textcolor colorpicker"
        }).then(function (editor) {
            tinymce.EditorManager.editors['contract-status-comment'].getBody().innerHTML = jQuery('#contracts-tree').tree('getSelected').attributes.comment;
        });
    });

    function generateSubleasesExistMsg() {
        var subleases = getSubleasesFromContract();
        var subleasesMsg = '';

        if(Object.keys(subleases).length > 0) {
            subleasesMsg = 'Договора не може да бъде изтрит/анулиран, защото съдържа имот(и), които са преотдадени чрез:<br><br>';
            for (const id in subleases){
                subleasesMsg += '<a href="index.php?page=Subleases.Home&sublease_id=' + subleases[id].id + '" target="_blank">' + subleases[id].c_num + '</a><br>';
            }
        }

        return subleasesMsg;
    }

    function getSubleasesFromContract(){
        var plots = jQuery('#contract-plots-tables').datagrid('getRows');
        var subleases = {};
        for (const plot of plots) {
            for (const sublease of plot.subleases)
            if(sublease.active && ! sublease.is_expired) {
                if(!subleases.hasOwnProperty(sublease.id)) {
                    subleases[sublease.id] = sublease;
                }
            }
        }

        return subleases;
    }

    jQuery('#btn-add-annex').bind('click', function () {
        if (!hasPlotRightsRW) {
            messagerPlotsWriteRights();
            return false;
        }
        var getSelected = jQuery('#contracts-tree').tree('getSelected');
        var contractPlots = jQuery('#contract-plots-tables').datagrid('getData');
        if (contractPlots.total == 0) {
            jQuery.messager.alert('Грешка', 'Не може да анексирате договор, в който не са включени имоти!');
            return false;
        }

        if (getSelected) {
            var contractData = getSelected.attributes;

            if (contractData.is_annex == true) {
                jQuery.messager.alert('Грешка', 'Не може да анексирате анекс');
                return false;
            }

            if (getSelected.attributes.from_sublease > 0) {
                jQuery.messager.alert('Грешка', 'Не може да анексирате избраният договор. Той е създаден автоматично от подмодул "Преотдадени".', 'warning');
                return false;
            }

            if (contractData.c_type != 1 && contractData.c_type != 4)
            {
                if (contractData.active_text == 'Действащ')
                {
                    jQuery('#win-add-edit-annex').window('open');
                    initContractsAnnexesPlotsGrid();
                    toggleOverallRentaField();
                    clearContractsAnnexesFields();
                } else {
                    jQuery.messager.confirm('Потвърждение', 'Договорът е анулиран или изтекъл. Сигурни ли сте, че искате да продължите?', function (r) {
                        if (r) {
                            jQuery('#win-add-edit-annex').window('open');
                            initContractsAnnexesPlotsGrid();
                            toggleOverallRentaField();
                            clearContractsAnnexesFields();
                        }
                    });
                }
            } else {
                jQuery.messager.alert('Грешка', 'Към договори от тип собственост или споразумение не могат да бъдат добавяни анекси!');
            }
        } else {
            jQuery.messager.alert('Грешка', 'Моля изберете договор, за който да добавите анекс!');
        }
        return false;
    });

    jQuery('#btn-print-contract').bind('click', function () {
        var getSelected = jQuery('#contracts-tree').tree('getSelected');
        if (getSelected && getSelected.id || getSelected.folder_id) {
            if (getSelected.folder_id)
            {
                contracts_templates.initPrintTemplatesGrid(null,getSelected.folder_id,getSelected.parrent_id,getSelected.attributes);
                jQuery('#win-contracts-templates').window('open');
            }
           else{
                if (getSelected.attributes.c_type != 1 && getSelected.attributes.c_type != 4)
                {
                    contracts_templates.initPrintTemplatesGrid(getSelected.id,null);
                    jQuery('#win-contracts-templates').window('open');
                } else {
                    jQuery.messager.alert('Грешка', 'Договори от тип собственост или споразумение не могат да бъдат разпечатвани!');
                }
            }
        } else {

            jQuery.messager.alert('Грешка', 'Моля изберете договор или папка!');
        }
        return false;
    });

    jQuery('#btn-print-filtered-annexes').bind('click', function () {
        printFilteredContracts(true);
    });
    

    jQuery('#btn-print-filtered-contracts').bind('click', function () {
        printFilteredContracts(false);
    });

    function printFilteredContracts(annexes = false) {
        var contracts = jQuery('#contracts-tree').tree('getRoots');
        let contractsToPrint = [];

        if (annexes == true) {
            for(let contract of contracts) {
                let children = jQuery('#contracts-tree').tree('getChildren', contract.target);
                if (children.length) {
                    for(let child of children) {
                        contractsToPrint.push(child);
                    }
                }
            }
        } else {
            contractsToPrint = contracts;
        }
       
        var allowableContractTypes = [2,3,5];
        var contractsData = [];
        let page = jQuery('#contracts-tree').tree('options')['page'];
        if(contractsToPrint.length === 0) {
            jQuery.messager.alert('Грешка', 'Нама намерени договори за принтиране');
            return false;
        }

        let filteredContractType = parseInt(contracts[0].attributes.c_type);

        for(let contract of contractsToPrint) {
            const c_type = parseInt(contract.attributes.c_type);

            if(!allowableContractTypes.includes(c_type) || filteredContractType !== c_type){
                jQuery.messager.alert('Грешка', 'Филтрираните договори не са само от един тип - „Наем“, „Аренда“ или „Съвместна обработка“. Моля филтрирайте договори само от един тип и стартирайте инструмента отново!');
                return false;
            }
            contractsData.push({
                'id': contract.id,
                'c_num': contract.attributes.c_num,
            });
        }

        contracts_templates.initPrintTemplatesGrid(null,contractsData, null, null, page);
        jQuery('#win-contracts-templates').window('open');
    }

    jQuery('#btn-export-excel-sort-contracts-to-xls').bind('click', function () {
        printFilteredContractsToXLS();
    });

    function printFilteredContractsToXLS() {
        var winDownload = jQuery('#win-download').window({
            onClose: onDownloadWindowClose
        });
        var downloadFile = jQuery('#btn-download-file');
        var filterObj = getFilterContractsTreeParams();

        TF.Rpc.Contracts.ContractsTree.exportFilteredContractsToXLS(filterObj)
            .done(function (data) {
                winDownload.window('open');
                _pathFile = data.file_path;
                _fileName = data.file_name;
                downloadFile.attr("href", _pathFile);
            })
            .fail(function (errorObj){
                return jQuery.messager.alert('Грешка', errorObj.getOriginalMessage(), 'warning');
            });
    }

    function onDownloadWindowClose()
    {
        return;
    }

    jQuery('#btn-copy-contract').bind('click', function () {
        if (!hasPlotRightsRW) {
            messagerPlotsWriteRights();
            return false;
        }
        var selected = jQuery('#contracts-tree').tree('getSelected');

        if (!selected) {
            jQuery.messager.alert('Грешка', 'Не е избран договор!');
            return false;
        }

        if(selected.attributes.c_type == CONTRACT_TYPE_OWN){
            return jQuery.messager.alert('Грешка', 'Договори от тип Собственост не могат да бъдат копирани!');
        }

        if(selected.attributes.is_annex == true){
            return jQuery.messager.alert('Грешка', 'Анекси не могат да бъдат копирани!');
        }

        if (selected.attributes.active == false) {
            return jQuery.messager.alert('Грешка', 'Не може да копирате анулиран договор!');
        }

        //No Rights to operate with "Договори за собственост"
        if(selected.attributes.c_type == CONTRACT_TYPE_OWN && !hasContractsOwnWriteRights) {
            messagerContractsOwnWriteRights();
            return false;
        }

        if (selected.attributes.from_sublease > 0) {
            messageContractIsFromSublease(selected);
            return false;
        }        

        copyContractID = selected.id;

        if (selected.children) {
            const contractDueDate = new Date(selected.attributes.due_date.split('.').reverse().join('-'));
            
            var biggestAnnexDueDate = null;
            selected.children.forEach(child => {
                if (child.attributes.active) {
                    const childDueDate = new Date(child.attributes.due_date.split('.').reverse().join('-'));

                    //copy contract with biggest id and due date
                    if (childDueDate >= contractDueDate && childDueDate > biggestAnnexDueDate) {
                        biggestAnnexDueDate = childDueDate;
                        //copyContractID is used in save contract
                        copyContractID = child.id;
                    }
                }
            });
        }

        editContractID = undefined;

        TF.Rpc.Contracts.ContractsTree.hasContractEditedPlots(copyContractID)
        .done(function (data) {
            if(data == true) {
                jQuery.messager.confirm('Потвърждение', 'В избраният договор са включени исторически имоти, ' +
                    'които не са част от актуалната КВС/КК. Желаете ли да продължите?', function (r) {
                    if (r) {
                        TF.Rpc.Contracts.ContractsTree.contractCopy(selected.id)
                        .done(function (data) {
                            if (data.additionalRentas) {
                                rentaResult = jQuery.map(data.additionalRentas, function (value, index) {
                                    return [value];
                                });
                            } else {
                                rentaResult = [];
                            }
                            clearAddEditPanelFields();
                            setAddEditCopyPanelData(data, preventDatesDisable= true);
                            jQuery('#win-add-edit-contracts').window('open');
                        })
                        .fail(function (errorObj) {
                            if (errorObj.is(TF.Rpc.ExceptionsList.INVALID_USER_CONTRACTS_RIGHTS_OWN_CONTRACTS)) {
                                messagerContractsOwnWriteRights();
                            }
                        });
                    }
                });
            }
            else{
                TF.Rpc.Contracts.ContractsTree.contractCopy(selected.id)
                .done(function (data) {
                    if (data.additionalRentas) {
                        rentaResult = jQuery.map(data.additionalRentas, function (value, index) {
                            return [value];
                        });
                    } else {
                        rentaResult = [];
                    }
                    clearAddEditPanelFields();
                    setAddEditCopyPanelData(data, preventDatesDisable = true);
                    jQuery('#win-add-edit-contracts').window('open');
                })
                .fail(function (errorObj) {
                    if (errorObj.is(TF.Rpc.ExceptionsList.INVALID_USER_CONTRACTS_RIGHTS_OWN_CONTRACTS)) {
                        messagerContractsOwnWriteRights();
                    } else {
                        jQuery.messager.alert('Грешка', errorObj.getMessage(), 'warning');
                    }
                });
            }
        })
        .fail(function (errorObj) {
            console.error(errorObj);
        });
    });

    if (tinymce.EditorManager.editors['contract-comment-textarea'] == undefined) {
        tinymce.init({
            selector: '#contract-comment-textarea',
            theme: "modern",
            language: 'bg_BG',
            invalid_elements: "script",
            relative_urls: false,
            statusbar: false,
            toolbar: "forecolor",
            menubar: false,
            style_formats_merge: true,
            forced_root_block: false,
            plugins: "textcolor colorpicker"
        }).then(function (editor) {
            jQuery('#contract-comment-textarea_ifr').width('99.9%');
        });
    }

    jQuery('#win-add-edit-contracts').window({
        onClose: function () {
            if (tinymce.EditorManager.editors['contract-comment-textarea'] != undefined) {
                tinyMCE.get('contract-comment-textarea').setContent('');
            }
        }
    });

    if (tinymce.EditorManager.editors['edit-plot-comment-contracts'] === undefined) {
        tinymce.init({
            selector: '#edit-plot-comment-contracts',
            theme: "modern",
            language: 'bg_BG',
            invalid_elements: "script",
            relative_urls: false,
            statusbar: false,
            toolbar: "forecolor",
            menubar: false,
            style_formats_merge: true,
            forced_root_block: false,
            plugins: "textcolor colorpicker"
        })
    }

    jQuery('#win-edit-contract-plot-data').window({
        onClose: function () {
            if (tinymce.EditorManager.editors['edit-plot-comment-contracts'] != undefined) {
                tinyMCE.get('edit-plot-comment-contracts').setContent('');
            }
        }
    });
    //from contracts-plots-grid
    initAddPlotOwnershipContractChecks();
    onClickSavePlotOwnCont();
});

jQuery(function () {
    jQuery('#btn-copy-filtered-contracts').bind('click', function () {

        var contracts = jQuery('#contracts-tree').tree('getRoots');
        obj = getFilterContractsTreeParams();

        if (contracts.length === 0) {
            jQuery.messager.alert('Грешка', TF.Rpc.ExceptionsList.NO_CONTRACTS_FOR_MULTICOPY.message, 'error');
            return;
        }

        jQuery("#copy-filtered-with-files-checkbox").prop('checked', false);
        jQuery('#win-copy-filtered-contracts').window('open');

    });
});

function confirmCopyFilteredContracts() {
    TF.Rpc.Contracts.ContractsTree.getAllFilteredContracts(obj)
        .done(function (contracts) {
            if (contracts.length === 0) {
                jQuery.messager.alert('Грешка', TF.Rpc.ExceptionsList.NO_CONTRACTS_FOR_MULTICOPY.message, 'error');
                return false;
            }
            var contractIDs = {
                contract_ids: []
            };
            contracts.forEach(function (contract) {
                contractIDs.contract_ids.push(contract.id);
            });

            TF.Rpc.Contracts.ContractsTree.checkForLongerThanYear(contractIDs)
                .done(function (data) {
                    if (data) {
                        jQuery.messager.confirm('Потвърждение', 'Сред филтрираните договори има договори с период на действие повече от една стопанска година. Искате ли да продължите?', function (re) {
                            if (re) {
                                multiCopyContracts();
                            }
                        });
                    } else {
                        multiCopyContracts();
                    }
                })
                .fail(function (errorObj) {
                    jQuery.messager.alert('Грешка', errorObj.getMessage(), 'error');
                });
        })
        .fail(function (errorObj) {
            if (errorObj.is(TF.Rpc.ExceptionsList.INCORRECT_CONTRACTS_TYPE)) {
                jQuery.messager.alert('Грешка', TF.Rpc.ExceptionsList.INCORRECT_CONTRACTS_TYPE.message, 'error');
            } else if (errorObj.is(TF.Rpc.ExceptionsList.NO_CONTRACTS_FOR_MULTIEDIT)) {

                jQuery.messager.alert('Грешка', TF.Rpc.ExceptionsList.NO_CONTRACTS_FOR_MULTIEDIT.message, 'error');
            }
            jQuery('#win-copy-filtered-contracts').window('close');
            jQuery('#win-multiedit-contracts').window('close');
        });
}

function printContract() {
    var getChecked = jQuery('#contracts-tables').datagrid('getChecked');

    if (selectedContractID) {
        contracts_templates.initTemplatesGrid(selectedContractID);
    } else {
        contracts_templates.initTemplatesGrid();
    }

    jQuery('#win-templates').window('open');
    return false;
}

function closeWindows() {
    jQuery('#win-set-owner-percent').window('close');
    jQuery('#win-plot-owner-add').window('close');
}

function initMultiEditPanelValidators() {
    jQuery('#win-multiedit-contracts').window('resize', {
        height: getZoomedWindowHeight(710),
        width: 500
    });

    jQuery('#multi-renta > input').numberspinner({
        required: false,
        precision:2,
        min: 0
    });

    var multiRentaNaturaSpinner = jQuery('#multi-renta-natura-0');
    jQuery('#multi-renta-type-cb-0').combobox({
        url: 'index.php?common-rpc=renta-types-combobox',
        rpcParams: [{
            as_list: true
        }],
        textField: 'name',
        valueField: 'id',
        editable: false,
        onLoadSuccess: function () {
            multiRentaNaturaSpinner.numberspinner({
                min: 0,
                precision: 3,
                disabled: true,
                required: false,
                missingMessage: 'Моля задайте количество на горепосочената рента в натура(количество за декар).',
                parser: function (value) {
                    if(value == '-') {
                        return value;
                    }
                    if(jQuery.isNumeric(value) && parseFloat(value) >= 0) {
                        var output = parseFloat(value).toFixed(3);
                        return output;
                    }
                }
            });
        },
        onSelect: function (rec) {
            if(jQuery('#multi-renta-type-cb-0').combobox('getValue') == 0) {
                try{
                    jQuery(multiRentaNaturaSpinner).numberspinner('setValue', '0');
                }
                catch(err){}
            }
            var rowArr = jQuery('.js-multi-renta-type-row');
            var rowVars = [];
            rowVars[0] = jQuery('#multi-renta-type-cb-0').combobox('getValue');

            var tmpVars = jQuery('.js-multi-renta-type-row');
            var j = 0;
            for(var rowVar in tmpVars)
            {
                if(j >= tmpVars.length) {
                    break;
                }
                j++;
                rowVars[parseInt(rowVar)+1] = jQuery('#' + tmpVars[rowVar].id).combobox('getValue');
            }
            if(rec.id != 0)
            {
                var inRes = jQuery.inArray(String(rec.id),rowVars);
                if (inRes != -1 && jQuery.inArray(String(rec.id),rowVars,inRes+1) != -1) {
                    jQuery('#renta-error-win').window('open');
                    jQuery('#multi-renta-type-cb-0').combobox('clear');
                }
            }
        },
        onChange: function (newValue, oldValue) {
            onChangeRentaTypesCombobox(this, newValue);
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

function initAddEditPanelValidators() {

    jQuery('#contract-number > input').validatebox({
        required: true,
        missingMessage: 'Моля въведете номер на договор.'
    });

    var date                         = new Date();
    var todayDate                    = date.getFullYear() + '-' + (date.getMonth() + 1) + '-' + date.getDate();
    var farmingComboboxData          = ComboboxData.FarmingCombobox;
    var newFarmingComboboxData       = [];
    var contractTypeComboboxData     = ComboboxData.ContractTypeCombobox;
    var newContractTypeComboboxData  = [];
    var contractAggTypesComboboxData = ComboboxData.ContractAggTypesCombobox;
    let userFarmingPermissions      = ComboboxData.UserFarmingPermissions;
    var contractGroupsCombobox = ComboboxData.ContractGroupsCombobox;


    ComboboxData.FarmingYearCombobox.forEach(function (el, index) {
        if(el.selected === true){
            businessStartDate            = ComboboxData.FarmingYearCombobox[index].start_date;
            businessEndDate              = ComboboxData.FarmingYearCombobox[index].end_date;
        }
    });

    jQuery('#contract-date > input').datebox({
        required: true,
        validType:'validDate',
        missingMessage: 'Моля изберете дата на договор.',
        value: todayDate
    });

    jQuery('#contract-start-date > input').datebox({
        required: true,
        validType:'validDate',
        missingMessage: 'Моля изберете дата на влизане в сила да договор.',
        value: businessStartDate
    });

    jQuery('#contract-due-date > input').datebox({
        required: true,
        validType:'validDate',
        missingMessage: 'Моля въведете продължителност на договор в месеци.',
        value: businessEndDate
    });
    
    let selectedOptionId = 0;
    farmingComboboxData.forEach(function (el) {
        if (el.id !== "") {
            el.selected = false;
            let hasPermission = Object.values(userFarmingPermissions).includes(el.id);
            if(!hasPermission) {   
                el.disabled = true;
            } else {
                if (selectedOptionId == 0) {
                    selectedOptionId = el.id;
                    el.selected = true;
                } 
            }

            newFarmingComboboxData.push(el);
        }
    });

    jQuery('#contract-farming > input').combobox({
        data: newFarmingComboboxData,
        valueField: 'id',
        textField: 'name',
        required: true,
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    jQuery('#sv-date > input').datebox({
        required: false,
        validType:'validDate'
    });

    jQuery('#osz-date > input').datebox({
        required: false,
        validType:'validDate'
    });

    var selection;
    var rentaSpinner = jQuery('#renta_price');
    var overallRentaSpinner = jQuery('#overall_renta_price');

    var currnentOverallRenta = 0;
    var currnentRenta = 0;

    contractTypeComboboxData.forEach(function (el) {
        if (el.id !== '') {
            newContractTypeComboboxData.push(el);
        }
    });

    newContractTypeComboboxData[0].selected = true;
    jQuery('#contract-type > input').combobox({
        data: newContractTypeComboboxData,
        valueField: 'id',
        textField: 'name',
        required: true,
        onLoadSuccess: function () {
            selection = jQuery(this).combobox('getValue');

            if (!selection) jQuery(this).combobox('select', '1');

            if(selection == 5) {
                rentaSpinner.numberspinner({
                    required: false,
                    precision:2,
                    min: 0
                });
                overallRentaSpinner.numberspinner({
                    required: false,
                    precision:2,
                    min: 0
                });
            } else {
                rentaSpinner.numberspinner({
                    required: true,
                    min: 0,
                    precision:2,
                    missingMessage: 'Моля въведете рента в лева на декар.'
                });

                overallRentaSpinner.numberspinner({
                    required: true,
                    precision:2,
                    min: 0,
                    missingMessage: 'Моля въведете обща рента в лева.'
                });
            }

            displayAddEditPanelFields(selection);
        },
        onSelect: function (record) {
            selection = record.id;

            if (selection == 5) {
                rentaSpinner.numberspinner({
                    required: false,
                    precision:2,
                    min: 0,
                    value:0
                });

                overallRentaSpinner.numberspinner({
                    required: false,
                    precision:2,
                    min: 0,
                    value:0
                });
            } else {
                if(jQuery('#use-overall-renta').is(':checked')) {
                    overallRentaSpinner.numberspinner({
                        required: true,
                        disabled: false,
                        precision:2,
                        min: 0,
                        value:0,
                        missingMessage: 'Моля въведете обща рента в лева.'
                    });
                    rentaSpinner.numberspinner({
                        required: false,
                        disabled: true,
                        precision:2,
                        min: 0,
                        value:0,
                        missingMessage: 'Моля въведете рента в лева на декар.'
                    });
                } else {
                    overallRentaSpinner.numberspinner({
                        required: false,
                        disabled: true,
                        precision:2,
                        min: 0,
                        value:0,
                        missingMessage: 'Моля въведете обща рента в лева.'
                    });
                    rentaSpinner.numberspinner({
                        required: true,
                        disabled: false,
                        precision:2,
                        min: 0,
                        value:0,
                        missingMessage: 'Моля въведете рента в лева на декар.'
                    });
                }
            }
            displayAddEditPanelFields(selection);
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    jQuery('#contract-group > input').combobox({
        data: contractGroupsCombobox,
        valueField: 'id',
        textField: 'name',
        required: false,
        onLoadSuccess: function () {
            jQuery(this).combobox('select', '');
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    var daySpinner = jQuery('#pd-day > input');
    jQuery('#pd-month > input').combobox({
        data: months,
        valueField: 'value',
        textField: 'label',
        onLoadSuccess: function () {
            daySpinner.numberspinner({
                min: 1,
                max: 31,
                precision: 0
            });
        },
        onSelect: function (rec) {
            var day = daySpinner.numberspinner('getValue');
            if(day > rec.maxDays) {
                day = rec.maxDays;
            }
            daySpinner.numberspinner({
                min: 1,
                max: rec.maxDays,
                precision: 0,
                value:day
            });
        }
    });

    contractAggTypesComboboxData[0].selected = true;
    jQuery('#contract-agg-type > input').combobox({
        data: contractAggTypesComboboxData,
        required: true,
        valueField: 'id',
        textField: 'name',
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    jQuery('#renta-error-win').window({
        width:275,
        height:150,
        modal:true,
        closed:true,
        resizable: false,
        collapsible: false,
        minimizable: false,
        maximizable: false,
        title: 'Грешка при избор на тип на рента.'
    });
}

function onCompleteActionContractsRights(data) {

    //No Rights to operate with "Договори за собственост"
    if(data.contractType == CONTRACT_TYPE_OWN && !data.hasContractsOwnWriteRights) {
        messagerContractsOwnWriteRights();
        return false;
    }

    jQuery('#contracts-tree').tree('loadRpc');
    initContractInfo();
    initFilesGrid(0);
    initContractsPlotsGrid(0);
    initContractsFarmingGrid(0);
}

function onCompleteActionContractsRightsSimple(sender, parameter) {

    //No Rights to operate with "Договори за собственост"
    if(parameter.contractType == CONTRACT_TYPE_OWN && !parameter.hasContractsOwnWriteRights) {
        messagerContractsOwnWriteRights();
        return false;
    }
}

function messagerContractsOwnWriteRights() {

    jQuery.messager.alert('Грешка', 'Нямате права да оперирате с Договори от тип:  Собственост!', 'warning');
}

function displayAddEditPanelFields(selection) {
    var contract_window = jQuery('#win-add-edit-contracts');
    var additionalCopyWindowHeight = 0;

    if (copyContractID !== undefined){
        additionalCopyWindowHeight = 30;
        jQuery('#is-contract-copy-files').show();
    } else {
        jQuery('#is-contract-copy-files').hide();
    }

    var now = new Date();
    if (now.getMonth()<6 && now.getDay() < 15){
        var start_date = '01-10-' + (new Date().getFullYear()-1);
        var end_date = '30-09-' + new Date().getFullYear();
    }else{
        var start_date = '01-10-' + new Date().getFullYear();
        var end_date = '30-09-' + (new Date().getFullYear()+1);
    }

    contract_window.window('resize', {
        height: getZoomedWindowHeight(650),
        width: 515
    });

    jQuery('#contract-group-container').hide();
    if (selection == 1) {
        /* 1 : Собственост / ownership */

        jQuery('#contract-due-date-legend').hide();
        jQuery('#contract-start-date > input').datebox('setValue', todayDate);
        jQuery('#contract-due-date').hide();

        jQuery('#add-edit-accordion').accordion('getPanel', 'Информация за рента').panel('collapse');
        var rentaPanelOptions = jQuery('#add-edit-accordion').accordion('getPanel', 'Информация за рента').panel('options');
        rentaPanelOptions.collapsible = false;
        jQuery('#add-edit-accordion').accordion('getPanel', 'Информация за рента').panel('options', rentaPanelOptions);

        jQuery('#add-edit-accordion').accordion('getPanel', 'Специфична рента').panel('collapse');
        var rentaTypesPanelOptions = jQuery('#add-edit-accordion').accordion('getPanel', 'Специфична рента').panel('options');
        rentaTypesPanelOptions.collapsible = false;
        jQuery('#add-edit-accordion').accordion('getPanel', 'Специфична рента').panel('options', rentaTypesPanelOptions);

        jQuery('#add-edit-accordion').accordion('getPanel', 'Информация от ОСЗ').panel('collapse');
        var oszPanelOptions = jQuery('#add-edit-accordion').accordion('getPanel', 'Информация от ОСЗ').panel('options');
        oszPanelOptions.collapsible = false;
        jQuery('#add-edit-accordion').accordion('getPanel', 'Информация от ОСЗ').panel('options', oszPanelOptions);

        jQuery('#box-extra-fields').show();
        jQuery('#sv-fill-text').hide();
        jQuery('#ds-fill-text').hide();
        jQuery('#box-sv-fields').show();
        jQuery('#box-ds-fields').show();
        jQuery('#agg-type').hide();
        jQuery('#is-subleased').hide();
        jQuery('#is-closed-for-editing-container').hide();
    } else if (selection == 4) {
        /* 1 : Споразумение / agreement */

        jQuery('#contract-due-date-legend').show();
        jQuery('#contract-due-date').show();
        jQuery('#box-extra-fields').show();
        jQuery('#sv-fill-text').show();
        jQuery('#ds-fill-text').show();
        jQuery('#add-edit-accordion').accordion('getPanel', 'Информация от ОСЗ').panel('collapse');
        var oszPanelOptions = jQuery('#add-edit-accordion').accordion('getPanel', 'Информация от ОСЗ').panel('options');
        oszPanelOptions.collapsible = false;
        jQuery('#add-edit-accordion').accordion('getPanel', 'Информация от ОСЗ').panel('options', oszPanelOptions);
        jQuery('#box-sv-fields').hide();
        jQuery('#box-ds-fields').hide();

        jQuery('#add-edit-accordion').accordion('getPanel', 'Информация за рента').panel('collapse');
        var rentaPanelOptions = jQuery('#add-edit-accordion').accordion('getPanel', 'Информация за рента').panel('options');
        rentaPanelOptions.collapsible = false;
        jQuery('#add-edit-accordion').accordion('getPanel', 'Информация за рента').panel('options', rentaPanelOptions);
        
        jQuery('#add-edit-accordion').accordion('getPanel', 'Специфична рента').panel('collapse');
        var rentaTypesPanelOptions = jQuery('#add-edit-accordion').accordion('getPanel', 'Специфична рента').panel('options');
        rentaTypesPanelOptions.collapsible = false;
        jQuery('#add-edit-accordion').accordion('getPanel', 'Специфична рента').panel('options', rentaTypesPanelOptions);


        jQuery('#renta-fill-text').show();
        jQuery('#agg-type').show();
        jQuery('#is-subleased').hide();
        jQuery('#is-closed-for-editing-container').hide();
    }
    else if (selection == 5) {
        /* Съвместна обработка : Joint processing */

        jQuery('#contract-due-date-legend').show();
        jQuery('#contract-due-date').show();
        if (copyContractID == undefined) {
            jQuery('#contract-start-date > input').datebox('setValue', businessStartDate);
            jQuery('#contract-due-date > input').datebox('setValue', businessEndDate);
        }
        jQuery('#box-extra-fields').show();

        jQuery('#add-edit-accordion').accordion('getPanel', 'Информация за рента').panel('expand');
        var rentaPanelOptions = jQuery('#add-edit-accordion').accordion('getPanel', 'Информация за рента').panel('options');
        rentaPanelOptions.collapsible = true;
        jQuery('#add-edit-accordion').accordion('getPanel', 'Информация за рента').panel('options', rentaPanelOptions);

        jQuery('#add-edit-accordion').accordion('getPanel', 'Специфична рента').panel('collapse');
        var rentaTypesPanelOptions = jQuery('#add-edit-accordion').accordion('getPanel', 'Специфична рента').panel('options');
        rentaTypesPanelOptions.collapsible = false;
        jQuery('#add-edit-accordion').accordion('getPanel', 'Специфична рента').panel('options', rentaTypesPanelOptions);

        jQuery('#sv-fill-text').hide();
        jQuery('#add-edit-accordion').accordion('getPanel', 'Информация от ОСЗ').panel('collapse');
        var oszPanelOptions = jQuery('#add-edit-accordion').accordion('getPanel', 'Информация от ОСЗ').panel('options');
        oszPanelOptions.collapsible = false;
        jQuery('#add-edit-accordion').accordion('getPanel', 'Информация от ОСЗ').panel('options', oszPanelOptions);
        jQuery('#ds-fill-text').hide();
        jQuery('#box-sv-fields').show();
        jQuery('#box-ds-fields').show();
        jQuery('#agg-type').hide();
        jQuery('#is-subleased').hide();
        jQuery('#is-closed-for-editing-container').hide();
    }else {
        /* 2 Аренда , 3 Наем : rents */

        jQuery('#contract-due-date-legend').show();
        jQuery('#contract-due-date').show();
        if (copyContractID == undefined) {
            jQuery('#contract-start-date > input').datebox('setValue', businessStartDate);
            jQuery('#contract-due-date > input').datebox('setValue', businessEndDate);
        }
        jQuery('#box-extra-fields').show();

        jQuery('#add-edit-accordion').accordion('getPanel', 'Информация за рента').panel('expand');
        var rentaPanelOptions = jQuery('#add-edit-accordion').accordion('getPanel', 'Информация за рента').panel('options');
        rentaPanelOptions.collapsible = true;
        jQuery('#add-edit-accordion').accordion('getPanel', 'Информация за рента').panel('options', rentaPanelOptions);

        jQuery('#add-edit-accordion').accordion('getPanel', 'Специфична рента').panel('expand');
        var rentaTypesPanelOptions = jQuery('#add-edit-accordion').accordion('getPanel', 'Специфична рента').panel('options');
        rentaTypesPanelOptions.collapsible = true;
        jQuery('#add-edit-accordion').accordion('getPanel', 'Специфична рента').panel('options', rentaTypesPanelOptions);

        jQuery('#sv-fill-text').hide();
        jQuery('#ds-fill-text').hide();
        jQuery('#add-edit-accordion').accordion('getPanel', 'Информация от ОСЗ').panel('expand');
        var oszPanelOptions = jQuery('#add-edit-accordion').accordion('getPanel', 'Информация от ОСЗ').panel('options');
        oszPanelOptions.collapsible = true;
        jQuery('#add-edit-accordion').accordion('getPanel', 'Информация от ОСЗ').panel('options', oszPanelOptions);
        jQuery('#box-sv-fields').show();
        jQuery('#box-ds-fields').show();
        jQuery('#agg-type').hide();
        jQuery('#is-subleased').show();
        jQuery('#is-closed-for-editing-container').show();
    }

    if ([2,3].includes(selection)) {
        jQuery('#contract-group-container').show();
    }

    contract_window.window('center');
}

function addRentaFieldMulti(){
    var rows = jQuery('#multi-renta-table').find('.js-multi-renta-type-row');
    var rentaRows = rows.length;
    var id;
    if(rentaRows != 0)
    {
        var id_arr = rows[rentaRows-1].id.split('-');
        id = id_arr[id_arr.length-1];
    }
    if(rentaRows == jQuery('#multi-renta-type-cb-0').combobox('getData').length - 2) {
        return false;
    }
    if(id != undefined)
    {
        rentaRows = parseInt(id);
    }
    var removeButton = '<a id="remove-multi-renta-row-btn-' + (rentaRows+1) + '" href="javaScript:void(0)" class="easyui-linkbutton" onClick="removeMultiRentaField(' + (rentaRows+1) + ');"data-options="iconCls:\'icon-cancel\'">&nbsp;</a>';

    var comboHTML ='<tr><td style="padding-left:10px"><select style="width: 80px;" class="js-multi-renta-type-row" id="multi-renta-type-cb-'+(rentaRows+1)+'"></select></td>';
    comboHTML += '<td><input style="width: 80px;" class="js-multi-renta-value" id="multi-renta-natura-'+(rentaRows+1)+'"></td><td>'+removeButton+'</td></tr>';

    jQuery('#multi-renta-table').append(comboHTML);

    jQuery('#remove-multi-renta-row-btn-' + (rentaRows+1)).linkbutton({
        iconCls: 'icon-cancel',
        width:26
    });
    jQuery('#multi-renta-type-cb-'+(rentaRows+1)).combobox({
        url: 'index.php?common-rpc=renta-types-combobox',
        rpcParams: [{
            as_list: true
        }],
        valueField: 'id',
        textField: 'name',
        width: 205,
        onBeforeLoad: function () {
            jQuery('#multi-renta-natura-'+(rentaRows+1)).numberspinner({
                min: 0,
                disabled: true,
                required: false,
                missingMessage: 'Моля задайте количество на горепосочената рента в натура(количество за декар).',
                precision: 3,
                width: 95,
                parser: function (value) {
                    if(value == '-') {
                        return value;
                    }
                    if(jQuery.isNumeric(value) && parseFloat(value) >= 0) {
                        var output = parseFloat(value).toFixed(3);
                        return output;
                    }
                }
            });
        },
        onSelect: function (rec) {
            if(jQuery('#multi-renta-type-cb-'+(rentaRows+1)).combobox('getValue') == 0)
            {
                jQuery('#multi-renta-natura-'+(rentaRows+1)).numberspinner('setValue', '0');
            }
            var rowArr = jQuery('.js-multi-renta-type-row');
            var rowVars = [];
            rowVars[0] = jQuery('#multi-renta-type-cb-0').combobox('getValue');

            var tmpVars = jQuery('.js-multi-renta-type-row');
            for (var i = 0; i < tmpVars.length; i++) {
                rowVars[parseInt(i + 1)] = jQuery('#' + tmpVars[i].id).combobox('getValue');
            }

            if(rec.id != 0)
            {
                var inRes = jQuery.inArray(String(rec.id),rowVars);
                if (inRes != -1 && jQuery.inArray(String(rec.id),rowVars,inRes+1) != -1) {
                    jQuery('#renta-error-win').window('open');
                    jQuery('#multi-renta-type-cb-'+(rentaRows+1)).combobox('clear');
                }
            }
        },
        onChange: function (newValue, oldValue) {
            onChangeRentaTypesCombobox(this, newValue);
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

function onChangeRentaTypesCombobox(element, newValue) {
    var renta_nat_unit_id = element.id.split("-");
    var id = renta_nat_unit_id[renta_nat_unit_id.length - 1];
    var data = jQuery('#multi-renta-type-cb-' + id).combobox('getData');
    var natura_amount = jQuery('#multi-renta-natura-' + id);

    //get unit value for renta type and set to field
    jQuery.each(data, function (key, value) {
        if((!value.name || value.name == '-') && value.id == newValue)
        {
            natura_amount.numberspinner({required: false, disabled: true});
            natura_amount.numberspinner('clear');
            return false;
        }
        else if(value.id == newValue) {
            natura_amount.numberspinner({required: true, disabled: false});
            return false;
        }
    });
}

function addEditRentaField(params){
    var rows = jQuery('#renta-table').find('.js-renta-type-row');
    var rentaRows = rows.length;

    var removeButton = '<a id="remove-renta-row-btn-' + (rentaRows) + '" href="javaScript:void(0)" class="easyui-linkbutton" onClick="removeRentaField(' + (rentaRows) + ');"data-options="iconCls:\'icon-cancel\'">&nbsp;</a>';
    var addButton = '<a id="add-new-renta-btn" href="javaScript:void(0)" class="easyui-linkbutton" onClick="addEditRentaField();" data-options="iconCls:\'icon-add\'"></a>';

    var comboHTML ='<tr><td style="padding-left:10px"><select class="js-renta-type-row" style="width: 60px;" id="renta-type-cb-'+(rentaRows)+'"></select></td>';

    //first row
    if(rentaRows == 0) {
        comboHTML += '<td><input class="js-renta-value" style="width: 60px;" id="renta-value-'+(rentaRows)+'"></td><td>'+addButton+'</td></tr>';
    } else {
        comboHTML += '<td><input class="js-renta-value" style="width: 60px;" id="renta-value-'+(rentaRows)+'"></td><td>'+removeButton+'</td></tr>';
    }

    jQuery('#renta-table').append(comboHTML);
    jQuery('#remove-renta-row-btn-' + (rentaRows)).linkbutton({
        iconCls: 'icon-cancel',
        width:32
    });

    if(rentaRows == 0) {
        jQuery('#add-new-renta-btn').linkbutton({
            iconCls: 'icon-add',
            width:32,
            disabled: true
        });
    } else {
        jQuery('#add-new-renta-btn').linkbutton({
            disabled: true
        });
    }

    jQuery('#renta-type-cb-'+(rentaRows)).combobox({
        url: 'index.php?common-rpc=renta-types-combobox',
        rpcParams: [{
            as_list: true
        }],
        valueField: 'id',
        textField: 'name',
        width: 205,
        onLoadSuccess: function () {
            var renta_id = 0;
            var renta_value = 0;

            if(params) {
                renta_value = params['data']['renta_value'];
                renta_id = params['data']['renta_id'];
            }
            jQuery('#renta-type-cb-'+(rentaRows)).combobox('select', renta_id);

            jQuery('#renta-value-'+(rentaRows)).numberspinner({
                min: 0,
                precision: 3,
                missingMessage: 'Моля задайте количество на горепосочената рента в натура(количество за декар).',
                width: 95,
                value: renta_value
            });
        },
        onSelect: function (rec) {
            const rowArr = jQuery('.js-renta-type-row');
            const rentaTypesArr = Array.from(rowArr).map((row) => 
                jQuery(`#${row.id}`).combobox('getValue')
            );

            let oldValue = jQuery(`#renta-type-cb-${rentaRows}`).combobox('getValue');
            var isRentaTypeSelected = jQuery.inArray(String(rec.id), rentaTypesArr);

            if(isRentaTypeSelected !== -1) {
                jQuery.messager.alert('Внимание', 'Вече има въведена натура от избраният тип. Моля изберете друг тип натура!', 'warning');
                setTimeout(function(){
                    jQuery(`#renta-type-cb-${rentaRows}`).combobox('select', oldValue);
                },0);
                return false;
            }

            setTimeout(function(){
                const currentValue  = jQuery('#renta-type-cb-'+(rentaRows)).combobox('getValue');
                const currnetРentaTypesArr = Array.from(rowArr).map((row) => 
                    jQuery(`#${row.id}`).combobox('getValue')
                );

                if(0 !== currentValue && !currnetРentaTypesArr.includes('0')) {
                    jQuery('#add-new-renta-btn').linkbutton({
                        disabled: false
                    });
                } else {
                    jQuery('#add-new-renta-btn').linkbutton({
                        disabled: true
                    });
                }
            },0);
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

function removeRentaField(index) {
    var parent = jQuery('#renta-type-cb-'+index).parent().parent();
    parent.detach();

    const rowArr = jQuery('.js-renta-type-row');
    const rentaTypesArr = Array.from(rowArr).map((row) => 
        jQuery(`#${row.id}`).combobox('getValue')
    );

    if(!rentaTypesArr.includes('0')) {
        jQuery('#add-new-renta-btn').linkbutton({
            disabled: false
        });
    } else {
        jQuery('#add-new-renta-btn').linkbutton({
            disabled: true
        });
    }
}

function removeMultiRentaField(index) {
    var parent = jQuery('#multi-renta-type-cb-'+index).parent().parent();
    parent.detach();
}

function clearAddEditPanelFields() {
    let new_farming_id = jQuery('#contract-farming > input').combobox('getValue') 
                         ? jQuery('#contract-farming > input').combobox('getValue') 
                         : null;

    jQuery('#contract-type > input').combobox('loadData',ComboboxData.ContractTypeCombobox);
    jQuery('#contract-type > input').combobox('enable');
    jQuery('#contract-group > input').combobox('loadData', ComboboxData.ContractGroupsCombobox);
    jQuery('#contract-group > input').combobox('enable');
    jQuery('#contract-number > input').val('');
    jQuery('#contract-date > input').datebox('reset');
    jQuery('#contract-start-date > input').datebox({'editable': true});
    jQuery('#contract-due-date > input').datebox({'editable': true});
    jQuery('#contract-start-date > input').datebox({'disabled': false});
    jQuery('#contract-due-date > input').datebox({'disabled': false});
    
    jQuery('#contract-start-date > input').datebox('reset');
    jQuery('#contract-due-date > input').datebox('reset');
    if (new_farming_id) {
        jQuery('#contract-farming > input').combobox('select',new_farming_id);
    }
    jQuery('#tom > input').val('');
    jQuery('#delo > input').val('');
    jQuery('#na-num > input').val('');
    jQuery('#court > input').val('');
    jQuery('#sv-num > input').val('');
    jQuery('#sv-date > input').datebox('reset');
    jQuery('#osz-num > input').val('');
    jQuery('#osz-date > input').datebox('reset');
    jQuery('#renta_price').numberspinner('clear');
    jQuery('#overall_renta_price').numberspinner('clear');
    jQuery('#pd-day > input').numberspinner('clear');
    jQuery('#pd-month > input').combobox('reset');
    jQuery('#contract-agg-type > input').combobox('reset');
    jQuery("#copy-contract-files-check").prop('checked', false);
    jQuery("#use-overall-renta").prop('checked', false);
    jQuery("#arable-renta").prop('checked', false);
    jQuery("#is-closed-for-editing").prop('checked', false);
    jQuery("#contract-is-subleased").prop('checked', false);
    onUseOverallRentaChange();
    removeRentaFields();
    rentaTypes = [];

    jQuery("#contract-renta-types>fieldset").remove();
}

function removeRentaFields() {
    var total = jQuery('.js-renta-type-row').length;
    for (var i = 0 ; i <= total; i++) {
        removeRentaField(i);
    }
}

function clearContractsAnnexesFields() {
    jQuery("#annex-number > input").val('');
    jQuery("#annex-start-date > input").datebox('reset');
    jQuery("#annex-date > input").datebox('reset');
    jQuery("#annex-due-date > input").datebox('reset');
    jQuery("#annex-sv-num > input").val('');
    jQuery("#annex-sv-date > input").datebox('reset');
    jQuery("#annex-osz-num > input").val('');
    jQuery("#annex-osz-date > input").datebox('reset');
    jQuery("#annex-renta > input").numberspinner('clear');
    jQuery("#annex-overall-renta > input").numberspinner('clear');
    jQuery("#annex-pd-day > input").numberspinner('clear');
    jQuery("#annex-pd-month > input").combobox('reset');
    jQuery("#annex-comment > textarea").val('');
    jQuery("#annex-renta-type-cb-0").combobox('reset');
    jQuery("#annex-renta-value-0").numberspinner('clear');

    removeContAnnexRentaFields();
}

function toggleOverallRentaField() {
    var selectedContract = jQuery('#contracts-tree').tree('getSelected');
    
    TF.Rpc.Contracts.ContractsTree.initAnnexRenta(selectedContract.id)
        .done(function (data) {
            jQuery('#annex-renta > input').numberspinner({
                precision: 2,
                min: 0,
                value: parseFloat(data.renta)
            });

            removeAnnexRentaFields();
            if (data.additionalRentas) {
                rentaResult = jQuery.map(data.additionalRentas, function (value, index) {
                    return [value];
                });

                for(var i = 0; i<rentaResult.length; i++) {
                    var addFieldParams = {index:i, data:rentaResult[i]};
                    addAnnexRentaField(addFieldParams);
                }
            } else {
                addAnnexRentaField();
            }
            if(jQuery.isNumeric(data.overall_renta)) {
                jQuery('#annex-overall-renta > input').numberspinner({
                    precision: 2,
                    min: 0,
                    value: parseFloat(data.overall_renta)
                });
            }
        })
        .fail(function (data) {
            RpcErrorHandler.show(data);
        });
    
    if (selectedContract.attributes.overall_renta) {
        jQuery("#annex-renta").hide();
        jQuery("#annex-renta-label").hide();
        jQuery("#annex-overall-renta").show();
        jQuery("#annex-overall-renta-label").show();
    } else {
        jQuery("#annex-renta").show();
        jQuery("#annex-renta-label").show();
        jQuery("#annex-overall-renta").hide();
        jQuery("#annex-overall-renta-label").hide();
    }

}

function removeContAnnexRentaFields() {
    total = jQuery('.js-annex-renta-type-row').length;
    for (var i = 1 ; i < total; i++) {
        removeContAnnexRentaField(i);
    }
}

function removeContAnnexRentaField(index) {
    var parent = jQuery('#annex-renta-type-cb-'+index).parent().parent();
    parent.detach();
}

function clearMultiEditPanelFields() {

    jQuery('#multi-renta > input').numberspinner('clear');
    jQuery('#multi-renta-type-cb-0').combobox('reset');
    jQuery('#multi-renta-natura-0').numberspinner('clear');
    jQuery("#multiedit-area-for-rent-checkbox").prop('checked', false);

    removeMultiRentaFields();
}

function removeMultiRentaFields() {
    total = jQuery('.js-multi-renta-type-row').length;
    for (var i = 1 ; i <= total; i++) {
        removeMultiRentaField(i);
    }
}

function setAddEditCopyPanelData(data, preventDatesDisable = false) {
    displayAddEditPanelFields(data.nm_usage_rights);
    var contractTypeElement = jQuery('#contract-type > input');
    var contractTypes = ComboboxData.ContractTypeCombobox;
    if(data.id) {
        var allowedTypes = [CONTRACT_TYPE_RENT, CONTRACT_TYPE_LEASE, CONTRACT_TYPE_COOPERATIVE_USAGE];
        contractTypeElement.combobox('disable');
        var contractType = data.contractType ?? data.nm_usage_rights ?? undefined;
        if(!data.is_annex){
            if(allowedTypes.includes(contractType) ){
                contractTypes = contractTypes.filter(type => allowedTypes.includes(type.id));
                contractTypeElement.combobox('enable');
            }
        }


    }
    contractTypeElement.combobox('loadData', contractTypes);
    contractTypeElement.combobox('select', data.nm_usage_rights);

    if (data.group) {
        jQuery('#contract-group > input').combobox('select', data.group);
    }

    jQuery('#contract-date > input').datebox('setValue', data.c_date);
    jQuery('#contract-start-date > input').datebox('setValue', data.start_date);
    jQuery('#contract-due-date > input').datebox('setValue', data.due_date);
    
    jQuery('#contract-farming > input').combobox('setValue', data.farming_id);
    jQuery('#sv-num > input').val(data.sv_num);
    jQuery('#sv-date > input').datebox('setValue', data.sv_date);
    jQuery('#osz-num > input').val(data.osz_num);
    jQuery('#osz-date > input').datebox('setValue', data.osz_date);
    tinyMCE.get('contract-comment-textarea').setContent(data.comment ? data.comment : '');
    jQuery('#renta_price').numberspinner('setValue', data.renta);
    jQuery('#overall_renta_price').numberspinner('setValue', data.overall_renta);
    jQuery('#pd-day > input').numberspinner('setValue', data.payday[0]);
    jQuery('#pd-month > input').combobox('setValue', data.payday[1]);
    jQuery('#contract-agg-type > input').combobox('setValue', data.agg_type);
    jQuery('#contract-is-subleased').prop('checked', data.is_declaration_subleased);
    jQuery('#is-closed-for-editing').prop('checked', data.is_closed_for_editing);

    jQuery('#copy-contract-files-check').prop('checked', false);

    //used click() in orther to trigger the change event
    if(data.overall_renta != null) {
        if(!jQuery('#use-overall-renta').prop('checked')) {
            jQuery('#use-overall-renta').click();
        } else {
            jQuery('#use-overall-renta').click();
            jQuery('#use-overall-renta').click();
        }
    } else {
        if(jQuery('#use-overall-renta').prop('checked')) {
            jQuery('#use-overall-renta').click();
        } else {
            jQuery('#use-overall-renta').click();
            jQuery('#use-overall-renta').click();
        }
    }
    jQuery('#tom > input').val(data.tom);
    jQuery('#na-num > input').val(data.na_num);
    jQuery('#delo > input').val(data.delo);
    jQuery('#court > input').val(data.court);

    if (data.isEdit || data.is_copy) {
        jQuery('#contract-number > input').val(data.c_num);
    } else {
        jQuery('#contract-number > input').val('');
    }

    if((data.isEdit || data.is_copy) && allowedTypes.includes(contractType)) {
        for(var i = 0; i<rentaResult.length; i++) {
            var addFieldParams = {index:i, data:rentaResult[i]};
            addEditRentaField(addFieldParams);
        }

        if(rentaResult.length == 0) {
            addEditRentaField();
        }

        if(data.rentTypes) {
            jQuery(".plot-rent-nat-rows").remove();
            addEditPlotRentaNatField(null, 'contract');
            initAddEditRentTypesWindow();
            editRentTypeId = null;
            for(var i = 0; i<data.rentTypes.length; i++) {
                rentTypeId = i;
                addEditRentType(data.rentTypes[i]);
            }

            jQuery('#add-edit-accordion').accordion('getPanel', 'Специфична рента').panel('expand');
            var rentTypeOptions = jQuery('#add-edit-accordion').accordion('getPanel', 'Специфична рента').panel('options');
            rentTypeOptions.collapsible = true;
            jQuery('#add-edit-accordion').accordion('getPanel', 'Специфична рента').panel('options', rentTypeOptions);
        }
    }
}

function initAreaEqualize() {
    var alert_text = "Площта по договор на всеки парцел ще бъде изравнена с площта по документ за всеки имот, при който разликата между двете площи е по-малка или равна на 10 кв. метра. При приравняването на площите няма да се вземат предвид договори, създадени автоматично през подмодул \"Преотдадени\". Искате ли да продължите?";
    jQuery.messager.confirm('Потвърждение', alert_text, function (r) {
        if (r) {
            TF.Rpc.Contracts.ContractsTree.initAreaEqualize()
            .done(function (data) {
                if(data.length > 0){
                    jQuery.messager.alert('Съобщение', generatePlotsWithSRMsg(data), 'info');
                } else {
                    jQuery.messager.alert('Съобщение','Успешно изравняване на площите.', 'info');
                }
            })
            .fail(function () {
                jQuery.messager.alert('Съобщение','Възникна грешка при изравняване на площите.', 'warning');
            })
        }
    });
}

function generatePlotsWithSRMsg(plots) {
    msg = 'Следните имоти имат специфични ренти и техните площ и рента не са променени:<br><br>';
    for (const id in plots){
        msg += plots[id].kad_ident + ' - <a href="index.php?page=Contracts.Home&contract_id=' + plots[id].contract_id + '" target="_blank">' + plots[id].c_num_text + '</a><br>';
    }

    return msg;
}

function initContractsTreeFilter () {
    var obj = getFilterContractsTreeParams();
    if(!obj) return false;

    //reinit tree with query params
    initContractsTree(1, obj);

    jQuery('#win-filter-contracts').window('close');
}

function clearSortContractsTreeFields() {
    jQuery('input[name=sortcontractsgroup]:checked').prop('checked', false);
    jQuery('#type-desc').prop('checked', true);
}

function initSearchFieldsComplete() {

    var owners = new Bloodhound({
        datumTokenizer: Bloodhound.tokenizers.whitespace('value'),
        queryTokenizer: Bloodhound.tokenizers.whitespace,
        remote: {
            url: 'index.php?owners-rpc=owners-list',
            cache: false,
            prepare: function (query, settings) {
                settings.query = query;
                return settings;
            },
            transport: function (settings, onSuccess, onError) {
                options = {
                    type: 'POST',
                    dataType: 'json',
                    contentType: "application/json; charset=UTF-8",
                    data: JSON.stringify({
                        "method": "read",
                        "params": ["owners", settings.query],
                        "id": 1,
                        "jsonrpc": "2.0"
                    })
                };

                jQuery.ajax('index.php?owners-rpc=owners-list', options)
                .done(done)
                .fail(fail)
                .always(always);

                function done(data, textStatus, request) {
                    onSuccess(data.result);
                }
                function fail(request, textStatus, errorThrown) {
                    onError(errorThrown);
                }
                function always() {
                }
            },
        },
    });

    jQuery('#search-owner-name').typeahead(
    {
        hint: false,
        highlight: false,
        minLength: 1,
        limit: 10,
        menu:jQuery('#search-owner-name-typeahead-target'),
    },
    {
        name: 'owners',
        displayKey: 'value',
        source: owners.ttAdapter(),
    });

    jQuery('#search-owner-name').on('typeahead:open', function (e, datum) {
        jQuery('#search-owner-name-typeahead-target').width(jQuery('#search-owner-name').width());
        var offset = jQuery('#search-owner-name').offset();
        jQuery("#search-owner-name-typeahead-target").offset({
                top: offset.top + jQuery('#search-owner-name').height(),
                left: offset.left
            });
        jQuery('#search-owner-name-typeahead-target').css("z-index",9999);
    });

    jQuery('#search-owner-name').on('typeahead:selected', function (e, datum) {
        jQuery('#search-owner-name').typeahead('close');
        jQuery('#search-owner-name-typeahead-target').css("z-index",-1);
    });

    jQuery('#search-owner-name').on('typeahead:idle', function (e, datum) {
            jQuery('#search-owner-name').typeahead('close');
            jQuery('#search-owner-name-typeahead-target').css("z-index",-1);
    });

    var representatives = new Bloodhound({
        datumTokenizer: Bloodhound.tokenizers.whitespace('value'),
        queryTokenizer: Bloodhound.tokenizers.whitespace,
        remote: {
            url: 'index.php?owners-rpc=owners-list',
            cache: false,
            prepare: function (query, settings) {
                settings.query = query;
                return settings;
            },
            transport: function (settings, onSuccess, onError) {
                options = {
                    type: 'POST',
                    dataType: 'json',
                    contentType: "application/json; charset=UTF-8",
                    data: JSON.stringify({
                        "method": "read",
                        "params": ["reps", settings.query],
                        "id": 1,
                        "jsonrpc": "2.0"
                    })
                };

                jQuery.ajax('index.php?owners-rpc=owners-list', options)
                .done(done)
                .fail(fail)
                .always(always);

                function done(data, textStatus, request) {
                    onSuccess(data.result);
                }
                function fail(request, textStatus, errorThrown) {
                    onError(errorThrown);
                }
                function always() {
                }
            },
        },
    });

    jQuery('#search-represent-name').typeahead(
    {
        hint: false,
        highlight: false,
        minLength: 1,
        limit: 10,
        menu:jQuery('#search-represent-name-typeahead-target'),
    },
    {
        name: 'representatives',
        displayKey: 'value',
        source: representatives.ttAdapter(),
    });

    jQuery('#search-represent-name').on('typeahead:open', function (e, datum) {
        jQuery('#search-represent-name-typeahead-target').width(jQuery('#search-represent-name').width());
        var offset = jQuery('#search-represent-name').offset();
        jQuery("#search-represent-name-typeahead-target").offset({
                top: offset.top + jQuery('#search-represent-name').height(),
                left: offset.left
            });
        jQuery('#search-represent-name-typeahead-target').css("z-index",9999);
    });

    jQuery('#search-represent-name').on('typeahead:selected', function (e, datum) {
        jQuery('#search-represent-name').typeahead('close');
        jQuery('#search-represent-name-typeahead-target').css("z-index",-1);
    });

    jQuery('#search-represent-name').on('typeahead:idle', function (e, datum) {
            jQuery('#search-represent-name').typeahead('close');
            jQuery('#search-represent-name-typeahead-target').css("z-index",-1);
    });

    var companies = new Bloodhound({
        datumTokenizer: Bloodhound.tokenizers.whitespace('value'),
        queryTokenizer: Bloodhound.tokenizers.whitespace,
        remote: {
            url: 'index.php?owners-rpc=owners-list',
            cache: false,
            prepare: function (query, settings) {
                settings.query = query;
                return settings;
            },
            transport: function (settings, onSuccess, onError) {
                options = {
                    type: 'POST',
                    dataType: 'json',
                    contentType: "application/json; charset=UTF-8",
                    data: JSON.stringify({
                        "method": "read",
                        "params": ["companies", settings.query],
                        "id": 1,
                        "jsonrpc": "2.0"
                    })
                };

                jQuery.ajax('index.php?owners-rpc=owners-list', options)
                .done(done)
                .fail(fail)
                .always(always);

                function done(data, textStatus, request) {
                    onSuccess(data.result);
                }
                function fail(request, textStatus, errorThrown) {
                    onError(errorThrown);
                }
                function always() {
                }
            },
        },
    });

    jQuery('#search-company-name').typeahead(
    {
        hint: false,
        highlight: false,
        minLength: 1,
        limit: 10,
        menu:jQuery('#search-company-name-typeahead-target'),
    },
    {
        name: 'companies',
        displayKey: 'value',
        source: companies.ttAdapter(),
    });

    jQuery('#search-company-name').on('typeahead:open', function (e, datum) {
        jQuery('#search-company-name-typeahead-target').width(jQuery('#search-company-name').width());
        var offset = jQuery('#search-company-name').offset();
        jQuery("#search-company-name-typeahead-target").offset({
                top: offset.top + jQuery('#search-company-name').height(),
                left: offset.left
            });
        jQuery('#search-company-name-typeahead-target').css("z-index",9999);
    });

    jQuery('#search-company-name').on('typeahead:selected', function (e, datum) {
        jQuery('#search-company-name').typeahead('close');
        jQuery('#search-company-name-typeahead-target').css("z-index",-1);
    });

    jQuery('#search-company-name').on('typeahead:idle', function (e, datum) {
            jQuery('#search-company-name').typeahead('close');
            jQuery('#search-company-name-typeahead-target').css("z-index",-1);
    });

}

jQuery(function() {
    jQuery('#btnsaveowner').bind('click', function() {
        if (validateNewOwnerSubmitInfo())
        {
            var obj = getAddOwnerInputFieldsData();
            if (!isEditing) {

                TF.Rpc.Contracts.ContractsOwnersGrid.addNewOwner(obj)
                    .done(function (response)
                    {
                        if(lastAddedParent == 0) {
                            lastAddedParent = response;
                        }
                        lastAddedOwner = response;

                        if (jQuery('#owners-add-tables').data().hasOwnProperty('datagrid')) {
                            jQuery('#owners-add-tables').datagrid('reload');
                        }
                        addingOwnerHeritor = false;
                        isEditing = false;

                        jQuery('#win-add-new-owner').window('close');

                        if (obj.is_dead) {
                            if(lastAddedParent != lastAddedOwner && lastAddedOwner != 0 && lastAddedParent != 0) {
                                TF.Rpc.Owners.OwnersHeritorsTree.addOwnerHeritor(lastAddedOwner, lastAddedParent)
                                    .done(function (data) {

                                    }).
                                fail(function (errorObj) {
                                    jQuery.messager.alert('Грешка', errorObj.getMessage(), 'warning');
                                });
                            }
                            lastAddedParent = response;
                        } else {
                            if(lastAddedParent != lastAddedOwner && lastAddedOwner != 0 && lastAddedParent != 0) {
                                TF.Rpc.Owners.OwnersHeritorsTree.addOwnerHeritor(lastAddedOwner, lastAddedParent)
                                    .done(function (data) {
                                        jQuery('#win-choose-heritor').window('close');
                                        jQuery('#owners-add-tables').datagrid('loadRpc');
                                        jQuery('#contract-owners-tables').treegrid('loadRpc');
                                    }).
                                fail(function (errorObj) {
                                    jQuery.messager.alert('Грешка', errorObj.getMessage(), 'warning');
                                });
                            }
                        }
                        if (!obj.is_dead) {
                            lastAddedOwner = 0;
                            lastAddedParent = 0;
                        } else {
                            addOwnerHeritorHelper(lastAddedParent);
                        }
                    })
                    .fail(function (data) {
                        if (data.getCode() == -33310) {
                            jQuery.messager.alert('Грешка', data.getMessage(), 'warning');
                            return false;
                        } else {
                            RpcErrorHandler.show(data);
                            return false;
                        }
                    });
            } else {
                var selectedOwner = jQuery('#contract-owners-tables').treegrid('getSelected');
                if(selectedOwner && obj.is_legal === true) {
                    TF.Rpc.Owners.OwnersTree.editLegalOwner(obj,selectedOwner.owner_id)
                        .done(function (data) {
                            jQuery('#win-add-new-owner').window('close');
                            isEditing = false;
                        })
                        .fail(function (data) {
                            if (data.getCode() == -33310) {
                                jQuery.messager.alert('Грешка', data.getMessage(), 'warning');
                                return false;
                            } else {
                                RpcErrorHandler.show(data);
                                return false;
                            }
                        });
                }else if (selectedOwner && obj.is_legal === false){
                    TF.Rpc.Owners.OwnersTree.editOwner(obj,selectedOwner.owner_id)
                        .done(function (response) {
                            if(lastAddedParent == 0) {
                            lastAddedParent = response;
                        }
                        lastAddedOwner = response;

                        if (jQuery('#owners-add-tables').data().hasOwnProperty('datagrid')) {
                            jQuery('#owners-add-tables').datagrid('reload');
                        }
                            addingOwnerHeritor = false;
                        isEditing = false;

                        if (obj.is_dead) {
                            if(lastAddedParent != lastAddedOwner && lastAddedOwner != 0 && lastAddedParent != 0) {
                                TF.Rpc.Owners.OwnersHeritorsTree.addOwnerHeritor(lastAddedOwner, lastAddedParent)
                                    .done(function (data) {

                                    }).
                                fail(function (errorObj) {
                                    jQuery.messager.alert('Грешка', errorObj.getMessage(), 'warning');
                                });
                            }
                            lastAddedParent = response;
                        } else {
                            if(lastAddedParent != lastAddedOwner && lastAddedOwner != 0 && lastAddedParent != 0) {
                                TF.Rpc.Owners.OwnersHeritorsTree.addOwnerHeritor(lastAddedOwner, lastAddedParent)
                                    .done(function (data) {
                                        jQuery('#win-choose-heritor').window('close');
                                        jQuery('#owners-add-tables').datagrid('loadRpc');
                                    }).
                                fail(function (errorObj) {
                                    jQuery.messager.alert('Грешка', errorObj.getMessage(), 'warning');
                                });
                            }
                        }
                        if (!obj.is_dead) {
                            lastAddedOwner = 0;
                            lastAddedParent = 0;
                        } else {
                            addOwnerHeritorHelper(lastAddedParent);
                        }
                        })
                        .fail(function (data) {
                            jQuery.messager.alert('Грешка', data.getMessage(), 'warning');
                        });
                }
                jQuery('#win-add-new-owner').window('close');
            }
        }
    });

    jQuery('#btn-search-owner-heritor').bind('click', function () {
        var egn = jQuery('#search-heritor-by-egn').val();

        jQuery('#choose-heritor').combobox({
            url: 'index.php?owners-rpc=owners-heritors-combobox',
            valueField: 'id',
            textField: 'owner_names',
            required: true,
            missingMessage: 'Моля изберете наследник.',
            rpcParams: [lastAddedParent, egn, true],
            filter: function (q, row) {
                var opts = jQuery(this).combobox('options');
                var text = row[opts.textField].toLowerCase();
                var find = q.toLowerCase();
                if (text.indexOf(find) != -1) {
                    return true;
                }
            },
            loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
            loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter,
        });
    });

    jQuery('#btn-add-new-owner-heritor').bind('click', function () {
        addingNewHeritor = true;
        clearAddOwnerInputDataFields();
        onAddownerPanelOpen();
        jQuery('#win-add-new-owner').window('open');
    });
});



function messageContractIsFromSublease(contract) {

    var message = 'Не може да редактирате избрания договор. Той е създаден автоматично от подмодул "Преотдадени". За да направите промени по този договор трябва да редактирате';
        message += ' договор за преотдаване <a href="index.php?page=Subleases.Home&sublease_id=' + contract.attributes.from_sublease + '" target="_blank">№: '+contract.attributes.c_num+'</a>';

    jQuery.messager.alert('Внимание', message, 'warning');
}

function displayRentaInfoForContract() {
    var selectedID = jQuery('#contracts-tree').tree('getSelected').id;

     // This code is commented in order to cover the case from this task: GPS-4225
    // var farmingYear = getFarmingYearId();
    
    var farmingYear = getFarmingYearByCalendarYear(new Date().getFullYear());
    window.open("index.php?page=Payments.Home&contract_id=" + selectedID + "&farming_year=" + farmingYear.year_id, '_blank');
}

function onUseOverallRentaChange() {
    jQuery('#use-overall-renta').change(function () {
        var rentaSpinner = jQuery('#renta_price');
        var overallRentaSpinner = jQuery('#overall_renta_price');
        currnentOverallRenta = jQuery('#overall_renta_price').val();
        currnentRenta = jQuery('#renta_price').val();

        if (jQuery('#use-overall-renta').prop('checked')) {

            if(jQuery('#contract-renta-types fieldset').length > 0){
                jQuery.messager.alert('Внимание', 'Не може да се избере опция "Обща рента" ако вече сте добавили "Специфична рента".', 'warning');
                jQuery('#use-overall-renta').prop('checked', false);
                return false;
            }

            overallRentaSpinner.numberspinner({
                required: true,
                disabled: false,
                precision:2,
                min: 0,
                value: currnentOverallRenta,
                missingMessage: 'Моля въведете обща рента в лева.'
            });
            rentaSpinner.numberspinner({
                required: false,
                disabled: true,
                precision:2,
                min: 0,
                value:currnentRenta,
                missingMessage: 'Моля въведете рента в лева на декар.'
            });

            jQuery('#add-edit-accordion').accordion('getPanel', 'Специфична рента').panel('collapse');
            var rentaTypesPanelOptions = jQuery('#add-edit-accordion').accordion('getPanel', 'Специфична рента').panel('options');
            rentaTypesPanelOptions.collapsible = false;
            jQuery('#add-edit-accordion').accordion('getPanel', 'Специфична рента').panel('options', rentaTypesPanelOptions);
        } else {
            overallRentaSpinner.numberspinner({
                required: false,
                disabled:true,
                precision:2,
                min: 0,
                value: currnentOverallRenta,
                missingMessage: 'Моля въведете обща рента в лева.'
            });
            rentaSpinner.numberspinner({
                required: true,
                disabled: false,
                precision:2,
                min: 0,
                value: currnentRenta,
                missingMessage: 'Моля въведете рента в лева на декар.'
            });

            jQuery('#add-edit-accordion').accordion('getPanel', 'Специфична рента').panel('expand');
            var rentaTypesPanelOptions = jQuery('#add-edit-accordion').accordion('getPanel', 'Специфична рента').panel('options');
            rentaTypesPanelOptions.collapsible = true;
            jQuery('#add-edit-accordion').accordion('getPanel', 'Специфична рента').panel('options', rentaTypesPanelOptions);
        }
    });
}

function initSearchOnEnter() {
    jQuery("#win-filter-contracts").off("keyup").on("keyup", function (event) {
		// Listen for enter key
        if (13 !== event.keyCode) {
			return;
		}
		jQuery("#btn-filter-contracts-tree").click()
    });

    jQuery("#win-multiedit-contracts").off("keyup").on("keyup", function (event) {
		// Listen for enter key
        if (13 !== event.keyCode) {
			return;
		}
		jQuery("#multibtnsave").click()
    });
}
