(function (root, factory) {
    if (typeof define === 'function' && define.amd) {
        // AMD. Register as an anonymous module.
        define([], factory);
    } else {
        Namespace('TF.Rpc.ExceptionsList');
        // Browser globals (root is window)
        root.TF.Rpc.ExceptionsList = factory();
    }
}(typeof self !== 'undefined' ? self : this, function () {
    return {
        NO_RIGHTS: {code: '499', message: 'Нямате права да оперирате с тази функционалност!'},

        METHOD_NOT_FOUND: {code: '-32601', message: 'Method not found'},
        SYSTEM_ERROR: {code: '-32603', message: 'Системна грешка.'},

         
            //Generic fields validation [-33001 ÷ -33050]
        VALIDATION_INVALID_KAD_IDENT: {code: '-33000', message: 'Полето може да съдържа само цифри от 0 до 9'},
        VALIDATION_INVALID_DIGITS_ONLY: {code: '-33001', message: 'Полето може да съдържа само цифри от 0 до 9'},
        VALIDATION_INVALID_NUMBER: {code: '-33002', message: 'Полето не може да съдържа само неотрицателни числа'},
        VALIDATION_INVALID_TEXT: {code: '-33003', message: 'Полето не може да съдържа специални символи'},
        VALIDATION_INVALID_REQUIRED: {code: '-33004', message: 'Полето не може да бъде празно'},
        VALIDATION_INVALID_NOT_NULL: {code: '-33005', message: 'Полето не може да бъде null'},
        VALIDATION_INVALID_INTEGER: {code: '-33006', message: 'Полето може да съдържа само цели, неотрицателни числа'},
        VALIDATION_INVALID_DATE: {code: '-33007', message: 'Полето съдържа невалиден формат дата.'},
        VALIDATION_INVALID_TIMESTAMP: {code: '-33008', message: 'Полето не съдържа валиден формат за час.'},
        VALIDATION_INVALID_COLOR: {code: '-33009', message: 'Полето не съдържа валиден формат за цвят.'},
        VALIDATION_INVALID_FARMING_YEAR: {code: '-33010', message: 'Полето не съдържа валиден формат за стопанска година.'},
        VALIDATION_INVALID_CONTRACT_TYPE: {code: '-33011', message: 'Полето не съдържа валиден формат за тип договор.'},
        VALIDATION_INVALID_ARRAY_TYPE: {code: '-33012', message: 'Група (масив) от еднотипни данни съдържат невалиден елемент.'},
        VALIDATION_INVALID_SORTING_RULE: {code: '-33013', message: 'Невалиден критерий за сортиране!'},
        VALIDATION_INVALID_SORTING_ORDER: {code: '-33014', message: 'Невалиден ред за сортиране!'},
        PASSWORDS_DOES_NOT_MATCH: {code: '-33015', message: 'Паролите не съвпадат'},
        REQUIRED_FIELDS: {code: '-33016', message: 'Моля, въведете стойност в задължителните полета!'},
        USERNAME_NOT_VALID: {code: '-33017', message: 'Невалидно потребителско име. Позволените символи са букви, цифри и долна черта.'},


        NON_EXISTING_USER_ID: {code: '-33310', message: 'Не съществува потребител с такъв идентификационен код!'},
        MAXIMUM_NUMBER_OF_FARMINGS_REACHED: {code: '-33040', message:'Достигнат е максимален брой стопанства за потребителя.'},
        NON_EXISTING_FARMING_SELECTED: {code: '-33041', message:'Избрано е несъществуващо стопанство.'},
        RENTA_TYPE_ALREADY_EXISTS: {code: '-33042', message:'Вече съществува тип рента в натура с такова име!'},
        FARMING_HAS_NO_CONTRACTS: {code: '-33043', message:'Действието изтриване на стопанство няма да засегне информация от модул Имоти!'},
        FARMING_NOT_FOUND: {code: '-33044', message:'Не беше открита информация за стопанството!'},
        NO_FARMING_DELETE_PERMISSIONS: {code: '-33045', message:'Нямате права да за изтриване на стопанство!'}, 
        NO_FARMING_READ_PERMISSIONS: {code: '-33048', message:'Нямате права да за достъп до това стопанство!'}, 
        NO_FARMING_DELETE_PERMISSIONS_DUE_CONTRACTS_EXISTENCE: {code: '-33046', message:'Стопанството не може да бъде изтрито, защото има въведени договори с него!'}, 
        GEOSCAN_USER_ROLE_NOT_MAPPED: {code: '-33047', message:'Геосцан ролята не е вързана към Технофарм ролите. Добавянето на потребител ще бъде пропуснато'}, 
        CONTRACT_GROUP_ALREADY_EXISTS: {code: '-33049', message:'Вече съществува група с такова име!'}, 
        CONTRACT_GROUP_HAS_CONTRACTS: {code: '-33050', message:'Групата не може да бъде изтрита, защото има добавени договори към нея!'}, 


            //Database error codes
        DATABASE_CONNECTION_ERROR: {code: '-33101', message: 'Грешка при връзка с базата данни'},
        DATABASE_INVALID_TABLE_NAME: {code: '-33102', message: 'Няма налични данни в базата данни'},
        ACTIVE_OPERATION_EXISTS: {code: '-33103', message: 'Съществува активна заявка'},
                
            //Plots related error codes[-33151 ÷ -33250]
        CONTRACT_AREA_EXCEEDS_PLOT_AREA: {code: '-33151', message: 'Площта по договори надхвърля площта на имота'},
        NON_EXISTING_CONTRACT_OR_PLOT_NUMBER: {code: '-33152', message: 'Не съществува договор/имот с такъв номер.'},

        INVALID_ANNEX_CONTRACTS: {code: '-33201', message: 'Не може да aнексирате анекс!'},

        ANNEXES_INVALID_ANNEX_DATE: {code: '-33202', message: 'Не може да въвеждате анекси със застъпващи се периоди на действие!'},
        EXISTING_CONTRACT_NUMBER: {code: '-33203', message: 'Съществува договор с такъв номер!'},
        NON_EXISTING_CONTRACT_PLOT_RELATION: {code: '-33204', message: 'Не съществува връзка между избраните имот и договор!'},
        CANNOT_ANNEX_OWN_OR_AGREEMENT_CONTRACT: {code: '-33205', message: 'Към договори от тип собственост или споразумение не могат да бъдат добавяни анекси!'},
        NON_EXISTING_OWNER_ID: {code: '-33206', message: 'Избран е несъществуващ собственик!'},
        CONTRACT_PLOT_RELATION_ALREADY_EXISTS: {code: '-33207', message: 'Вече съществува връзка между избраните договор и имот!'},
        NON_EXISTING_CONTRAGENT_TYPE: {code: '-33208', message: 'Не съществува контрагент от такъв тип!'},
        NON_EXISTING_SUBLEASE_RELATION: {code: '-33209', message: 'Не съществува връзка между избрания договор и имоти!'},
        PLOT_ALREADY_IN_AGREEMENT: {code: '-33210', message: 'Избраният имот вече съществува в това споразумение!'},
        NO_KMS_INTERSECTIONS: {code: '-33211', message: 'Няма имоти, които се пресичат със заредените данни за комасация!'},
        CANNOT_EXPORT_EMPTY_REPORT: {code: '-33212', message: 'Не може да експортнете празна справка!'},
        CONTRACT_PLOT_AREA_EQUALIZING_FAILED: {code: '-33213', message: 'Възникна грешка при приравняване на площите!'},
        CONTRACT_RENT_AREA_EXCEEDS_PLOT_AREA: {code: '-33214', message: 'Площта по ренти надхвърля площта на имота'},
        CONFIRM_DELETING_SUBLEASED_PLOT: {code: '-33215', message: 'Имотът, който искате да изтриете е преотдаден с друг договор.', showOriginalMessage: true},

        INVALID_USER_CONTRACTS_RIGHTS_OWN_CONTRACTS: {code: '-33200', message: 'Нямате права да оперирате с Договори от тип:  Собственост!'},

            //Payments related error codes
        MISSING_RENTA_NATURA: {code: '-33214', message: 'Моля задайте количество на горепосочената рента в натура(количество за декар)!'},
        MISSING_UNIT_VALUE: {code: '-33215', message: 'Моля задайте единична стойност(лв)!'},
        WRONG_CHARGED_RENTA: {code: '-33216', message: 'Моля въведете данни за начислението!'},
        MISSING_CHARGED_RENTA_TYPE: {code: '-33217', message: 'Моля въведете име на начислението!'},
        MISSING_ADD_PAYMENT: {code: '-33218', message: 'Не са открити дължими стойности за избрания тип плащане. Няма да бъде създадена транзакция!'},
        WRONG_FRACTION: {code: '-33219', message: 'Знаменателят трябва да е по-голям или равен на числителя!'},
        WRONG_PERCENT_OWNERSHIP: {code: '-33220', message: 'Процентът собственост трябва да е между 0 и 100!'},
        WRONG_PLOT_AREA: {code: '-33221', message: 'Площта по договор не може да е по-голяма от площта по документ!'},
        NON_EXISTING_PAYMENT_INFO: {code: '-33222', message: 'Не е намерена детайлна информация за избраната транзакция!'},
        MISSING_DATA_TRANSACTION: {code: '-33223', message: 'Не са открити стойности за изплащане. Няма да бъде създадена транзакция!'},
        CANNOT_MODIFY_NON_GROUP_DATA: {code: '-33224', message: 'Не може да променяте данни, които не принадлежат на Вашата потребителска група!'},
        WRONG_PLOT_AREA_FOR_RENT: {code: '-33225', message: 'Площта за рента не може да е по-голяма от площта по документ!'},
        WRONG_PLOT_AREA_FOR_RENT_TOO_LARGE: {code: '-33226', message: 'Площта за рента не може да е по-голяма от площта по договор!'},
        MISSING_FARMING_ID: {code: '-33227', message: 'Стопанство не е посочен. Няма да бъде създадена транзакция!'},
        WRONG_RKO_NUMBER: {code: '-33228', message: 'Грешен РКО номер! РКО номера не може да съдържа само символи и букви.'},
        WRONG_RENT_AREA: {code: '-33229', message: 'Площта по рента не може да е по-голяма от площта по документ!'},
        WRONG_AREA_T0_SUBLEASE: {code: '-33230', message: 'Площта по договор и рента не може да превишава разликата от площа по договор намалена с площа от други частични преотдавания на имота!'},
        UNSUPPORTED_MASSPAYMENT_TYPE: { code: '-33231', message: 'Несъществуващ метод за масови плащания!'},
        INVALID_PAYMENT_DATE: { code: '-33233', message: 'Опитвате се да изплатите сума на починал собственик. За да продължите, моля коригирайте датата на плащане, с такава преди датата на почиване на избрания собственик.'},
        INVALID_DEAD_DATE: { code: '-33234', message: 'Датата на смъртта не може да бъде в бедещето.'},
        CAN_NOT_CHANGE_DEAD_DATE: { code: '-33235', message: 'Не можете да промените датата на смъртта на избраният собственик. Открити са изплатени ренти за следващи стопански години.'},
        WRONG_EGNS_FOR_XML_EXPORT_DECL_73: {code: '-33236', message: 'Експорта не може да бъде приключен. В списъка има невалидни данни за ЕГН', showOriginalMessage: true},


        PERSONAL_USE_COLLECTIONS_EXIST: { code: '-33236', message: 'За избраното лично ползване има въведени обработки.  За да го изтриете/редактирате, трябва да анулирате вземането от подмодул Вземания'},
        PERSONAL_USE_PAYMENTS_EXIST: { code: '-33237', message: 'За избраното лично ползване има направени плащания.  За да го изтриете/редактирате, трябва да анулирате плащанията от менюто Транзакции.'},
        DEDUCTION_ONLY_CASH: { code: '-33238', message: 'Изплащането чрез приспадане е само за плащане в брой.'},
        WRONG_PERSONAL_USE_AREA: { code: '-33239', message: 'Площта за лично ползване трябва да бъде число по-голямо или равно на 0!'},

            //Subsidies related error codes [-33251 ÷ -33350]
        EXISTING_SOIL_SAMPLE_NUMBER: {code: '-33251', message: 'Вече съществува проба с такъв номер!'},
        EMPTY_REPORT_REQUESTED: {code: '-33252', message: 'Не може да се експортва празна справка!'},
        NO_ZPLOTS_FOUND: {code: '-33253', message: 'Не са открити земеделски парцели, които да бъдат редактирани!'},
            //Owner related error codes
        INVALID_OWNER_ID: {code: '-33301', message: 'Невалиден индентификатор на собственик'},
        OWNER_IS_NOT_HERITOR: {code: '-33302', message: 'Избраният собственик не е наследник на никого.'},
        CANNOT_ADD_HERITOR_TO_ALIVE_OWNER: {code: '-33303', message: 'Не може да добавите наследник на жив собственик!'},
        ILLEGAL_OWNER_EDIT: {code: '-33304', message: 'Не може да промените статусът на собственика, защото има въведени наследници!'},
        FILE_DOES_NOT_EXIST: {code: '-33305', message: 'Файлът не може да бъде намерен!'},
        REPRESENTING_LEGAL_ENTITITES: {code: '-33306', message: 'Представителят представлява юридически лица!'},
        WRONG_USERNAME_OR_PASSWORD: {code: '-33307', message: 'Грешен потребител или парола!'},
        CANNOT_MODIFY_NON_GROUP_USER: {code: '-33308', message: 'Не може да изтриете потребител, който не е от Вашата потребителска група!'},
        CANNOT_MODIFY_OWN_USER_DATA: {code: '-33309', message: 'Не може да редактирате текущият потребител!'},
        OWNER_ALREADY_EXISTS: {code: '-33310', message: 'Съществува собственик със съвпадащо ЕГН/ЕИК!'},
        ILLEGAL_REPRESENTATIVE_EDIT_IS_OWNER: {code: '-33311', message: 'Избрания представител е въведен и като Собственик. Редакцията на данни за собственици се извършва само от страница "Собственици".'},
        MODIFY_PERMISSIONS_DENIAL: {code: '-33312', message: 'Нямате права за редактиране на този потребител.'},
        ACCOUNT_IS_NOT_ACTIVE: {code: '-33313', message: 'Акаунта Ви е деактивиран. За ре-активация, моля свържете се с наш представител!'},
        USER_NOT_FOUND: {code: '-33314', message: 'Грешен потребител!'},
        USER_HAS_CONTRACTS: {code: '-33315', message: 'Собственикът, който желаете да изтриете участва в договори.'},

            //Cooperators related error codes
        INVALID_COOPERATOR_HERITOR_RELATION: {code: '-33551', message: 'Не съществува връзка между избраните кооператори наследник.'},

            //Map related error codes [-33051 ÷ -33150]
        MAP_UNEXPORTABLE_LAYER: {code: '-33051', message: 'Този слой не може да бъде експортван.'},
        MAP_EXPORT_ERROR: {code: '-33052', message: 'Проблем при генериране на файловете за експорт.'},
        MAP_REQUESTED_LAYER_NOT_FOUND: {code: '-33053', message: 'Търсеният слой не е намерен.'},
        MAP_LAYER_EDIT_FAILED: {code: '-33054', message: 'Грешка при редактиране на слой.'},
        MAP_EXISTING_NAME: {code: '-33055', message: 'Избраното име вече съществува.'},
        MAP_EMPTY_AREA: {code: '-33056', message: 'На избраната точка не съществува дефиниран земеделски парцел.'},
        LAYER_CLIPPING_UNSUCCESSFULL_OPERATION_CUT: {code: '-33057', message: 'Грешка при извършване на операция "Изрязване" при пресичане на имоти.'},
        LAYER_CLIPPING_UNSUCCESSFULL_OPERATION_SPLIT: {code: '-33058', message: 'Грешка при извършване на операция "Разцепване" при пресичане на имоти.'},
        LAYER_CLIPPING_UNSUCCESSFULL_OPERATION_DELETE: {code: '-33059', message: 'Грешка при извършване на операция "Отрязване" при пресичане на имоти.'},
        LAYER_CLIPPING_UNSUCCESSFULL_COPY_FOR_ISAK: {code: '-33060', message: 'Грешка при извършване на операция "Пресичане" със слой "За ИСАК".'},
        EMPTY_LAYER_TABLE: {code: '-33061', message: 'В избраният слой не съществуват данни.'},
        EMPTY_EXPORT_FILE: {code: '-33062', message: 'Не може да бъде експортван празен файл. Трябва да филтрирате/изберете данни от слой КВС.'},
        TOO_MANY_EXPORTED_PLOTS: {code: '-33063', message: 'Опитвате се да експортненте прекалено много имоти. Моля използвайте филтъра за да редуцирате броя им.'},
        SPLIT_TOOL_UNSUCCESSFUL_OPERATION_AUTOMATIC_SPLIT: {code: '-33064', message: 'Грешка при опит за автоматично разделяне на геометрия.'},
        SPLIT_TOOL_NO_SPLIT_PLOTS_FOUND: {code: '-33065', message: 'Не са намерени критерии за промяна по посочения от Вас имот.'},
        MAP_PRINT_ERROR: {code: '-33066', message: 'Възникна проблем в приготовлението на файла за принтиране.'},
        MAP_KVS_EDIT_PLOT: {code: '-33067', message: 'Полетата екате, масив и имот са задължителни.'},
        MAP_LAYER_CADASTRE_IDENT_ERROR: {code: '-33068', message: 'Полето Кадастрален идентификатор е задължително'},
        MAP_LAYER_CADASTRE_ERROR: {code: '-33069', message: 'Възникна грешка при търсенето'},
        MAP_LAYER_CADASTRE_IDENT_FORMAT_ERROR: {code: '-33070', message: 'Грешен формат на Кадастрален идентификатор'},
        LAYER_NOT_FOUND: {code: '-346059', message: 'Слоят не е намерен'}, //TODO: GPS-3104 Add translation in FE
        INVALID_LAYER_DEFINITIONS: {code: '-346060', message: 'Невалидни дефиниции на слой'}, //TODO: GPS-3104 Add translation in FE
        LAYER_NOT_COPYABLE: {code: '-346061', message: 'Копирането в слой от този тип не е позволено'}, //TODO: GPS-3104 Add translation in FE
        FAILED_TO_COPY_LAYER:  {code: '-346062', message: 'Грешка при копирането на слой'}, //TODO: Add translation in FE
        LAYER_NOT_DELETABLE: {code: '-346063', message: 'Изтриването на слой от този тип не е позволено'}, //TODO: GPS-3104 Add translation in FE
        LAYER_TYPE_NOT_SUPPORTED: {code: '-346064', message: 'Този тип слой не се поддържа'}, //TODO: GPS-3401 Add translation in FE



            //Diary related error codes [-33351 ÷ -33450]
        EXPENSE_ALREADY_EXISTS: {code:'-33351', message:'За избраните дейности, изпълнители и период има валиден разход. Моля изберете друг период.'},
        TRACK_NOT_CROSSING_GEOZONE: {code:'-33352', message:'Избраната следа не пресича никой парцел.'},
        DIARY_REPORT_CREATION_ERROR: {code:'-33353', message:'TF шаблона не може да бъде създаден.'},
        INVALID_OR_EXPIRED_TOKEN: {code:'-33354', message:'Невалиден или изтекъл токен.'},
        PLOT_WITHOUT_CROSSING_UNITS: {code:'-33355', message:'Избраната машина не е работила в даденият парцел.'},
        PLOT_NOT_FOUND: {code:'-33356', message:'Не може да бъде намерен избраният парцел.'},
        CANNOT_CREATE_GEOFENCE: {code:'-33357', message:'Грешка при създаването на Геозон.'},

        END_UPDATE_KVS_NOT_UPDATED_PLOT: {code:'-33250', message:'Имате имоти, участващи в договори, които очакват актуализация или имоти с невалидна геометрия. Сигурни ли сте че искате да приключите с актуализацията?'},

            //Sales contracts related error codes [-33551 ÷ -33650]
        NON_EXISTING_SALES_CONTRACT_NUMBER: {code: '-33551', message: 'Не съществува договор за продажба с такъв индентификатор'},
        NON_EXISTING_BUYER_ID: {code: '-33552', message: 'Не съществува купувач с такъв индентификатор'},
        CONTRACT_BUYER_RELATION_ALREADY_EXISTS: {code: '-33553', message: 'Вече съществува връзка между избраните купувач и договор.'},
        NON_EXISTING_CONTRACT_BUYER_RELATION: {code: '-33554', message: 'Не съществува връзка между избраните купувач и договор.'},

            //Contracts related error codes [-33651 ÷ -33750]
        INCORRECT_CONTRACTS_TYPE: {code: '-33651', message: 'Редакция не е възможна, защото филтрираните договори не са само от тип „Наем“, „Аренда“ или „Съвместна обработка“. Моля филтрирайте договори само от този тип и стартирайте инструмента отново!'},
        NO_CONTRACTS_FOR_MULTIEDIT: {code: '-33652', message: 'Моля задайте договор/и за мултиредакция.'},
        NO_MULTIEDIT_VALUES: {code: '-33653', message: 'Моля задайте стойности за мултиредакция.'},
        NO_CONTRACTS_FOR_MULTICOPY: {code: '-33654', message: 'Няма договори, които да отгоравят на критериите за копиране на договори.'},
        NO_SELECTED_CONTRACT: {code: '-33655', message: 'Моля задайте договор.'},
        CONTRACT_PLOT_DUPLICATION: {code: '-33656', message: 'Вече съществува договор с активни имоти за периодa за който се опитвате да го създадете.'},
        NO_CONTRACT_PERMISSION: {code: '-33657', message: 'Нямате права за редакция на договори в избраното стопанство'},
        CONTRACTS_HAS_PAYMENTS_EXCEPTION: {code: '-33658', message: 'Договорът не може да бъде редактиран, защото има въведени плащания.'},
        CONTRACTS_HAS_SOLD_PLOTS_EXCEPTION: {code: '-33659', message: 'Договорът не може да бъде редактиран, защото има продадени имоти.'},

            //Subleases related error codes [-33751 ÷ -33850]
        CANNOT_ADD_MORE_FARMING_CONTRAGENTS: {code: '-33751', message: 'Не може да добавяте повече от едно стопанство като контрагент'},
        CANNOT_MODIFY_FROM_SUBLEASE_CONTRACT: {code: '-33752', message: 'Не може да редактирате договор, създаден през подмодул "Преотдадени"'},
        EMPTY_MOL: {code: '-33753', message: 'Не може да добавяте стопанство без МОЛ. Моля редактирайте стопанството от менюто Настройки->Стопанства или изберете представител от таблицата с представители.'},
        CONFIRM_DELETING_CONTRACT_WITH_SUBLEASED_PLOTS: {code: '-33754', message: 'Довоговът, който искате да изтриете съдържа имоти, които са преотдадени.', showOriginalMessage: true},
        CANNOT_MODIFY_FROM_SUBLEASE_CONTRACT_DATES: {code: '-33755', message: 'Не може да редактирате дата на влизане в сила и крайна дата на преотдаден договор.'},
        CANNOT_MODIFY_CONTRACT_DATES: {code: '-33756', message: 'Не може да редактирате дата на влизане в сила и крайна дата на договор.'},
        NO_SUBLEASE_PLOTS_FOUND: {code: '-33757', message: 'Не са намерени имоти в избрания договор'},
        COPY_SUBLEASE_ERROR: {code: '-33758', message: 'Възникна грешка при копиране на преотдаден договор'},
        CHANGE_FARMING_SUBLEASE_ERROR: {code: '-33759', message: 'Довоговът, на който искате да смените стопанството съдържа имоти, които са преотдадени.'},
        CANNOT_EDIT_CONTRACT_WITH_CHARGED_RENT: {code: '-33760', message: 'Действието не може да бъде изпълнено, защото има начислена рента.'},
    
        //Users related error codes [-33851 ÷ -33950]
        CANNOT_LOGIN_AS_NON_SUB_ACCOUT: {code: '-33851', message: 'Не може да влезете като потребител, който не е подакаунт на текущия потребител.'},
        MUST_BE_LOGGED_ON: {code: '-33852', message: 'Възникна грешка при изпълнение на действието. Опитайте да влезете отново в акаунта си и ако продължите да виждате грешки, моля свържете се с нас.'},
        POLICY_FORBIDDEN_LOGIN_AS: {code: '-33853', message: 'Не може да влезете в избрания потребител.'},        

        //Warehouse Exceptions
        FIELD_ALREADY_EXISTS: {code: '-33011', message: 'Не сте попълнили всички задължителни полета.', showOriginalMessage: true},
        WAREHOUSE_ITEMS_NOT_FOUND: {code: '-33012', message: 'Няма намерени артикули.'},
        WAREHOUSE_NOT_CREDIT_ITEMS_FOUND: {code: '-33013', message: 'Избрали сте артикул от склада, но такъв не е изписан за съответа дата.'},
        WAREHOUSE_ITEM_QUANTITY_NOT_ENOUGH: {code: '-33014', message: 'Изписаното количество е вече изразходвано от предишни операции'},
        WAREHOUSE_COMPANY_HAS_TRANSACTIONS: {code: '-33015', message: 'Не може да изтриете компания, за която има направена транзакция'},
        WAREHOUSE_HAS_CHILDREN: {code: '-33017', message: 'Не може да изтриете склад, който има подскладове'},
        WAREHOUSE_HAS_ARTICLES: {code: '-33018', message: 'Не може да изтриете склад, който в който веднъж са заприходени артикули'},
        NOT_ENOUGH_QUANTITY: {code: '-33019', message: 'Наличното количество не е достатъчно, за да бъде изпълнена операцията.', showOriginalMessage: true},
        DELETE_PARENT_GROUP: {code: '-33020', message: 'Не може да изтриете група, която има подгрупи.'},
        DOCUMENT_NUMBER_EXISTS: {code: '-33021', message: 'Номерът на документа вече съществува.'},
        
        //Export Exceptions [-33951 ÷ -34050]
        REQUEST_ENTITY_TOO_LARGE: {code: '-33951', message: 'Опитвате се да експортнете твърде много данни'},
        GENERIC_EXPORT_ERROR: {code: '-33952', message: 'Възникна проблем при опит за експорт'},
        EXPORT_STRUCTURE_ERROR: {code: '-33953', message: 'Невалидна структура за екпорт файл'},


        //Authentication Exceptions  [-34050 ÷ -34150]
        INVALID_AUTH_STATE: {code: '-34051', message: 'Невалидно състояние на автентикация.'},
        FAILED_TO_GET_ACCESS_TOKEN:  {code: '-34052', message: 'Неуспешен опит за взимане на access token'},
        FAILED_TO_GET_RESOURCE_OWNER:  {code: '-34053', message: 'Неуспешен опит за достъпване на собвеник на ресурс'},
        FAILED_BACKCHANELL_LOGOUT:  {code: '-34054', message: 'Неуспешен логaут чрез провайдъра на автентикация'},
        REFRESH_TOKEN_ERROR:  {code: '-34055', message: 'Неуспешно генериране на access token чрез refresh token'},
        ACCESS_TOKEN_ERROR:  {code: '-34056', message: 'Невалиден token'},
        FAILED_TO_EXCHANGE_ACCESS_TOKEN:  {code: '-34057', message: 'Неуспешен опит за обмен на access token'},


        //Slope Exceptions:
        AVG_SLOPE_CALCULATION_ERROR:  {code: '-34057', message: 'Грешка при изчисляване на среден наклон'},
        AVG_SLOPE_ONLY_WORK_LAYER_ERROR:  {code: '-34058', message: 'Средният наклон може да бъде изчислен само за работен слой'},


        //Plot area exceptions
        CONTRACTS_AVAILABLE_PLOT_AREA_EXCEPTION: {code: '-34059', message: 'С това действие ще надхвърлите допустимата площ по документ.'},
        CONTRACT_PERIOD_OVERLAP_WITH_ANNEX: {code: '-34060', message: 'Поради наличието на вече съществуващ анекс, основният договор не може да бъде редактиран. Моля, отразете промените, чрез редактиране/създаване на анекс, за желаният от Вас период.'},
        PLOT_RENT_TYPE_GENERIC_NOT_ALLOWED: {code: '-34061', message: 'Не може да изтривате рента от тип "Стандартен"'},
        PLOT_RENT_TYPE_HAS_CHARGED_RENT: {code: '-34062', message: 'Не може да изтриете типа рента. По даденият критерии вече е начислена рента.'},
        PLOT_RENT_TYPE_NOT_ENOUGH_AREA: {code: '-34063', message: 'Въвели сте площ по-голяма от наличната за рента.'},
        PLOT_RENT_TYPE_NO_VALUE: {code: '-34064', message: 'Не сте въвели стойност на избрания тип рента.'},
        PLOT_RENT_TYPE_NO_TYPE: {code: '-34065', message: 'Не сте въвели стойност за тип рента.'},
        PLOT_RENT_TYPE_DUPLICATED_VALUE: {code: '-34066', message: 'Не може да дублирате стойности от един и същи тип рента.'},
        PLOT_RENT_TYPE_PLOT_RENTS_AREA_BIGGER_THAN_RENT_AREA: {code: '-34067', message: 'За избрания имот има разпределена площ по ренти по-голяма от желаната.'},
        
        BIGGER_RENT_TYPE_AREAS_THAN_AREA_FOR_RENT: {code: '-34068', message: 'Въведената площ за специфична рента е по-голяма от общата площ за рента на имота.'},
        SMALLER_RENT_TYPE_AREAS_THAN_AREA_FOR_RENT: {code: '-34069', message: 'Трябва да разпределите цялата площ за рента на имота по специфичните ренти'},
        DELETE_USED_RENT_TYPE: {code: '-34070', message: 'Специфичната рента не може да бъде изтрита, защото е добавена в имот(и).', showOriginalMessage: true},
        CANNOT_HAVE_BOTH_INDIVIDUAL_AND_SPECIFIC_RENT_FOR_SAME_PLOT: {code: '-34071', message: 'Не може да имате Индивидуална и Специфична рента за един и същи имот.'},
    
        // Sales contracts exceptions
        CONTRACT_TYPE_MODIFY_DENIED_SALES_RELATION: {code: '-34100', message: 'Не може да променяте договори от тип Собственост, защото има връзка с договор за продажба.'},
    
        //Payments exceptions
        CANNOT_REMOVE_HERRITOR_WITH_PAYMENTS: {code: '-34151', message: 'Не може да премахнете наследника, защото има въведени плащания.'},
    }
}));
