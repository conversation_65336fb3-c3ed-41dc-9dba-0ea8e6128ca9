function initSearchComboboxFields(isAnnex = false) {

    var contractTypeComboboxData    = ComboboxData.ContractTypeCombobox,
        newContractTypeComboboxData = [],
        farmingComboboxData         = ComboboxData.FarmingCombobox,
        contractStatusComboboxData  = ComboboxData.ContractStatusCombobox,
        ekateComboboxData           = ComboboxData.EkateCombobox,
        categoryComboboxData        = ComboboxData.PlotCategoryCombobox,
        newCategoryComboboxData     = [],
        plotNTPComboboxData         = ComboboxData.PlotNTPCombobox,
        newPlotNTPComboboxData      = [],
        farmingYearComboboxData     = ComboboxData.FarmingYearCombobox,
        irrigatedAreaComboboxData   = ComboboxData.IrrigatedAreaCombobox,
        userFarmingPermissions      = ComboboxData.UserFarmingPermissions;
        userFarmingPermissions      = ComboboxData.UserFarmingPermissions;
        contractGroupsComboboxData        = ComboboxData.ContractGroupsCombobox;

    contractTypeComboboxData.forEach(function (el) {
        if (el.id !== '') {
            newContractTypeComboboxData.push(el);
        }
    });
    jQuery('#search-contract-type').combobox({
        data: newContractTypeComboboxData,
        editable: false,
        valueField: 'id',
        textField: 'name',
        multiple: true,
        onHidePanel: onHidePanelMultiSelect,
        onSelect: onComboMultiSelect,
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    jQuery('#search-contract-group').combobox({
        data: contractGroupsComboboxData,
        editable: true,
        valueField: 'id',
        textField: 'name',
        multiple: true,
        onHidePanel: onHidePanelMultiSelect,
        onSelect: onComboMultiSelect,
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    jQuery('#search-contract-plot-ekate > input').combobox({
        data:ekateComboboxData,
        valueField: 'ekate',
        textField: 'text',
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
    jQuery('#search-contract-plot-category > input').combobox({
        data: categoryComboboxData,
        valueField: 'category',
        valueField: 'id',
        textField: 'name',
        multiple: true,
        onHidePanel: onHidePanelMultiSelect,
        onSelect: onComboMultiSelect,
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
    
    farmingComboboxData.forEach(function (el) {
        if (el.id !== "") {
            el.selected = false;
            let hasPermission = Object.values(userFarmingPermissions).includes(el.id);
            if(!hasPermission) {   
                //disable all farmings with no permissions
                el.disabled = true;
            } else {
                //auto filter farmings with permissions
                el.selected = true;               
            }
        }
    });

    jQuery('#search-farming').combobox({
        data: farmingComboboxData,
        editable: false,
        valueField: 'id',
        textField: 'name',
        multiple: true,
        onHidePanel: onHidePanelMultiSelect,
        onSelect: onComboMultiSelect,
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    jQuery('#search-contract-status').combobox({
        data: contractStatusComboboxData,
        editable: false,
        valueField: 'id',
        textField: 'name',
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    jQuery('#search-ekatte').combobox({
        data: ekateComboboxData,
        valueField: 'ekate',
        textField: 'text',
        multiple: true,
        onSelect: onComboMultiSelect,
        onHidePanel: onHidePanelMultiSelect,
        onLoadSuccess: function () {
            jQuery('#search-plot-ekate > input').combobox({
                data: jQuery('#search-ekatte').combobox('getData'),
                valueField: 'ekate',
                textField: 'text',
                filter: function (q, row) {
                    var opts = jQuery(this).combobox('options'),
                        text = row[opts.textField].toLowerCase(),
                        value = row[opts.valueField],
                        find = q.toLowerCase();
                    if(text.indexOf(find) != -1 || value.indexOf(find) != -1)
                    {
                        return true;
                    }
                }
            });
        },
        filter: function (q, row) {
            var opts = jQuery(this).combobox('options'),
                text = row[opts.textField].toLowerCase(),
                value = row[opts.valueField],
                find = q.toLowerCase();
            if(text.indexOf(find) != -1 || value.indexOf(find) != -1)
            {
                return true;
            }
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    categoryComboboxData.forEach(function (el) {
        if (el.id !== '') {
            newCategoryComboboxData.push(el);
        }
    });

    jQuery('#edit-category-contracts').combobox({
        data: newCategoryComboboxData,
        editable: false,
        valueField: 'id',
        textField: 'name',
        multiple: false,
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    plotNTPComboboxData.forEach(function (el) {
        if (el.id !== '') {
            newPlotNTPComboboxData.push(el);
        }
    });
    jQuery('#edit-ntp-contracts').combobox({
        data: newPlotNTPComboboxData,
        editable: false,
        valueField: 'id',
        textField: 'name',
        multiple: false,
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    var farmingYearComboboxDataCopy = farmingYearComboboxData.map(function (el) {
        var newEl = Object.assign({}, el);
        newEl.selected = false;
        return newEl;
    });
    farmingYearComboboxDataCopy[0].selected = true;

    jQuery('#search-farming-year').combobox({
        data: farmingYearComboboxDataCopy,
        valueField: 'id',
        textField: 'farming_year',
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    jQuery('#search-date-from').datebox({});
    jQuery('#search-date-to').datebox({});
    jQuery('#search-due-date-from').datebox({});
    jQuery('#search-due-date-to').datebox({});

    jQuery('#sr-type-combobox').combobox({
        data: ComboboxData.RentaTypesOptionsCombobox,
        valueField: 'value',
        textField: 'title',
        onSelect: function (rec) {
            jQuery('#sr-value-combobox').combobox('clear');
            if (rec.value == 'category') {
                let categoies = [];
                ComboboxData.PlotCategoryCombobox.forEach(function (el) {
                    if (el.id != '' && el.id != '-1') {
                        categoies.push(el);
                    }
                });
                jQuery('#sr-value-combobox').combobox('loadData', categoies);
            } else if (rec.value == 'arable') {
                jQuery('#sr-value-combobox').combobox('loadData', [
                    {id: 'true', name: 'Да'},
                    {id: 'false', name: 'Не'},
                ]);
            } else if (rec.value == 'ntp') {
                let ntps = [];
                ComboboxData.PlotNTPCombobox.forEach(function (el) {
                    if (el.id != '' && el.id != '-1') {
                        let ntp = {};
                        ntp.id = null; 
                        ntp.name = null; 
                        
                        ntp.id = el.id;
                        ntp.name = el.id + ': ' + el.name;
                        ntps.push(ntp);
                    }
                });
                jQuery('#sr-value-combobox').combobox('loadData', ntps);
            } else {
                jQuery('#sr-value-combobox').combobox('loadData', []);
            }
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    jQuery('#sr-value-combobox').combobox({
        data: [],
        editable: true,
        valueField: 'id',
        textField: 'name',
        multiple: false,
        onChange : function (newValue,oldValue) {
            jQuery('#sr-type-combobox').combobox('getValue');
            let selectedType = jQuery('#sr-type-combobox').combobox('getValue');

            //check if rentTypeInfo.type  and rentTypeInfo.value are already in rentaTypes
            if(!isEditPlotRent && rentaTypes != null && rentaTypes.length > 0) {
                if (rentaTypes.some(function(rentType) {
                    if (rentType.type == selectedType && rentType.value == newValue) {
                        jQuery('#sr-value-combobox').combobox('select', oldValue);
                        jQuery.messager.alert('Грешка', 'Вече съществува рента с такъв тип и стойност!', 'warning');
                        return true;
                    }
                })) {
                    return false;
                }
            }
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    jQuery('#search-category').combobox({
        data: categoryComboboxData,
        valueField: 'id',
        textField: 'name',
        multiple: true,
        onHidePanel: onHidePanelMultiSelect,
        onSelect: onComboMultiSelect,
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    jQuery('#search-area-type').combobox({
        data: plotNTPComboboxData,
        editable: false,
        valueField: 'id',
        textField: 'name',
        multiple: true,
        onHidePanel: onHidePanelMultiSelect,
        onSelect: onComboMultiSelect,
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    jQuery('#search-renta-types').combobox({
        url: 'index.php?common-rpc=renta-types-combobox',
        rpcParams: [{
            as_list: true
        }],
        valueField: 'id',
        textField: 'name',
        multiple: true,
        onHidePanel: onHidePanelMultiSelect,
        onSelect: onComboMultiSelect,
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    irrigatedAreaComboboxData[0].selected = true;
    jQuery('#search-irrigated-area').combobox({
        data: irrigatedAreaComboboxData,
        editable: false,
        valueField: 'value',
        textField: 'label',
        multiple: false,
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    jQuery('#rent-place > input').combobox({
        data: ComboboxData.EkateCombobox,
        valueField: 'ekate',
        textField: 'text',
        filter: function(q, row){
            var opts = jQuery(this).combobox('options');
            var text = row[opts.textField].toLowerCase();
            var value = row[opts.valueField];
            var find = q.toLowerCase();
            if(text.indexOf(find) != -1 || value.indexOf(find) != -1)
            {
                return true;
            }
        },
        onLoadSuccess: function (data) {
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    jQuery('#search-owner-type').combobox({
        data: [
            {
                id: '',
                title: 'Всички',
                selected: true
            }, {
                id: '0',
                title: 'Юридически лица',
            }, {
                id: '1',
                title: 'Физически лица',
            },
        ],
        valueField: 'id',
        textField: 'title',
        editable: false,
        multiple: false,
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

function clearContractsFilter(callable) {
    jQuery('#search-cnum').val('');
    jQuery('#search-na-num').val('');
    jQuery('#search-ao_c_num').val('');
    jQuery('#search-cnum-complete-match').prop('checked', true);
    jQuery('#search-na-num-complete-match').prop('checked', true);
    jQuery('#search-contract-type').combobox('reset');
    jQuery('#search-contract-group').combobox('reset');
    jQuery('#search-contract-status').combobox('reset');
    jQuery('#search-farming').combobox('reset');
    jQuery('#search-farming-year').combobox('reset');
    jQuery('#search-renta-types').combobox('clear');
    jQuery('#search-date-from').datebox('reset');
    jQuery('#search-date-to').datebox('reset');
    jQuery('#search-due-date-from').datebox('reset');
    jQuery('#search-due-date-to').datebox('reset');
    jQuery('#search-all-contracts input').prop('checked', true);
    jQuery('#search-kad-ident').val('');
    jQuery('#search-ekatte').combobox('reset');
    jQuery('#search-masiv').val('');
    jQuery('#search-number').val('');
    jQuery('#search-category').combobox('reset');
    jQuery('#search-block').val('');
    jQuery('#search-area-type').combobox('reset');
    jQuery('#search-irrigated-area').combobox('loadRpc');
    jQuery('#search-owner-name').val('');
    jQuery('#search-owner-egn').val('');
    jQuery('#search-owner-type').combobox('reset');
    jQuery('#search-represent-name').val('');
    jQuery('#search-represent-egn').val('');
    jQuery('#search-heritor-name').val('');
    jQuery('#search-heritor-egn').val('');
    jQuery('#search-company-name').val('');
    jQuery('#search-company-eik').val('');
    jQuery('#search-person-name').val('');
    jQuery('#search-person-egn').val('');
    jQuery('#contract-note').val('');
    jQuery('#search-all-contracts-rent-per-plot input').prop('checked', true);
    jQuery('#ownerless-contracts').prop('checked', false);
    jQuery('#search-is-closed-for-editing').prop('checked', false);
    jQuery('#search-owner-note').val('');
    jQuery('#search-owner-phone').val('');
    jQuery('#sr-type-combobox').combobox('reset');
    jQuery('#sr-value-combobox').combobox('reset');

    //re-init tree with query params

    callable(1, {});

    //@TODO
    jQuery('#contracts-tree-pagination').pagination('select', 1);
    jQuery('#win-filter-contracts').window('close');

    return false;
}

function getFilterContractsTreeParams(isClosedForEditing) {
    var obj = {};

    obj.c_num = jQuery('#search-cnum').val();
    obj.c_folder = jQuery('#search-cfolder').val();
    obj.c_num_complete_match = jQuery('#search-cnum-complete-match').is(':checked');
    obj.c_type = jQuery('#search-contract-type').combobox('getValues');
    obj.c_status = jQuery('#search-contract-status').combobox('getValue');
    obj.farming = jQuery('#search-farming').combobox('getValues');
    obj.farming_year = jQuery('#search-farming-year').combobox('getValue');
    obj.renta_types = jQuery('#search-renta-types').combobox('getValues');
    obj.date_from = jQuery('#search-date-from').datebox('getValue');
    obj.date_to = jQuery('#search-date-to').datebox('getValue');
    obj.na_num = jQuery('#search-na-num').val();
    obj.na_num_complete_match = jQuery('#search-na-num-complete-match').is(':checked');

    if(jQuery('#search-contract-group').combobox('getValues') != '') {
        obj.c_group = jQuery('#search-contract-group').combobox('getValues');
    }

    if(jQuery('#search-ao_c_num').val().length > 0) {
        obj.ao_c_num = jQuery('#search-ao_c_num').val();
    }

    if (jQuery('#ownerless-contracts').is(':checked'))
    {
        obj.ownerless_contracts = true;
    }

    if (jQuery('#search-contracts-with-nat input').is(':checked'))
    {
        obj.with_renta_nat = true;
    }

    if (jQuery('#search-contracts-without-nat input').is(':checked'))
    {
        obj.with_renta_nat = false;
    }

    if (jQuery('#search-is-closed-for-editing').is(':checked')) {
        obj.is_closed_for_editing = true;
    }

    if (typeof isClosedForEditing !== "undefined") {
        obj.is_closed_for_editing = isClosedForEditing;
    } 

    obj.filter_rent_per_plot = !!jQuery('#search-contracts-with-rent-per-plot input').is(':checked');

    obj.kad_ident = jQuery('#search-kad-ident').val();
    obj.ekate = jQuery('#search-ekatte').combobox('getValues');
    obj.masiv = jQuery('#search-masiv').val();
    obj.number = jQuery('#search-number').val();
	obj.block = jQuery('#search-block').val();
    obj.category = jQuery('#search-category').combobox('getValues');
    obj.ntp = jQuery('#search-area-type').combobox('getValues');
    obj.irrigated_area = jQuery('#search-irrigated-area').combobox('getValue');
    obj.owner_name = jQuery('#search-owner-name').val();
    obj.owner_egn = jQuery('#search-owner-egn').val();
    obj.owner_type = jQuery('#search-owner-type').combobox('getValue');
    obj.rep_name = jQuery('#search-represent-name').val();
    obj.rep_egn = jQuery('#search-represent-egn').val();
    obj.heritor_name = jQuery('#search-heritor-name').val();
    obj.heritor_egn = jQuery('#search-heritor-egn').val();
    obj.company_name = jQuery('#search-company-name').val();
    obj.company_eik = jQuery('#search-company-eik').val();
    obj.person_name = jQuery('#search-person-name').val();
    obj.person_egn = jQuery('#search-person-egn').val();
    obj.contract_note = jQuery('#contract-note').val();
    
    obj.owner_note = jQuery('#search-owner-note').val();
    if(obj.owner_note.length > 0 && obj.owner_note.length < 3) {
		jQuery.messager.alert('Грешка', 'Моля въведете поне 3 символа за търсене в полето "Забележка"', 'warning');
		return;
	}
    
    obj.owner_phone = jQuery('#search-owner-phone').val();
    if(obj.owner_phone.length > 0 && obj.owner_phone.length < 5) {
		jQuery.messager.alert('Грешка', 'Моля въведете поне 5 символа за търсене в полето "Телефон/Мобилен"', 'warning');
		return;
	}

    if(obj.contract_note.length > 0 && obj.contract_note.length < 3) {
        jQuery.messager.alert('Грешка', 'Полето Забележка трябва да с минимум 3 символа.');
        return false;
    }
    
    obj.rentaType = jQuery('#sr-type-combobox').combobox('getValue');
    obj.rentaValue = jQuery('#sr-value-combobox').combobox('getValue');

    return obj;
}


