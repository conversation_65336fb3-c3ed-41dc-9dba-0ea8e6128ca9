var root_id;
var tmpPath;
var reloadTarget;
var addingOwnerHeritor = false;

function initOwnersHeritorsTree(owner_id) {
	root_id = owner_id;
	tmpPath = owner_id;

	jQuery.extend(jQuery.fn.tree.methods,{
		unselect:function(jq,target){
			return jq.each(function(){
				var opts = jQuery(this).tree('options');
				jQuery(target).removeClass('tree-node-selected');
				if (opts.onUnselect){
					opts.onUnselect.call(this, jQuery(this).tree('getNode',target));
				}
			});
		}
	});

	jQuery('#owners-heritors-tree').tree({
		url: 'index.php?owners-rpc=owners-heritors-tree',
		animate: true,
		lines: true,
		sort: 'id',
		order: 'desc',
		page: 1,
		onBeforeLoad: function(node, param) {
			//sending filter parameters with POST method
			param.path = tmpPath + '.*{1}';
		},
		onBeforeExpand: function(node) {
			tmpPath = node['attributes'].path;
		},
		onLoadSuccess: function() {
		},
		onBeforeSelect: function (row, index) {
			var currentSelection = jQuery(this).tree('getSelected');

			if (currentSelection) {
				if (currentSelection.attributes.id == row.attributes.id) {
					jQuery(this).tree('unselect', row.target);
					return false;
				}
			}
		},
		loader: function (filterParams, succ, error) {
			var options = jQuery(this).tree('options');
			var rpcParams = options.rpcParams || [];

			rpcParams.push(filterParams);
			rpcParams.push(options.page);
			rpcParams.push(options.sort);
			rpcParams.push(options.order);

			var params = JSON.stringify({
				"method": "read",
				"params": rpcParams,
				"id": 1,
				"jsonrpc": "2.0"
			});
			var url = options.url;
			jQuery.ajax({
				url: url,
				data: params,
				accepts: 'application/json',
				contentType: 'application/json',
				method: 'post',
				dataType: 'json',
				processData: false
			})
			.done(succ);
		},
		loadFilter: function (data) {
			return data.result || [];
		}
	});
}

function addOwnerHeritor() {
	if (!hasPlotRightsRW) {
		messagerPlotsWriteRights();
		return;
	}
	var getSelected = jQuery('#owners-tree').tree('getSelected');
	var selectedHeritor = jQuery('#owners-heritors-tree').tree('getSelected');
	if(getSelected)
	{
		if(!getSelected['attributes'].is_dead)
		{
			jQuery.messager.alert('Грешка', 'Не може да добавите наследници на жив човек.');
		}
		else if(selectedHeritor && !selectedHeritor['attributes'].is_dead)
		{
			jQuery.messager.alert('Грешка', 'Не може да добавите наследници на жив човек.');
		}
		else
		{
			if (selectedHeritor) {
				ownerID = selectedHeritor.attributes.owner_id;
			} else {
				ownerID = getSelected.attributes.id;
			}
			jQuery('#search-heritor-by-egn').val('');
			lastAddedParent = ownerID;

			jQuery('#choose-heritor').combobox({
				url: 'index.php?owners-rpc=owners-heritors-combobox',
				valueField: 'id',
				textField: 'owner_names',
				required: true,
				missingMessage: 'Моля изберете наследник.',
				rpcParams: [ownerID, null, null],
				filter: function(q, row){
					var opts = jQuery(this).combobox('options');
					var text = row[opts.textField].toLowerCase();
					var find = q.toLowerCase();
					if(text.indexOf(find) != -1)
					{
						return true;
					}
				},
				loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        		loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter,
			});

			jQuery('#win-choose-heritor').window('open');
		}
	}
	else
	{
		jQuery.messager.alert('Грешка', 'Моля изберете собственик.');
	}
}

function deleteOwnerHeritor() {
	if (!hasPlotRightsRW) {
		messagerPlotsWriteRights();
		return;
	}
	var obj = new Object;

	var getSelected = jQuery('#owners-heritors-tree').tree('getSelected');
	if(getSelected)
	{
		jQuery.messager.confirm('Потвърждение', 'Сигурни ли сте, че искате да изтриете този наследник?', function(r) {
			if (r) {
				var parent = jQuery('#owners-heritors-tree').tree('getParent', getSelected.target);
				if(parent)
				{
					obj.path = parent['attributes'].owner_id + '.' + getSelected['attributes'].owner_id;
				}
				else
				{
					obj.path = root_id + '.' + getSelected['attributes'].owner_id;
				}

				if(parent)
				{
					tmpPath = parent['attributes'].path;
					reloadTarget = parent;
				}
				else
				{
					tmpPath = root_id;
					reloadTarget = undefined;
				}

				var selectedParent = jQuery('#owners-tree').tree('getSelected');
				TF.Rpc.Owners.OwnersHeritorsTree.deleteOwnerHeritor(obj.path)
			    .done(function (dataObj) {
				    jQuery('#owners-tree').tree('select',selectedParent.target);
			    })
			    .fail(function (errorObj) {
					if (errorObj.is(TF.Rpc.ExceptionsList.CANNOT_REMOVE_HERRITOR_WITH_PAYMENTS)) {
						jQuery.messager.alert('Грешка',errorObj.getMessage(),'warning');
					}
			    });

			}
		});
	}
	else
	{
		jQuery.messager.alert('Грешка', 'Моля изберете наследник.');
	}
}

function showOwnerHeritorInfo() {
	var getSelected = jQuery('#owners-heritors-tree').tree('getSelected');
	if(getSelected)
	{
		window.open("index.php?page=Owners.Home&owner_id=" + getSelected['attributes'].owner_id, '_blank');
	}
	else
	{
		jQuery.messager.alert('Грешка', 'Моля изберете наследник.');
	}
}

jQuery(function() {
	jQuery('#btn-save-owner-heritor').bind('click', function() {
		var obj = new Object;
		obj.parent = root_id;

		var getSelected = jQuery('#owners-heritors-tree').tree('getSelected');
		if(getSelected)
		{
			obj.parent = getSelected['attributes'].owner_id;
		}

		obj.parent = lastAddedParent ? lastAddedParent : obj.parent;
		var owner_id = jQuery('#choose-heritor').combobox('getValue');

		if(!owner_id)
		{
			jQuery.messager.alert('Грешка', 'Моля изберете наследник.');
			return false;
		}

		if(getSelected)
		{
			tmpPath = getSelected['attributes'].path;
			reloadTarget = getSelected;
		}
		else
		{
			tmpPath = root_id;
			reloadTarget = undefined;
		}

		var selectedParent = jQuery('#owners-tree').tree('getSelected');
		TF.Rpc.Owners.OwnersHeritorsTree.addOwnerHeritor(owner_id, obj.parent)
	    .done(function (dataObj) {
		    jQuery('#owners-tree').tree('select', selectedParent.target);
	    })
	    .fail(function (errorObj) {
	    	jQuery.messager.alert('Грешка',errorObj.getMessage(),'warning');
	    });

	    jQuery('#win-choose-heritor').window('close');
		lastAddedParent = 0;
		lastAddedOwner  = 0;
	});

	jQuery('#btn-search-owner-heritor').bind('click', function() {
		var egn = jQuery('#search-heritor-by-egn').val();

		jQuery('#choose-heritor').combobox({
			url: 'index.php?owners-rpc=owners-heritors-combobox',
			valueField: 'id',
			textField: 'owner_names',
			required: true,
			missingMessage: 'Моля изберете наследник.',
			rpcParams: [root_id, egn, true],
			filter: function(q, row){
				var opts = jQuery(this).combobox('options');
				var text = row[opts.textField].toLowerCase();
				var find = q.toLowerCase();
				if(text.indexOf(find) != -1)
				{
					return true;
				}
			},
			loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
			loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter,
		});
	});

	jQuery('#btn-add-new-owner-heritor').bind('click', function() {

		jQuery('#win-plot-owner-add').window('open');
		addingOwnerHeritor = true;
		onAddownerPanelOpen();
		setAddEditFieldsValidators();
		clearAddOwnerInputDataFields();

	});
});

function reloadHeritorTree()
{
	if(reloadTarget)
	{
		jQuery('#owners-heritors-tree').tree('reload', reloadTarget.target);
	}
	else
	{
		jQuery('#owners-heritors-tree').tree('reload');
	}
}
