var editContractID;
var pageNumber = 1;

function initContractsTree(page, filterObj) {
	pageNumber = page ?? 1

	var contractsTree = jQuery('#contracts-tree');
	var isTreeBound = contractsTree.data().hasOwnProperty('tree');

	var params = filterObj ?? {};

	if (params.contract_id) {
		params.contract_ids = params.contract_id;
		delete params.contract_id;
	}

	if (isTreeBound) {
		contractsTree.tree({
			rpcParams: [params],
			page: pageNumber
		});
		return;
	}

	contractsTree.tree({
		url: 'index.php?payments-rpc=payments-contracts-tree',
		animate: false,
		lines: true,
		sort: 'c.id',
		order: 'desc',
		page: pageNumber,
		rows: 30,
		rpcParams: [params],
		onSelect: function(node) {
			//fill annex info
			fillContractInfo(node.attributes);
			jQuery('#contract-payments-tables').treegrid('clearSelections');
			initContractPaymentsGrid(node.id, node.attributes.annex_id, node.attributes.year_id);
			initPersonalUseGrid(node.id, node.attributes.annex_id, node.attributes.year_id);
		},
		onLoadSuccess: function() {
			var roots = jQuery(this).tree('getRoots');
			var total = 0;
			var limit = 30;
			if (roots.length) {
				var getSelected = jQuery(this).tree('getSelected');
				if(!getSelected) {
					if (editContractID != undefined) {
						var node = jQuery(this).tree('find', editContractID);
						jQuery(this).tree('select', node.target);
					}
					else {
						jQuery(this).tree('select', roots[0].target);
					}
				}
				total = roots[0]['attributes']['pagination']['total'];
				limit = roots[0]['attributes']['pagination']['limit'];
			}
            else
            {
                //fill annex info
                fillContractInfo();
                initContractPaymentsGrid(0, 0, jQuery('#search-year').combobox('getValue'));
                initPersonalUseGrid(0,0,0);
				const currentFarmingYearObj =  getFarmingYearById(currentFarmingYear);

				if (currentFarmingYearObj) {
					currentFarmingYearText = +currentFarmingYearObj.year - 1 + '/' + currentFarmingYearObj.year;
                	jQuery.messager.alert('Внимание', 'Не са открити записи за стопанска година ' + currentFarmingYearText + ' г.', 'warning');
				} else {
					jQuery.messager.alert('Внимание', 'Не са открити записи', 'warning');
				}


            }
			//init pagination with total contract elements
			initContractsPagination(total, limit, pageNumber);

			if (currentFarmingYear != undefined && currentFarmingYear != jQuery('#search-year').combobox('getValue') ) {
			    jQuery('#search-year').combobox('setValue',currentFarmingYear);
			}
		},
		loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
		loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
	});

    jQuery('.js-charged-multirent-row').remove();

	var rentaRows = jQuery('#js-charged-multirent-table').find('.js-charged-multirent-row').length;
	var comboHTML ='<tr class="js-charged-multirent-row"><td style="padding:0px 0px 10px 15px"><select id="js-charged-renta-type-cb-'+(rentaRows)+'"></select></td>';
	comboHTML += '<td style="padding:0px 0px 10px 10px"><input class="js-charged-renta-value" id="charged-renta-value-'+(rentaRows)+'"></td>';
	comboHTML += '<td style="padding:0px 0px 10px 10px"><input class="js-charged-unit-price" id="charged-unit-price-'+(rentaRows)+'"></td>';
	comboHTML += '<td style="padding:0px 0px 10px 10px"><input type="checkbox" class="js-renta-nat-is-converted" id="renta-nat-is-converted-'+(rentaRows)+'"></td>';

	jQuery('#js-charged-multirent-table').append(comboHTML);
	var rentaNaturaSpinner = jQuery('#charged-renta-value-'+(rentaRows));

	jQuery('#renta-nat-is-converted-0').change(function()
	{
		if (jQuery('#renta-nat-is-converted-0').is(':checked'))
		{
			jQuery('#charged-unit-price-0').numberspinner({required: true, disabled: false});

			//set '0' cause id end with 0 for the first fields
			checkChargedRentaInLeva(0);
		} else {
			jQuery('#charged-unit-price-0').numberspinner({required: false, disabled: true});
		}
	});

	jQuery('#charged-unit-price-0').numberspinner({
		min: 0,
		disabled: true,
		missingMessage: 'Моля задайте Единична стойност(лв).',
		width: 95,
        precision: 3
	});

	jQuery('#charged-renta-value-0').numberspinner({
		min: 0,
		disabled: true,
		required: false,
		missingMessage: 'Моля задайте количество на горепосочената рента в натура(количество за декар).',
		width: 95,
		parser: function(value) {
			if(value == '-')
			{
				return value;
			}
			if(jQuery.isNumeric(value))
			{
				var output = parseFloat(value).toFixed(3);
				return output;
			}
		}
	});

	jQuery('#js-charged-renta-type-cb-'+(rentaRows)).combobox({
		url: 'index.php?common-rpc=renta-types-combobox',
    	valueField: 'id',
    	textField: 'name',
    	width: 205,
    	editable: false,
		onSelect: function(rec){
			let oldValue = jQuery('#js-charged-renta-type-cb-'+(rentaRows)).combobox('getValue');
			var rowArr = jQuery('.js-charged-multirent-row');
			var rowVars=[];
			for(var i=0; i < rowArr.length; i++) {
				rowVars[i] = jQuery('#js-charged-renta-type-cb-' + i).combobox('getValue');
			}
			var inRes = jQuery.inArray(String(rec.id),rowVars);
			if(inRes != -1) {
				jQuery.messager.alert('Внимание', 'Типът на натурата вече е избран!', 'warning');
				setTimeout(function(){
					jQuery('#js-charged-renta-type-cb-'+(rentaRows)).combobox('select', oldValue);
				},0);
				return false;
			}
		},
    	onChange: function(newValue, oldValue) {
    		onChangeRentaTypesCombobox(this, newValue);
    	},
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
	});
}

function onChangeRentaTypesCombobox(element, newValue) {
	var renta_nat_unit_id = element.id.split("-");
	var id = renta_nat_unit_id[renta_nat_unit_id.length - 1];
	var natura_price_check = jQuery('#renta-nat-is-converted-' + id).is(':checked');
	var data = jQuery('#js-charged-renta-type-cb-' + id).combobox('getData');
    var natura_price = jQuery('#charged-unit-price-' + id);
    var natura_amount = jQuery('#charged-renta-value-' + id);

    //clear value
    natura_price.numberbox('clear');

    //get unit value for renta type and set to field
	jQuery.each(data, function(key, value) {
	   if((!value.name || value.name == '-') && value.id == newValue)
	   {
	   	    natura_amount.numberspinner({required: false, disabled: true});
	   	    natura_amount.numberspinner('clear');
	   	    return;
	   }
	   else if(value.id == newValue) {
			if(natura_price_check) {
				natura_price.numberbox('setValue', value.unit_value);
			}

	   	    natura_amount.numberspinner({required: true, disabled: false});
			var natura_amount_value = natura_price.numberbox('getValue');
	   	    if(natura_amount_value == '')
	   	    {
	   	    	natura_amount.numberspinner('clear');
	   	    }
	   	    return;
	   }
	});
}

function checkChargedRentaInLeva(id) {
	var rentaTypesCombobox = jQuery('#js-charged-renta-type-cb-' + id);
	var data = rentaTypesCombobox.combobox('getData');
	var selectedData = rentaTypesCombobox.combobox('getValue');
    var natura_price = jQuery('#charged-unit-price-' + id);
    //clear value
    natura_price.numberbox('clear');
    //get unit value for renta type and set to field
	jQuery.each(data, function(key, value) {
	   if(value.unit_value && value.id == selectedData) {
	   	  natura_price.numberbox('setValue', value.unit_value);
	   }
	});
}

function initContractsPagination(total, limit, currentPage) {
	jQuery('#contracts-tree-pagination').pagination({
		showPageList: false,
		showRefresh: false,
		displayMsg: '',
		total: total,
		pageSize: limit,
		...(currentPage !== undefined && { pageNumber: currentPage }),
		onSelectPage: function(page, pageSize) {
			var obj = {};
			obj.year = jQuery('#search-year').combobox('getValue');
			obj.kad_ident = jQuery('#search-kad-ident').val();
			obj.ekate = jQuery('#search-ekatte').combobox('getValues');
			obj.masiv = jQuery('#search-masiv').val();
			obj.number = jQuery('#search-number').val();
			obj.category = jQuery('#search-category').combobox('getValues');
			obj.area_type = jQuery('#search-area-type').combobox('getValues');
			obj.cnum = jQuery('#search-cnum').val();
			obj.c_num_complete_match = jQuery('#search-cnum-complete-match').is(':checked');
			obj.contract_type = jQuery('#search-contract-type').combobox('getValues');
			obj.farming = jQuery('#search-farming').combobox('getValues');
			obj.irrigated_area = jQuery('#search-irrigated-area').combobox('getValue');
			obj.owner_name = jQuery('#search-owner-name').val();
			obj.owner_egn = jQuery('#search-owner-egn').val();
			obj.rep_name = jQuery('#search-represent-name').val();
			obj.rep_egn = jQuery('#search-represent-egn').val();
			obj.heritor_name = jQuery('#search-heritor-name').val();
			obj.heritor_egn = jQuery('#search-heritor-egn').val();
			obj.company_name = jQuery('#search-company-name').val();
			obj.company_eik = jQuery('#search-company-eik').val();
			obj.na_num = jQuery('#search-na-num').val();
			obj.na_num_complete_match = jQuery('#search-na-num-complete-match').is(':checked');

			if(jQuery('#search-contract-group').combobox('getValues') != '') {
				obj.c_group = jQuery('#search-contract-group').combobox('getValues');
			}

			if (jQuery('#search-is-closed-for-editing').is(':checked')) {
				obj.is_closed_for_editing = true;
			}

			if (jQuery('#search-contracts-with-nat input').is(':checked'))
			{
				obj.with_renta_nat = 1;
			}
			if (jQuery('#search-contracts-without-nat input').is(':checked'))
			{
				obj.with_renta_nat = 0;
			}

			initContractsTree(page, obj);
		}
	});
}

function fillContractInfo(data) {
    if(!data)
    {
        jQuery('#info-contract-number').html('');
        jQuery('#info-contract-type').html('');
		jQuery('#info-contract-group').html('');
        jQuery('#info-contract-date').html('');
        jQuery('#info-start-date').html('');
        jQuery('#info-due-date').html('');
        jQuery('#info-comment').html('');
        jQuery('#info-farming').html('');
        jQuery('#info-contract-renta').html('');
        jQuery('#info-contract-renta-label').html('');
        jQuery('#info-contract-renta-nat-type').html('');
        jQuery('#info-contract-renta-nat').html('');
        jQuery('#info-contract-sv-num').html('');
        jQuery('#info-contract-sv-date').html('');
        jQuery('#info-contract-pd-date').html('');
        jQuery('.js-payment-additional-row').remove();
        jQuery('#rent-per-plot-info').hide();
        jQuery('#info-is-closed-for-editing').html('');

        return false;
    }

    jQuery('#info-contract-number').html(data.c_num);
    jQuery('#info-contract-type').html(data.nm_usage_rights);
    jQuery('#info-contract-group').html(data.group_name);
    jQuery('#info-contract-date').html(data.c_date);
    jQuery('#info-start-date').html(data.start_date);
    jQuery('#info-due-date').html(data.due_date);
    jQuery('#info-comment').html(data.comment);
    jQuery('#info-farming').html(data.farming);
    jQuery('#info-contract-renta').html(data.renta_text);
    jQuery('#info-contract-renta-label').html('Рента/дка:');
    jQuery('#info-contract-renta-nat-type').html(data.renta_nat_type);
    jQuery('#info-contract-renta-nat').html(data.renta_nat_text);
    jQuery('#info-contract-sv-num').html(data.sv_num);
    jQuery('#info-contract-sv-date').html(data.sv_date);
    jQuery('#info-contract-pd-date').html(data.payday);
    jQuery('#info-is-closed-for-editing').html(data.is_closed_for_editing_text);

    if (data.has_rent_per_plot === true) {
        jQuery('#rent-per-plot-info').show();
    } else {
        jQuery('#rent-per-plot-info').hide();
    }

    if (data.overall_renta != null) {
        jQuery('#info-contract-renta-label').html('Обща сума за договор:');
        jQuery('#info-contract-renta').html(data.overall_renta);
    }
	
    var rowHTML;
    jQuery('.js-payment-additional-row').remove();
    for(var i = 0; i < data.additionalRentas.length; i++) {
        rowHTML = '<tr class="js-payment-additional-row"><td>' + data.additionalRentas[i]['renta_nat_type'] + '</td><td  style="padding-left:200px;">' + data.additionalRentas[i]['renta_nat_text'] + '</td></tr>';
        jQuery('#js-payment-rents-table').append(rowHTML);
    }
}

function contractsFilter(contractId) {
	const obj = getPaymentsContractsTreeFilter(contractId);

	initContractsTree(1, obj);

	jQuery('#acr-year > input').combobox('setValue', obj.year);
	jQuery('#acr-kad-ident > input').val(obj.kad_ident);
	jQuery('#acr-plot-ekatte > input').combobox('setValue', obj.ekate);
	jQuery('#acr-plot-masiv > input').val(obj.masiv);
	jQuery('#acr-plot-number > input').val(obj.number);
	jQuery('#acr-plot-category > input').combobox('setValue', obj.category);
	jQuery('#acr-plot-area-type > input').combobox('setValue', obj.area_type);
	jQuery('#acr-cnum > input').val(obj.cnum);
	jQuery('#acr-contract-type > input').combobox('setValue', obj.contract_type);
	jQuery('#acr-contract-group > input').combobox('setValue', obj.contract_group);
	jQuery('#acr-farming > input').combobox('setValue', obj.farming);
	jQuery('#acr-owner-name > input').val(obj.owner_name);
	jQuery('#acr-owner-egn > input').val(obj.owner_egn);
	jQuery('#acr-rep-name > input').val(obj.rep_name);
	jQuery('#acr-rep-egn > input').val(obj.rep_egn);
	jQuery('#acr-company-name > input').val(obj.company_name);
	jQuery('#acr-company-eik > input').val(obj.company_eik);

	jQuery('#win-contracts-filter').window('close');
}

//called on add-charged-renta button click
function validateAddChargedRenta() {
	var obj = getAddRentaFieldValues();

    TF.Rpc.Payments
        .AddChargedRenta
        .saveChargedRenta(obj)
        .done(function () {
			jQuery('#charged-renta-prev-win').window('close')
            jQuery('#win-add-charged-renta').window('close');
            jQuery('#charged-renta-history-tree').tree('loadRpc');

            var owner_payments_grid = jQuery('#contracts-owner-payments-tables');
            if(owner_payments_grid.length > 0) {
                owner_payments_grid.datagrid('loadRpc');
                return;
            }

            jQuery('#contract-payments-tables').treegrid('loadRpc');
        })
        .fail(function (errorObj) {
            jQuery('#charged-renta-history-tree').tree('loadRpc');
            jQuery.messager.alert('Грешка',errorObj.getMessage(),'warning');
        });
}

function getAddRentaFieldValues() {
	var renta = jQuery("div#win-add-charged-renta #acr-renta > input").numberbox('getValue');
	var type = jQuery("div#win-add-charged-renta #acr-type > input").val();
	if (type == '' || type.length > 20) {
		jQuery.messager.alert('Грешка', TF.Rpc.ExceptionsList.MISSING_CHARGED_RENTA_TYPE.message, 'error');
		return false;
	}

	//Количество натура/дка.
	var index = 0;
	var isCorrent = true;
	var haveChargedRenta = false;
	jQuery('#js-charged-multirent-table tr td:nth-child(2)').find('.validatebox-text').each(function(){
		var renta_amount = jQuery(this).val();
		var isValidRentaAmount = jQuery('#charged-renta-value-' + index).numberspinner('isValid');
		var rentaType = jQuery('#js-charged-renta-type-cb-' + index).combobox('getValue');

		index++;

		if((rentaType && rentaType != '-')) {
			haveChargedRenta = true;
		}

		if(renta_amount == '' && !isValidRentaAmount) {
			isCorrent = false;
			return false;
		}
	});

	if(!isCorrent){
		jQuery.messager.alert('Грешка', TF.Rpc.ExceptionsList.MISSING_RENTA_NATURA.message, 'error');
		return false;
	}

	//Единична стойност(лв)
	var chargedUnitPriceCorrect = true;
	jQuery.each(jQuery('.js-renta-nat-is-converted'), function (index, value) {
		var charged_unit_price = jQuery('#charged-unit-price-' + index).numberspinner('isValid');
		if (value.checked && !charged_unit_price){
			chargedUnitPriceCorrect = false;
			return false;
		}
	})

	if(!chargedUnitPriceCorrect){
		jQuery.messager.alert('Грешка', TF.Rpc.ExceptionsList.MISSING_UNIT_VALUE.message, 'error');
		return false;
	}

	if(!haveChargedRenta && renta == '') {
		jQuery.messager.alert('Грешка', TF.Rpc.ExceptionsList.WRONG_CHARGED_RENTA.message, 'error');
		return false;
	}

	var obj = new Object();
	obj.year = jQuery('div#win-add-charged-renta #acr-year > input').combobox('getValue');
	obj.ekate = jQuery('div#win-add-charged-renta #acr-plot-ekatte > input').combobox('getValue');
	obj.masiv = jQuery('div#win-add-charged-renta #acr-plot-masiv > input').val();
	obj.number = jQuery('div#win-add-charged-renta #acr-plot-number > input').val();
	obj.acr_type = jQuery('div#win-add-charged-renta #acr-type > input').val();
	obj.category = jQuery('div#win-add-charged-renta #acr-plot-category > input').combobox('getValues');
	obj.area_type = jQuery('div#win-add-charged-renta #acr-plot-area-type > input').combobox('getValues');
	obj.cnum = jQuery('div#win-add-charged-renta #acr-cnum > input').val();
	obj.contract_type = jQuery('div#win-add-charged-renta #acr-contract-type > input').combobox('getValue');
	obj.farming = jQuery('div#win-add-charged-renta #acr-farming > input').combobox('getValue');
	obj.contract_natura = jQuery('div#win-add-charged-renta #acr-contract-natura > input').combobox('getValue');
	obj.owner_ids = optionListOwners;
	obj.owner_egn = jQuery('div#win-add-charged-renta #acr-owner-egn > input').val();
	obj.rep_ids = optionListReps;
	obj.rep_egn = jQuery('div#win-add-charged-renta #acr-rep-egn > input').val();
	obj.company_ids = optionListCompanies;
	obj.company_eik = jQuery('div#win-add-charged-renta #acr-company-eik > input').val();
	obj.mestnost =jQuery('div#win-add-charged-renta #acr-plot-mestnost').combobox('getValues');

	obj.rent_type_arable = jQuery('#rent-type-arable-area').combobox('getValue');
	obj.rent_type_category = jQuery('#rent-type-category').combobox('getValues');
	obj.rent_type_ntp = jQuery('#rent-type-ntp').combobox('getValues');

	if (jQuery('div#win-add-charged-renta #acr-contract-group > input').combobox('getValue').length > 0) {
		obj.contract_group = jQuery('div#win-add-charged-renta #acr-contract-group > input').combobox('getValue');
    }

	if(obj.contract_type === '0') {
		obj.contract_type = null;
	}
	if(obj.farming === '0') {
		obj.farming = null;
	}

	//get data for charged renta
	if(renta)
	{
		obj.renta = renta;
	} else
	{
		obj.renta = null;
	}

	obj.renta_nat = 1;

	if(obj.category[0] == 0)
	{
		obj.category = [];
	}
	if(obj.area_type[0] == 0)
	{
		obj.area_type = [];
	}

	if (jQuery('div#win-add-charged-renta #acr-contracts-with-nat-radio').is(':checked'))
	{
		jQuery('#acr-contracts-with-nat-radio').prop('checked', true);
		obj.natura = 'with_nat';
	}
	if (jQuery('div#win-add-charged-renta #acr-contracts-without-nat-radio').is(':checked'))
	{
		jQuery('#acr-contracts-without-nat-radio').prop('checked', true);
		obj.contract_natura = '0';
		obj.natura = 'without_nat';
	}
	if (jQuery('div#win-add-charged-renta #acr-all-contracts-radio').is(':checked'))
	{
		jQuery('#acr-all-contracts-radio').prop('checked', true);
		obj.contract_natura = null;
		obj.natura = 'all';
	}

	if(jQuery('div#win-add-charged-renta #acr-convert-nat > input').is(':checked'))
	{
		obj.nat_is_converted = true;
		obj.nat_unit_price = jQuery("#acr-nat-unit-price input[type='text']").numberbox('getValue');
	}
	else
	{
		obj.nat_id_converted = false;
	}

	if(jQuery('div#win-add-charged-renta #acr-overall-renta-checkbox').is(':checked'))
	{
		obj.overall_renta = true;
	} else {
		obj.overall_renta = false;
	}

	obj.owner_type = jQuery('div#win-add-charged-renta #acr-owner-type').combobox('getValue');

	var rowArr = jQuery('.js-charged-multirent-row');
	var rowVars=[];

	for(var i = 0; i < rowArr.length; i++) {
		rowVars[i] = {
			'renta_nat_type':jQuery('#js-charged-renta-type-cb-' + i).combobox('getValue'),
			'renta_value':jQuery('#charged-renta-value-' + i).numberspinner('getValue'),
			'price_per_unit': jQuery('#charged-unit-price-' + i).numberspinner('getValue'),
			'is_converted': jQuery('#renta-nat-is-converted-' + i).is(':checked')
		};
	}

	obj.multirents = rowVars;

	return obj;
}

function addChargedRentaField(dataNatura, count){
    var rentaRows = jQuery('#js-charged-multirent-table').find('.js-charged-multirent-row').length;
    if(rentaRows == jQuery('#js-charged-renta-type-cb-0').combobox('getData').length - 1) {
        return false;
    }

    var comboHTML ='<tr class="js-charged-multirent-row"><td style="padding:0px 0px 10px 15px"><select id="js-charged-renta-type-cb-'+(rentaRows)+'"></select></td>';
    comboHTML += '<td style="padding:0px 0px 10px 10px"><input class="js-charged-renta-value" id="charged-renta-value-'+(rentaRows)+'"></td>';
	comboHTML += '<td style="padding:0px 0px 10px 10px"><input class="js-charged-unit-price" id="charged-unit-price-'+(rentaRows)+'"></td>';
    comboHTML += '<td style="padding:0px 0px 10px 10px"><input type="checkbox" class="js-renta-nat-is-converted" id="renta-nat-is-converted-'+(rentaRows)+'"></td>';

    if(count != rentaRows - 1){
        jQuery('#js-charged-multirent-table').append(comboHTML);
    }

    jQuery('#js-charged-renta-type-cb-'+(rentaRows)).combobox({
        url: 'index.php?common-rpc=renta-types-combobox',
        valueField: 'id',
        textField: 'name',
        width: 205,
		onSelect: function(rec) {
			let oldValue = jQuery('#js-charged-renta-type-cb-'+(rentaRows)).combobox('getValue');
			var rowArr = jQuery('.js-charged-multirent-row');
			var rowVars=[];
			for(var i=0; i < rowArr.length; i++) {
				rowVars[i] = jQuery('#js-charged-renta-type-cb-' + i).combobox('getValue');
			}
			var inRes = jQuery.inArray(String(rec.id),rowVars);
			if(inRes != -1) {
				jQuery.messager.alert('Внимание', 'Типът на натурата вече е избран!', 'warning');
				setTimeout(function(){
					jQuery('#js-charged-renta-type-cb-'+(rentaRows)).combobox('select', oldValue);
				},0);
				return false;
			}
		},
        onChange: function(newValue, oldValue) {
            onChangeRentaTypesCombobox(this, newValue);
        },
        onLoadSuccess: function() {
            if(typeof(dataNatura) != 'undefined'){
                var chargedRentaType = jQuery('#js-charged-renta-type-cb-' + rentaRows);
                var chargedRentaValue = jQuery('#charged-renta-value-' + rentaRows);

                chargedRentaType.combobox('select', dataNatura['type']);

                var prec = 3;
                if(dataNatura['unit'] === 3){
                    prec = 0;
                }
                if(dataNatura['price']){
                    jQuery('#renta-nat-is-converted-' + rentaRows).click();
                    jQuery('#charged-unit-price-' + rentaRows).numberspinner('setValue', dataNatura['price']);
                }

                chargedRentaValue.numberspinner({
                    precision: prec,
                    value: dataNatura['amount']
                });
            }
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    jQuery('#charged-renta-value-'+(rentaRows)).numberspinner({
        min: 0,
        disabled: true,
        missingMessage: 'Моля задайте количество на горепосочената рента в натура(количество за декар).',
        width: 95,
        parser: function(value) {
            if(value == '-') {
                return value;
            }
            if(jQuery.isNumeric(value))	{
                var output = parseFloat(value).toFixed(3);
                return output;
            }
        }
    });

    jQuery('#charged-unit-price-'+(rentaRows)).numberspinner({
        min: 0,
        disabled: true,
        precision: 3,
        missingMessage: 'Моля задайте единична стойност(лв).',
        width: 95,
    });

    jQuery('#renta-nat-is-converted-'+(rentaRows)).change(function()
    {
        if (jQuery('#renta-nat-is-converted-'+(rentaRows)).is(':checked'))
        {
            jQuery('#charged-unit-price-'+(rentaRows)).numberspinner({required: true, disabled: false});

            checkChargedRentaInLeva(rentaRows);

        } else {
            jQuery('#charged-unit-price-'+(rentaRows)).numberspinner({required: false, disabled: true});
        }
    });
}
