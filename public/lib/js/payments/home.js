//variable hold payment type
//single for one contract
//multi for more than one contract
var paymentType = "single",
    optionListOwners = [],
    transactionsOptionListOwners = [],
    optionListReps = [],
    optionListCompanies = [],
    _pathFile = "",
    tmpContractId = null,
    currentFarmingYear,
    ComboboxData

jQuery(function() {
    var date = new Date();
    var winDownload = jQuery("#win-download");
    var todayDate =
        date.getFullYear() + "-" + (date.getMonth() + 1) + "-" + date.getDate();
    setUserRights();

    initSearchOnEnter();

    TF.Rpc.Common.CombinedComboboxData.read(null, null, {selected: 'current'})
        .done(function(data) {
            ComboboxData = data;
            initFilters();
            initFiltersNatura();
            initTransactionFilterFields();

            //acr tree filter
            initFilterButtons();
            initRentaHistoryFilter(ComboboxData);
        })
        .fail(function(error) {
            jQuery.messager.alert("Грешка", error.getMessage(), "warning");
        });

    jQuery('input[name="tr_rko_type"]').change(function() {
        jQuery('#tr_rko_type_declaration_row').hide();
        jQuery('#tr_rko_type_declaration_row').prop('checked', false);

        if (jQuery('#tr_rko_receipt').is(':checked')) {
            jQuery('#win-generate-payment-document-from-transaction').window('resize', {
                height: jQuery('#btn-payment-order-generate').offset().top - jQuery('#transactionWindowTop').offset().top + 130
            });
            jQuery('#tr_rko_type_declaration_row').show();
        } else {   
            jQuery('#win-generate-payment-document-from-transaction').window('resize', {
                height: jQuery('#btn-payment-order-generate').offset().top - jQuery('#transactionWindowTop').offset().top + 89
            });
        }
    });

    jQuery('input[name="rko_type"]').change(function() {
        jQuery('#rko_type_declaration_row').hide();
        jQuery('#rko_type_declaration').prop('checked', false);
        jQuery('#collect-all-payment-amounts-input').show();

        if (jQuery('#rko_receipt').is(':checked')) {
            jQuery('#rko_type_declaration_row').show();
        } else if (jQuery('#rko_card').is(':checked')) {
            jQuery('#collect-all-payment-amounts-input').hide();
            jQuery('#collect-all-payment-amounts').prop('checked', false);
        } else {
            
        }
    });

    if (jQuery('#rko_receipt').is(':checked')) {
        jQuery('#rko_type_declaration_row').show();
    }

    jQuery('input[name="nat_rko_type"]').change(function() {
        jQuery('#nat_rko_type_declaration_row').hide();

        if (jQuery('#nat_rko_receipt').is(':checked')) {
            jQuery('#nat_rko_type_declaration_row').show();
        }
    });

    if (jQuery('#nat_rko_receipt').is(':checked')) {
        jQuery('#nat_rko_type_declaration_row').show();
    }

    //retrieve GET parameters
    var GET = getQueryParams();
    tmpContractId = GET.contract_id;
    tmpOwnerName = GET.owner_name ?? '';
    tmpCompanyName = GET.company_name ?? '';

    jQuery("#win-add-charged-renta").window({
        onBeforeClose: function(argument) {
            resetChargedRentaForm();
        }
    });
    
    initContractPaymentsGrid(0,0,getCalendarFarmingYearId());

    initChargedRentaHistoryTree(1, {});
    initFCBKcomplete();
    initTransactionsFilterFCBKcomplete();
    initSearchFieldsComplete();

    //init button controls
    jQuery("#btnweghingnote").bind("click", function() {
        initWeighingNoteGrid();
        jQuery("#win-weighing-note").window("open");
    });

    jQuery("#info-charged-renta-btn").bind("click", function() {
        jQuery("#win-info-charged-renta-btn").window("open");
        return false;
    });

    jQuery("#btn-open-contracts-filter").bind("click", function() {

        jQuery('#win-contracts-filter').window('resize', {
            height: getZoomedWindowHeight(710),
            width: 600
        });

        jQuery("#win-contracts-filter").window("open");
        jQuery("#win-contracts-filter").window("center");
        return false;
    });

    jQuery("#btn-clear-contracts-filter").bind("click", function() {
        tmpContractId = null;
        tmpOwnerName = null;
        tmpCompanyName = null;

        const currentFarmingYearId = getCalendarFarmingYearId();

        jQuery("#search-kad-ident").val("");
        jQuery("#search-ekatte").combobox("reset");
        jQuery("#search-masiv").val("");
        jQuery("#search-number").val("");
        jQuery("#search-category").combobox("reset");
        jQuery("#search-area-type").combobox("reset");
        jQuery("#search-farming").combobox("reset");
        jQuery("#search-cnum").val("");
        jQuery("#search-na-num").val("");
        jQuery("#search-cnum-complete-match").prop("checked", true);
        jQuery("#search-na-num-complete-match").prop("checked", true);
        jQuery("#search-contract-type").combobox("reset");
        jQuery("#search-contract-group").combobox("reset");
        jQuery("#search-is-closed-for-editing").prop("checked", false);

        if (jQuery("#search-irrigated-area")[0] != undefined) {
            jQuery("#search-irrigated-area").combobox("loadRpc");
        }

        jQuery('#search-owner-note').val('');
        jQuery('#search-owner-phone').val('');
        jQuery('#search-person-name').val('');
        jQuery('#search-person-egn').val('');
        jQuery('#search-owner-name').val('');
        jQuery('#search-owner-egn').val('');
        jQuery('#search-heirs-name').val('');
        jQuery('#search-heirs-egn').val('');
        jQuery('#search-represent-name').val('');
        jQuery('#search-represent-egn').val('');
        jQuery('#search-company-name').val('');
        jQuery('#search-company-eik').val('');
        jQuery('#search-all-contracts input').prop('checked', true);
        jQuery('#search-all-contracts-with-bank-acc').prop('checked', false);

        //clear add charged renta fields
        jQuery("#acr-kad-ident > input").val("");
        jQuery("#acr-plot-masiv > input").val("");
        jQuery("#acr-plot-number > input").val("");
        jQuery("#acr-cnum > input").val("");
        jQuery("#acr-owner-name > input").val("");
        jQuery("#acr-owner-egn > input").val("");
        jQuery("#acr-rep-name > input").val("");
        jQuery("#acr-rep-egn > input").val("");
        jQuery("#acr-company-name > input").val("");
        jQuery("#acr-company-eik > input").val("");
        jQuery("#acr-overall-renta-checkbox").prop("checked", false);
        jQuery("#search-mestnost").combobox("reset");
        
        // The request for reloading payments-contracts-tree is sent in the "#search-year" combobox's onSelect method which is called
        // when different farmig year is selected
        const selectedFarmingYearId = jQuery("#search-year").combobox("getValue");
        if (selectedFarmingYearId == currentFarmingYearId) {
            // If the selected year is the same as the current one, we need to reload the tree manually
            // becuse when select the same year the onSelect event is not triggered
            
            var paymentsTable = jQuery('#contract-payments-tables');
            var ownerPaymentsTable = jQuery('#contracts-owner-payments-tables');

            const params = paymentsTable?.length > 0
                ? getPaymentsContractsTreeFilter()
                : (ownerPaymentsTable?.length > 0
                    ? getOwnersContractsTreeFilter()
                    : {});

            initContractsTree(1, params);
        } else {
            // If the selected year is different from the current one we need to use the combobox's select method
            // in order to trigger the onSelect event which updates the selected year and reloads the tree
            jQuery("#search-year").combobox("select", currentFarmingYearId);
        }

    });

    jQuery("#btn-add-charged-renta").bind("click", function() {
        if (!hasPlotRightsRW) {
            messagerPlotsWriteRights();
            return false;
        }
        jQuery.messager.alert({
            title: "Информация",
            msg:
                "С това действие се начислява рента само на имотите с рента по договор, като не се взимат в предвид имотите с въведена индивидуална рента.",
            fn: function() {
                var contractData = jQuery("#contracts-tree").tree(
                    "getSelected"
                );

                if (contractData) {
                    jQuery("#win-add-charged-renta-history").window("open");
                } else {
                    jQuery.messager.alert(
                        "Грешка",
                        "Не е избран договор!",
                        "error"
                    );
                }
            }
        });

        return false;
    });

    jQuery('#btn-prev-charged-renta').bind('click', function() {
        initChargedRentaPrevGrid()
    });

    jQuery("#btn-create-new-charged-renta").bind("click", function() {
        jQuery("#win-add-charged-renta").window("open");
        jQuery("#win-add-charged-renta").window("center");

        jQuery("#rent-type-category").combobox('select', '');
        jQuery("#rent-type-ntp").combobox('select', '');

        initAddChargedRentaFields();
        return false;
    });

    jQuery("#btn-add-charged-renta > a").bind("click", function() {
        validateAddChargedRenta();

        return false;
    });

    jQuery("#btn-create-similar-charged-renta").bind("click", function(e) {
        e.preventDefault();

        var tree = jQuery("#charged-renta-history-tree").tree("getSelected");
        var natura = tree.data.natura;
        var categories = tree.data.category;
        var sr_categories = tree.data.sr_category;
        var sr_ntp = tree.data.sr_ntp;
        var sr_arable = tree.data.sr_arable;
        var area_types = tree.data.area_type;
        var owners = tree.dataIdsNames.owners;
        var reps = tree.dataIdsNames.reps;
        var companies = tree.dataIdsNames.companies;
        var renta = tree.data.renta;
        var dataNatura = tree.dataNatura || "";

        jQuery("#acr-year > input").combobox("select", tree.data.farming_year);

        if (owners) {
            jQuery.each(owners, function(key, value) {
                jQuery("#acr-owner-name > input").trigger("addItem", [
                    { title: value, value: key }
                ]);
            });
        }

        jQuery("#acr-owner-egn > input").val(tree.data.owner_egn);

        if (reps) {
            jQuery.each(reps, function(key, value) {
                jQuery("#acr-rep-name > input").trigger("addItem", [
                    { title: value, value: key }
                ]);
            });
        }
        jQuery("#acr-rep-egn > input").val(tree.data.rep_egn);

        if (companies) {
            jQuery.each(companies, function(key, value) {
                jQuery("#acr-company-name > input").trigger("addItem", [
                    { title: value, value: key }
                ]);
            });
        }

        jQuery("#acr-company-eik > input").val(tree.data.company_eik);
        jQuery("#acr-cnum > input").val(tree.data.c_num);
        jQuery("#acr-contract-type > input").combobox(
            "select",
            tree.data.c_type
        );
        jQuery("#acr-contract-group > input").combobox(
            "select",
            tree.data.c_group
        );
        jQuery("#acr-farming > input").combobox("select", tree.data.farming_id);
        jQuery("#acr-overall-renta-checkbox").prop(
            "checked",
            tree.data.with_overall_renta
        );
        jQuery("#acr-owner-type").combobox(
            "select",
            String(tree.data.owner_type)
        );

        if (natura == "without_nat") {
            jQuery("#acr-contracts-without-nat-radio").click();
        } else if (natura == "with_nat") {
            jQuery("#acr-contracts-with-nat-radio").click();
            jQuery("#acr-contract-natura > input").combobox(
                "select",
                tree.data.natura_type
            );
        }

        jQuery("#acr-plot-ekatte > input").combobox("select", tree.data.ekate);
        jQuery("#acr-plot-masiv > input").val(tree.data.masiv);
        jQuery("#acr-plot-number > input").val(tree.data.number);
        setComboCategories(categories);
        setComboAreaTypes(area_types);
        setComboSRCategory(sr_categories);
        setComboSRNTP(sr_ntp);
        setComboSRAraable(sr_arable);

        if (renta) {
            jQuery("#acr-renta > input").textbox("setValue", renta);
        }

        jQuery("#win-add-charged-renta").window("open");

        for (var i = 0; i < dataNatura.length; i++) {
            var chargedRentaType = jQuery("#js-charged-renta-type-cb-" + i);
            var chargedRentaValue = jQuery("#charged-renta-value-" + i);

            if (i == 0) {
                chargedRentaType.combobox("select", dataNatura[i]["type"]);
                chargedRentaValue.numberspinner(
                    "setValue",
                    dataNatura[i]["amount"]
                );

                if (dataNatura[i]["price"]) {
                    jQuery("#renta-nat-is-converted-" + i).click();
                    jQuery("#charged-unit-price-" + i).numberspinner(
                        "setValue",
                        dataNatura[i]["price"]
                    );
                }
            }

            if (dataNatura.length > 1) {
                addChargedRentaField(dataNatura[i], i);
            }
        }
    });

    jQuery("#btn-info-charged-renta").bind("click", function() {
        jQuery("#win-info-charged-renta").window("open");
    });

    jQuery("#btn-view-transactions").bind("click", function() {
        jQuery("#win-transactions").window("open");
        initTransactionsGrid();

        return false;
    });

    jQuery("#btn-payments-reports").bind("click", function() {
        jQuery("#win-choose-report-type").window("open");
    });

    //init button icons
    jQuery("#btn-choose-year > a").linkbutton({ iconCls: "icon-ok" });
    jQuery("#btn-add-charged-renta > a").linkbutton({ iconCls: "icon-save" });
    jQuery("#btn-add-payment > a").linkbutton({ iconCls: "icon-payments" });
    jQuery("#btn-add-nat-payment > a").linkbutton({ iconCls: "icon-payments" });

    //init charged renta fields
    initAddChargedRentaFields();

    jQuery(
        "#acr-all-contracts-radio, #acr-contracts-with-nat-radio, #acr-contracts-without-nat-radio"
    ).change(function() {
        var renta_nat_options;
        if (jQuery("#acr-contracts-with-nat-radio").is(":checked")) {
            jQuery("#acr-contract-natura > input").combobox("enable");

            renta_nat_options = jQuery("#acr-contract-natura > input").combobox(
                "options"
            );
            renta_nat_options.required = true;
            jQuery("#acr-contract-natura > input").combobox({
                options: renta_nat_options
            });
            jQuery("#acr-contract-natura > input").validatebox({
                required: true,
                missingMessage: "Това поле е задължително!"
            });
        } else {
            jQuery("#acr-contract-natura > input").combobox("disable");
            renta_nat_options = jQuery("#acr-contract-natura > input").combobox(
                "options"
            );
            renta_nat_options.required = false;
            jQuery("#acr-contract-natura > input").combobox({
                options: renta_nat_options
            });
        }
    });

    jQuery("#acr-contract-natura > input").combobox("reload");

    jQuery("#btn-add-payment > a").bind("click", function() {
        validateNewPayment();
    });

    jQuery("#btn-add-payment-with-deduction").bind("click", function() {
        validateNewPayment(true);
    });

    jQuery("#btn-add-nat-payment > a").bind("click", function() {
        if(payment_type && payment_type === 'personal_use') {
            validatePersonalUsePayment();
        } else {
            validateNewNatPayment();
        }
    });

    winDownload.window({
        onClose: onDowbloadWindowClose
    });

    jQuery("#refresh_summary-report-by-ekate-money").bind("click", function() {
        refreshSummaryReportByEkateMoney();
    });

    jQuery("#print_summary-report-by-ekate-money").bind("click", function() {
        printSummaryReport();
    });

    jQuery("#export_summary-report-by-ekate-money").bind("click", function() {
        exportToExcelSummaryReport();
    });
});

function initTransactionFilterFields() {
    var transactionTypesComboboxData = ComboboxData.TransactionTypesCombobox,
        farmingComboboxData = ComboboxData.FarmingCombobox;

    jQuery("#search-transaction-farming-year").combobox({
        data: ComboboxData.getFarmingYearWithCalendarYearSelectedCombobox,
        valueField: "id",
        textField: "farming_year",
        multiple: true,
        onHidePanel: onHidePanelMultiSelect,
        editable: false,
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    jQuery("#search-transaction-start-date").datebox({
        required: false,
        missingMessage: "Моля въведете дата.",
        buttons: dateboxWithClearButton
    });

    jQuery("#search-transaction-due-date").datebox({
        required: false,
        missingMessage: "Моля въведете дата.",
        buttons: dateboxWithClearButton
    });
    jQuery("#search-transaction-paid-by").combobox({
        url: "index.php?common-rpc=payer-names-combobox",
        valueField: "payer_name",
        textField: "payer_name",
        multiple: false,
        editable: false,
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
    jQuery("#search-transaction-disabled-by").combobox({
        url: "index.php?common-rpc=cancelled-by-combobox",
        valueField: "cancelled_by",
        textField: "cancelled_by",
        multiple: false,
        editable: false,
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    jQuery("#search-transaction-type").combobox({
        data: transactionTypesComboboxData,
        valueField: "id",
        textField: "name",
        multiple: true,
        editable: false,
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    jQuery("#search-transaction-farming").combobox({
        data: farmingComboboxData,
        valueField: "id",
        textField: "name",
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

function onDowbloadWindowClose() {
    return;
}

function initAddChargedRentaFields()
{
    jQuery("div#win-add-charged-renta #acr-renta > input").textbox({
        min: 0,
        parser: function(value) {
            if (value == "-") {
                return value;
            }
            if (jQuery.isNumeric(value)) {
                return parseFloat(value).toFixed(2);
            }
        }
    });

    jQuery("div#win-add-charged-renta #acr-renta-nat > input").textbox({
        min: 0,
        precision: 2
    });

    jQuery("#contract-info-button").bind("click", function() {
        var getSelected = jQuery("#contracts-tree").tree("getSelected");
        if (getSelected.id) {
            window.open(
                "index.php?page=Contracts.Home&contract_id=" + getSelected.id,
                "_blank"
            );
        } else {
            jQuery.messager.alert("Грешка", "Моля изберете договор.", "error");
        }
    });

    jQuery("div#win-add-charged-renta #acr-contract-natura > input").combobox({
        url: "index.php?common-rpc=renta-types-combobox",
        rpcParams: [
            {
                selected: true
            }
        ],
        valueField: "id",
        textField: "name",
        editable: false,
        disabled: true,
        loadFilter: function(data) {
            data = data.slice(1, data.length);
            return data;
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    jQuery("div#win-add-charged-renta #acr-type > input").validatebox({
        validType: {
            length: [0, 20]
        },
        required: true,
        missingMessage: "Това поле е задължително!"
    });
}

function initFCBKcomplete() {
    jQuery("div#win-add-charged-renta #acr-owner-name > input").fcbkcomplete({
        json_url: "index.php?owners-rpc=owners-list",
        rpcParams: ["owners"],
        cache: true,
        firstselected: true,
        addontab: true,
        complete_text: "Търси собственик...",
        width: 200,
        maxitimes: 20,
        maxshownitems: 20,
        height: 5,
        onselect: function() {
            jQuery("div#win-add-charged-renta #acr-owner-name-text > option:selected").each(function() {
                var id = jQuery(this).val();

                if (jQuery.inArray(id, optionListOwners) < 0) {
                    optionListOwners.push(id);
                }
            });
        },
        onremove: function() {
            var id = jQuery("div#win-add-charged-renta #acr-owner-name-text > option:selected").val();
            var index = optionListOwners.indexOf(id);
            if (index > -1) {
                optionListOwners.splice(index, 1);
            }
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    jQuery("div#win-add-charged-renta #acr-rep-name > input").fcbkcomplete({
        json_url: "index.php?owners-rpc=owners-list",
        rpcParams: ["reps"],
        cache: true,
        firstselected: true,
        addontab: true,
        complete_text: "Търси представител...",
        width: 222,
        maxitimes: 20,
        maxshownitems: 20,
        height: 5,
        onselect: function() {
            jQuery("div#win-add-charged-renta #acr-rep-name-text > option:selected").each(function() {
                var id = jQuery(this).val();

                if (jQuery.inArray(id, optionListReps) < 0) {
                    optionListReps.push(id);
                }
            });
        },
        onremove: function() {
            var id = jQuery("div#win-add-charged-renta #acr-rep-name-text > option:selected").val();
            var index = optionListReps.indexOf(id);

            if (index > -1) {
                optionListReps.splice(index, 1);
            }
        }
    });

    jQuery("div#win-add-charged-renta #acr-company-name > input").fcbkcomplete({
        json_url: "index.php?owners-rpc=owners-list",
        rpcParams: ["companies"],
        cache: true,
        firstselected: true,
        addontab: true,
        complete_text: "Търси фирма...",
        width: 222,
        maxitimes: 20,
        maxshownitems: 20,
        height: 5,
        onselect: function() {
            jQuery("div#win-add-charged-renta #acr-company-name-text > option:selected").each(function() {
                var id = jQuery(this).val();

                if (jQuery.inArray(id, optionListCompanies) < 0) {
                    optionListCompanies.push(id);
                }
            });
        },
        onremove: function() {
            var id = jQuery("div#win-add-charged-renta #acr-company-name-text > option:selected").val();
            var index = optionListCompanies.indexOf(id);

            if (index > -1) {
                optionListCompanies.splice(index, 1);
            }
        }
    });
}

function initTransactionsFilterFCBKcomplete() {
    jQuery("#transactions-filter-owner-name > input").fcbkcomplete({
        json_url: "index.php?owners-rpc=owners-list",
        rpcParams: ["owners"],
        cache: true,
        firstselected: true,
        addontab: true,
        complete_text: "Търси собственик...",
        width: 200,
        maxitimes: 20,
        maxshownitems: 20,
        height: 5,
        onselect: function() {
            jQuery(
                "#transactions-filter-owner-name-text > option:selected"
            ).each(function() {
                var id = jQuery(this).val();

                if (jQuery.inArray(id, transactionsOptionListOwners) < 0) {
                    transactionsOptionListOwners.push(id);
                }
            });
        },
        onremove: function() {
            var id = jQuery(
                "#transactions-filter-owner-name-text > option:selected"
            ).val();
            var index = transactionsOptionListOwners.indexOf(id);
            if (index > -1) {
                transactionsOptionListOwners.splice(index, 1);
            }
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

function initSearchFieldsComplete() {
    var search_owners = new Bloodhound({
        datumTokenizer: Bloodhound.tokenizers.whitespace("value"),
        queryTokenizer: Bloodhound.tokenizers.whitespace,
        remote: {
            url: "index.php?owners-rpc=owners-list",
            cache: false,
            prepare: function(query, settings) {
                settings.query = query;
                return settings;
            },
            transport: function(settings, onSuccess, onError) {
                var options = {
                    type: "POST",
                    dataType: "json",
                    contentType: "application/json; charset=UTF-8",
                    data: JSON.stringify({
                        method: "read",
                        params: ["owners", settings.query],
                        id: 1,
                        jsonrpc: "2.0"
                    })
                };

                jQuery
                    .ajax("index.php?owners-rpc=owners-list", options)
                    .done(done)
                    .fail(fail)
                    .always(always);

                function done(data, textStatus, request) {
                    onSuccess(data.result);
                }
                function fail(request, textStatus, errorThrown) {
                    onError(errorThrown);
                }
                function always() {}
            }
        }
    });

    jQuery("#search-owner-name").typeahead(
        {
            hint: false,
            highlight: false,
            minLength: 1,
            limit: 10,
            menu: jQuery("#search-owner-name-typeahead-target")
        },
        {
            name: "search_owners",
            displayKey: "value",
            source: search_owners.ttAdapter()
        }
    );

    jQuery("#search-owner-name").on("typeahead:open", function(e, datum) {
        jQuery("#search-owner-name-typeahead-target").width(
            jQuery("#search-owner-name").width()
        );
        var offset = jQuery("#search-owner-name").offset();
        jQuery("#search-owner-name-typeahead-target").offset({
            top: offset.top + jQuery("#search-owner-name").height(),
            left: offset.left
        });
        jQuery("#search-owner-name-typeahead-target").css("z-index",9999);
    });

    jQuery("#search-owner-name").on("typeahead:selected", function(e, datum) {
        jQuery("#search-owner-name").typeahead("close");
        jQuery("#search-owner-name-typeahead-target").css("z-index",-1);
    });
    jQuery("#search-owner-name").on("typeahead:idle", function(e, datum) {
        jQuery("#search-owner-name").typeahead("close");
        jQuery("#search-owner-name-typeahead-target").css("z-index",-1);
    });

    var search_reps = new Bloodhound({
        datumTokenizer: Bloodhound.tokenizers.whitespace("value"),
        queryTokenizer: Bloodhound.tokenizers.whitespace,
        remote: {
            url: "index.php?owners-rpc=owners-list",
            cache: false,
            prepare: function(query, settings) {
                settings.query = query;
                return settings;
            },
            transport: function(settings, onSuccess, onError) {
                var options = {
                    type: "POST",
                    dataType: "json",
                    contentType: "application/json; charset=UTF-8",
                    data: JSON.stringify({
                        method: "read",
                        params: ["reps", settings.query],
                        id: 1,
                        jsonrpc: "2.0"
                    })
                };

                jQuery
                    .ajax("index.php?owners-rpc=owners-list", options)
                    .done(done)
                    .fail(fail)
                    .always(always);

                function done(data, textStatus, request) {
                    onSuccess(data.result);
                }
                function fail(request, textStatus, errorThrown) {
                    onError(errorThrown);
                }
                function always() {}
            }
        }
    });

    jQuery("#search-represent-name").typeahead(
        {
            hint: false,
            highlight: false,
            minLength: 1,
            limit: 10,
            menu: jQuery("#search-represent-name-typeahead-target")
        },
        {
            name: "search_reps",
            displayKey: "value",
            source: search_reps.ttAdapter()
        }
    );

    jQuery("#search-represent-name").on("typeahead:open", function(e, datum) {
        jQuery("#search-represent-name-typeahead-target").width(
            jQuery("#search-represent-name").width()
        );
        var offset = jQuery("#search-represent-name").offset();
        jQuery("#search-represent-name-typeahead-target").offset({
            top: offset.top + jQuery("#search-represent-name").height(),
            left: offset.left
        });
        jQuery("#search-represent-name-typeahead-target").css("z-index","9999");
    });

    jQuery("#search-represent-name").on("typeahead:selected", function(
        e,
        datum
    ) {
        jQuery("#search-represent-name").typeahead("close");
        jQuery("#search-represent-name-typeahead-target").css("z-index",-1);
    });
    jQuery("#search-represent-name").on("typeahead:idle", function(e, datum) {
        jQuery("#search-represent-name").typeahead("close");
        jQuery("#search-represent-name-typeahead-target").css("z-index",-1);
    });

    var search_company = new Bloodhound({
        datumTokenizer: Bloodhound.tokenizers.whitespace("value"),
        queryTokenizer: Bloodhound.tokenizers.whitespace,
        remote: {
            url: "index.php?owners-rpc=owners-list",
            cache: false,
            prepare: function(query, settings) {
                settings.query = query;
                return settings;
            },
            transport: function(settings, onSuccess, onError) {
                var options = {
                    type: "POST",
                    dataType: "json",
                    contentType: "application/json; charset=UTF-8",
                    data: JSON.stringify({
                        method: "read",
                        params: ["companies", settings.query],
                        id: 1,
                        jsonrpc: "2.0"
                    })
                };

                jQuery
                    .ajax("index.php?owners-rpc=owners-list", options)
                    .done(done)
                    .fail(fail)
                    .always(always);

                function done(data, textStatus, request) {
                    onSuccess(data.result);
                }
                function fail(request, textStatus, errorThrown) {
                    onError(errorThrown);
                }
                function always() {}
            }
        }
    });

    jQuery("#search-company-name").typeahead(
        {
            hint: false,
            highlight: false,
            minLength: 1,
            limit: 10,
            menu: jQuery("#search-company-name-typeahead-target")
        },
        {
            name: "search_company",
            displayKey: "value",
            source: search_company.ttAdapter()
        }
    );

    jQuery("#search-company-name").on("typeahead:open", function(e, datum) {
        jQuery("#search-company-name-typeahead-target").width(
            jQuery("#search-company-name").width()
        );
        var offset = jQuery("#search-company-name").offset();
        jQuery("#search-company-name-typeahead-target").offset({
            top: offset.top + jQuery("#search-company-name").height(),
            left: offset.left
        });
        jQuery("#search-company-name-typeahead-target").css("z-index",9999);
    });

    jQuery("#search-company-name").on("typeahead:selected", function(e, datum) {
        jQuery("#search-company-name").typeahead("close");
        jQuery("#search-company-name-typeahead-target").css("z-index",-1);
    });

    jQuery("#search-company-name").on("typeahead:idle", function(e, datum) {
        jQuery("#search-company-name").typeahead("close");
        jQuery("#search-company-name-typeahead-target").css("z-index",-1);
    });

    jQuery("#search-heirs-name").typeahead(
        {
            hint: false,
            highlight: false,
            minLength: 1,
            limit: 10,
            menu: jQuery("#search-heirs-name-typeahead-target")
        },
        {
            name: "search_owners",
            displayKey: "value",
            source: search_owners.ttAdapter()
        }
    );

    jQuery("#search-heirs-name").on("typeahead:open", function(e, datum) {
        var type_ahead_target = jQuery("#search-heirs-name-typeahead-target");
        type_ahead_target.width(jQuery(e.target).width());
        var offset = jQuery(e.target).offset();
        type_ahead_target.offset({
            top: offset.top + jQuery("#search-heirs-name").height(),
            left: offset.left
        });
        type_ahead_target.css("z-index",9999);
    });

    jQuery("#search-heirs-name").on("typeahead:selected", function(e, datum) {
        jQuery(e.target).typeahead("close");
        jQuery("#search-heirs-name-typeahead-target").css("z-index",-1);
    });
    jQuery("#search-heirs-name").on("typeahead:idle", function(e, datum) {
        jQuery(e.target).typeahead("close");
        jQuery("#search-heirs-name-typeahead-target").css("z-index",-1);
    });
}

function resetChargedRentaForm() {
    var farmingYear = jQuery("#search-year").combobox("getValue");

    jQuery("#acr-type > input").val("");
    jQuery("#acr-year > input").combobox("select", farmingYear);
    var tmpOptionListOwners = optionListOwners.slice(0);
    for (var i = 0; i < tmpOptionListOwners.length; i++) {
        jQuery("#acr-owner-name > input").trigger("removeItem", [
            { value: tmpOptionListOwners[i] }
        ]);
    }
    optionListOwners = [];

    jQuery("#acr-owner-egn > input").val("");

    var tmpOptionListReps = optionListReps.slice(0);
    for (var i = 0; i < tmpOptionListReps.length; i++) {
        jQuery("#acr-rep-name > input").trigger("removeItem", [
            { value: tmpOptionListReps[i] }
        ]);
    }
    optionListReps = [];

    jQuery("#acr-rep-egn > input").val("");

    var tmpOptionListCompanies = optionListCompanies.slice(0);
    for (var i = 0; i < tmpOptionListCompanies.length; i++) {
        jQuery("#acr-company-name > input").trigger("removeItem", [
            { value: tmpOptionListCompanies[i] }
        ]);
    }
    optionListCompanies = [];

    jQuery("#acr-company-eik > input").val("");
    jQuery("#acr-cnum > input").val("");
    jQuery("#acr-contract-type > input").combobox("reset");
    jQuery("#acr-contract-group > input").combobox("reset");
    jQuery("#acr-farming > input").combobox("reset");
    jQuery("#acr-all-contracts > input").click();
    jQuery("#acr-contract-natura > input").combobox("reset");
    jQuery("#acr-plot-ekatte > input").combobox("reset");
    jQuery("#acr-plot-masiv > input").val("");
    jQuery("#acr-plot-number > input").val("");
    jQuery("#acr-plot-category > input").combobox("reset");
    jQuery("#acr-plot-area-type > input").combobox("reset");
    jQuery("#acr-renta > input").textbox("setValue", "");
    jQuery(".js-charged-multirent-row:not(:first)").remove();
    jQuery("#js-charged-renta-type-cb-0").combobox("reset");
    jQuery("#charged-renta-value-0").numberspinner("clear");
    jQuery("#charged-renta-value-0").numberspinner("disable");
    jQuery("#charged-unit-price-0").numberspinner("clear");
    jQuery("#charged-unit-price-0").numberspinner("disable");
    jQuery("#renta-nat-is-converted-0").prop("checked", false);
    jQuery("#acr-overall-renta-checkbox").prop("checked", false);
    jQuery("#acr-owner-type").combobox("reset");

    jQuery("#rent-type-arable-area").combobox("reset");
    jQuery("#rent-type-category").combobox("reset");
    jQuery("#rent-type-ntp").combobox("reset");
}

function setComboCategories(categories) {
    var haveCategories = false;

    if (categories) {
        var category = categories.split(",");
        for (var i = 0; i < category.length; i++) {
            jQuery("#acr-plot-category > input").combobox(
                "select",
                category[i]
            );
        }
        haveCategories = true;
    }

    //unSelect option ALL
    if (haveCategories) {
        jQuery("#acr-plot-category > input").combobox("unselect", "");
    } else {
        jQuery("#acr-plot-category > input").combobox("select", "");
    }
}

function setComboAreaTypes(areaTypes) {
    var haveAreaType = false;

    if (jQuery.isNumeric(areaTypes) && areaTypes > 0) {
        jQuery("#acr-plot-area-type > input").combobox("select", areaTypes);
        haveAreaType = true;
    } else if (areaTypes.indexOf(",") >= 0) {
        var areaType = areaTypes.split(",");

        for (var i = 0; i < areaType.length; i++) {
            jQuery("#acr-plot-area-type > input").combobox(
                "select",
                areaType[i]
            );
        }

        haveAreaType = true;
    }

    //unSelect option ALL
    if (haveAreaType) {
        jQuery("#acr-plot-area-type > input").combobox("unselect", "");
    } else {
        jQuery("#acr-plot-area-type > input").combobox("select", "");
    }
}

function setComboSRCategory(srCategories) {
    var haveSRCategory = false;

    if (srCategories) {
        var category = srCategories.split(",");
        for (var i = 0; i < category.length; i++) {
            jQuery("#rent-type-category").combobox(
                "select",
                category[i]
            );
        }
        haveSRCategory = true;
    }

    //unSelect option ALL
    if (haveSRCategory) {
        jQuery("#rent-type-category").combobox("unselect", "");
    } else {
        jQuery("#rent-type-category").combobox("select", "");
    }
}

function setComboSRNTP(srNTP) {
    var haveSRNTP = false;

    if (srNTP) {
        var ntp = srNTP.split(",");
        for (var i = 0; i < ntp.length; i++) {
            jQuery("#rent-type-ntp").combobox("select", ntp[i]);
        }
        haveSRNTP = true;
    }

    //unSelect option ALL
    if (haveSRNTP) {
        jQuery("#rent-type-ntp").combobox("unselect", "");
    } else {
        jQuery("#rent-type-ntp").combobox("select", "");
    }
}

function setComboSRAraable(srArable) {
    var haveSRAraable = false;

    if (srArable) {
        jQuery("#rent-type-arable-area").combobox(
            "select",
            srArable
        );
        haveSRAraable = true;
    }

    //unSelect option ALL
    if (haveSRAraable) {
        jQuery("#rent-type-arable-area").combobox("unselect", "");
    } else {
        jQuery("#rent-type-arable-area").combobox("select", "");
    }
}

function initChooseYearPanel() {
    jQuery("#choose-year > input").combobox({
        data: jQuery("#search-year").combobox("getData"),
        valueField: "id",
        textField: "title",
        editable: false
    });
}

function initFiltersNatura() {
    var farmingYearComboboxData = ComboboxData.FarmingYearCombobox,
        newFarmingYearComboboxData = [];

    farmingYearComboboxData.forEach(function(el, index) {
        if (el.id != "") {
            newFarmingYearComboboxData.push(el);
        }
    });

    jQuery("#prf-year-natura > input").combobox({
        data: newFarmingYearComboboxData,
        valueField: "id",
        textField: "farming_year",
        multiple: true,
        onHidePanel: onHidePanelMultiSelect,
        editable: false,
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    jQuery("#prf-type-natura > input").combobox({
        url: "index.php?common-rpc=renta-types-combobox",
        valueField: "id",
        textField: "name",
        editable: false,
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

function initFilters() {
    var irrigatedAreaComboboxData = ComboboxData.IrrigatedAreaCombobox,
        farmingYearComboboxData = ComboboxData.FarmingYearCombobox,
        newFarmingYearComboboxData = [],
        contractTypeComboboxData = ComboboxData.ContractTypeCombobox,
        newContractTypeComboboxData = [],
        farmingComboboxData = ComboboxData.FarmingCombobox,
        plotNTPComboboxData = ComboboxData.PlotNTPCombobox,
        categoryComboboxData = ComboboxData.PlotCategoryCombobox,
        ekateComboboxData = ComboboxData.EkateCombobox,
        contractGroupsCombobox = ComboboxData.ContractGroupsCombobox;

    irrigatedAreaComboboxData[0].selected = true;
    jQuery("#search-irrigated-area").combobox({
        data: irrigatedAreaComboboxData,
        editable: false,
        valueField: "value",
        textField: "label",
        multiple: false,
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    filterObj = {};
    let GET = getQueryParams();
    if(GET.contract_id || GET.owner_name || GET.company_name){
        restorePageURL(GET);
    }
    farmingYearComboboxData.forEach(function(el, index) {
        if (el.id != "") {
            if(GET.farming_year){
                el.selected = false;
                if(el.id == GET.farming_year){
                    el.selected = true;
                }
            } 
            newFarmingYearComboboxData.push(el);
        }
    });
    jQuery("#acr-year > input").combobox({
        data: newFarmingYearComboboxData,
        valueField: "id",
        textField: "farming_year",
        editable: false
    });

    jQuery("#prf-year-by-date > input").combobox({
        data: newFarmingYearComboboxData,
        valueField: "id",
        textField: "farming_year",
        multiple: true,
        onHidePanel: onHidePanelMultiSelect,
        editable: false
    });
    jQuery("#prf-year > input").combobox({
        data: newFarmingYearComboboxData,
        valueField: "id",
        textField: "farming_year",
        multiple: true,
        onHidePanel: onHidePanelMultiSelect,
        editable: false,
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    contractTypeComboboxData.forEach(function(el) {
        if (el.id !== 1 && el.type !== 4) {
            newContractTypeComboboxData.push(el);
        }
    });

    jQuery("#search-contract-type").combobox({
        data: contractTypeComboboxData,
        editable: false,
        valueField: "id",
        textField: "name",
        multiple: true,
        onHidePanel: onHidePanelMultiSelect,
        onSelect: onComboMultiSelect,
        onLoadSuccess: function() {
            var contract_type_data = jQuery("#search-contract-type").combobox(
                "getData"
            );
            jQuery("#acr-contract-type > input").combobox({
                data: contract_type_data,
                valueField: "id",
                textField: "name",
                editable: false
            });

            jQuery("#prf-contract-type > input").combobox({
                data: contract_type_data,
                valueField: "id",
                textField: "name",
                editable: false
            });

            jQuery("#prf-contract-type-natura > input").combobox({
                data: contract_type_data,
                valueField: "id",
                textField: "name",
                editable: false
            });

            jQuery("#prf-contract-type-by-date > input").combobox({
                data: contract_type_data,
                valueField: "id",
                textField: "name",
                editable: false
            });
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    jQuery('#search-contract-group').combobox({
        data: contractGroupsCombobox,
        editable: true,
        valueField: "id",
        textField: "name",
        multiple: true,
        onHidePanel: onHidePanelMultiSelect,
        onSelect: onComboMultiSelect,
        onLoadSuccess: function () {
            jQuery(this).combobox('select', '');

            var contract_type_group = jQuery("#search-contract-group").combobox(
                "getData"
            );

            jQuery("#acr-contract-group > input").combobox({
                data: contract_type_group,
                valueField: "id",
                textField: "name",
                editable: true
            });
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    jQuery("#search-farming").combobox({
        data: farmingComboboxData,
        editable: false,
        valueField: "id",
        textField: "name",
        multiple: true,
        onHidePanel: onHidePanelMultiSelect,
        onSelect: onComboMultiSelect,
        onLoadSuccess: function() {
            var farming_data = jQuery("#search-farming").combobox("getData");
            jQuery("#acr-farming > input").combobox({
                data: farming_data,
                valueField: "id",
                textField: "name",
                editable: false
            });

            jQuery("#prf-farming > input").combobox({
                data: farming_data,
                valueField: "id",
                textField: "name",
                multiple: true,
                onHidePanel: onHidePanelMultiSelect,
                editable: false
            });

            jQuery("#prf-farming-natura > input").combobox({
                data: farming_data,
                valueField: "id",
                textField: "name",
                multiple: true,
                onHidePanel: onHidePanelMultiSelect,
                editable: false
            });

            jQuery("#prf-farming-by-date > input").combobox({
                data: farming_data,
                valueField: "id",
                textField: "name",
                multiple: true,
                onHidePanel: onHidePanelMultiSelect,
                editable: false
            });
            
            intFilterFarming(farming_data);
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    jQuery("#search-area-type").combobox({
        data: plotNTPComboboxData,
        editable: false,
        valueField: "id",
        textField: "name",
        multiple: true,
        onHidePanel: onHidePanelMultiSelect,
        onSelect: onComboMultiSelect,
        onLoadSuccess: function() {
            var plot_area_data = jQuery("#search-area-type").combobox(
                "getData"
            );
            jQuery("#acr-plot-area-type > input").combobox({
                data: plot_area_data,
                valueField: "id",
                textField: "name",
                multiple: true,
                editable: true
            });

            jQuery("#prf-plot-area-type > input").combobox({
                data: plot_area_data,
                valueField: "id",
                textField: "name",
                editable: false
            });

            jQuery("#prf-plot-area-type-natura > input").combobox({
                data: plot_area_data,
                valueField: "id",
                textField: "name",
                editable: false
            });
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    jQuery("#search-category").combobox({
        data: categoryComboboxData,
        editable: false,
        valueField: "id",
        textField: "name",
        multiple: true,
        onHidePanel: onHidePanelMultiSelect,
        onSelect: onComboMultiSelect,
        onLoadSuccess: function() {
            var category_data = jQuery("#search-category").combobox("getData");
            jQuery("#acr-plot-category > input").combobox({
                data: category_data,
                valueField: "id",
                textField: "name",
                multiple: true,
                onHidePanel: onHidePanelMultiSelect,
                editable: false
            });

            jQuery("#prf-plot-category > input").combobox({
                data: category_data,
                valueField: "id",
                textField: "name",
                editable: false
            });
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    jQuery("#rent-type-arable-area").combobox({
        data: [
            {
                id: "",
                title: "Без специфична рента",
                selected: true
            },
            {
                id: "0",
                title: "Необработваема земя"
            },
            {
                id: "1",
                title: "Обработваема земя"
            },
            
        ],
        valueField: "id",
        textField: "title",
        editable: false,
        multiple: false,
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    let categoies = [
        {
            id: "",
            name: "Без специфична рента",
            selected: true
        }
    ];
    ComboboxData.PlotCategoryCombobox.forEach(function (el) {
        if (el.id != '' && el.id != '-1') {
            categoies.push(el);
        }
    });
    jQuery("#rent-type-category").combobox({
        data: categoies,
        editable: false,
        valueField: "id",
        textField: "name",
        multiple: true,
        onHidePanel: onHidePanelMultiSelect,
        onSelect: onComboMultiSelect,
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    let ntps = [
        {
            id: "",
            name: "Без специфична рента",
            selected: true
        }
    ];
    ComboboxData.PlotNTPCombobox.forEach(function (el) {
        if (el.id != '' && el.id != '-1') {
            let ntp = {};
            ntp.id = null; 
            ntp.name = null; 
            
            ntp.id = el.id;
            ntp.name = el.id + ': ' + el.name;
            ntps.push(ntp);
        }
    });
    jQuery("#rent-type-ntp").combobox({
        data: ntps,
        editable: true,
        valueField: "id",
        textField: "name",
        multiple: true,
        onHidePanel: onHidePanelMultiSelect,
        onSelect: onComboMultiSelect,
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    jQuery("#search-ekatte").combobox({
        data: ekateComboboxData,
        valueField: "ekate",
        textField: "text",
        multiple: true,
        onHidePanel: onHidePanelMultiSelect,
        onSelect: onComboMultiSelect,
        onLoadSuccess: function() {
            var ekatte_data = jQuery("#search-ekatte").combobox("getData");
            jQuery("#acr-plot-ekatte > input").combobox({
                data: ekatte_data,
                valueField: "ekate",
                textField: "text",
                onChange: function(newValue, oldValue) {
                    updateMestnostCombobox(newValue, "#acr-plot-mestnost");
                },
                filter: function(q, row) {
                    var opts = jQuery(this).combobox("options");
                    var text = row[opts.textField].toLowerCase();
                    var value = row[opts.valueField];
                    var find = q.toLowerCase();
                    if (text.indexOf(find) != -1 || value.indexOf(find) != -1) {
                        return true;
                    }
                }
            });

            jQuery("#prf-plot-ekatte > input").combobox({
                data: ekatte_data,
                valueField: "ekate",
                multiple: true,
                onHidePanel: onHidePanelMultiSelect,
                textField: "text",
                filter: function(q, row) {
                    var opts = jQuery(this).combobox("options");
                    var text = row[opts.textField].toLowerCase();
                    var value = row[opts.valueField];
                    var find = q.toLowerCase();
                    if (text.indexOf(find) != -1 || value.indexOf(find) != -1) {
                        return true;
                    }
                }
            });

            jQuery("#prf-plot-ekatte-natura > input").combobox({
                data: ekatte_data,
                valueField: "ekate",
                multiple: true,
                onHidePanel: onHidePanelMultiSelect,
                textField: "text",
                filter: function(q, row) {
                    var opts = jQuery(this).combobox("options");
                    var text = row[opts.textField].toLowerCase();
                    var value = row[opts.valueField];
                    var find = q.toLowerCase();
                    if (text.indexOf(find) != -1 || value.indexOf(find) != -1) {
                        return true;
                    }
                }
            });

            jQuery("#prf-plot-ekatte-by-date > input").combobox({
                data: ekatte_data,
                valueField: "ekate",
                multiple: true,
                onHidePanel: onHidePanelMultiSelect,
                textField: "text",
                filter: function(q, row) {
                    var opts = jQuery(this).combobox("options");
                    var text = row[opts.textField].toLowerCase();
                    var value = row[opts.valueField];
                    var find = q.toLowerCase();
                    if (text.indexOf(find) != -1 || value.indexOf(find) != -1) {
                        return true;
                    }
                }
            });
        },
        onChange: function(newValue, oldValue) {
            updateMestnostCombobox(newValue, "#search-mestnost");
        },
        filter: function(q, row) {
            var opts = jQuery(this).combobox("options");
            var text = row[opts.textField].toLowerCase();
            var value = row[opts.valueField];
            var find = q.toLowerCase();
            if (text.indexOf(find) != -1 || value.indexOf(find) != -1) {
                return true;
            }
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    jQuery("#acr-nat-unit-price > input").numberbox({
        min: 0,
        precision: 2
    });

    jQuery("#acr-nat-unit-price input[type='text']").numberbox("disable");

    jQuery("#acr-convert-nat > input").change(function() {
        if (jQuery("#acr-convert-nat > input").is(":checked")) {
            jQuery("#acr-nat-unit-price input[type='text']").numberbox(
                "enable"
            );
        } else {
            jQuery("#acr-nat-unit-price input[type='text']").numberbox(
                "disable"
            );
        }
    });

    jQuery("#report-by-date-from").datebox();
    jQuery("#report-by-date-to").datebox();

    jQuery("#acr-owner-type").combobox({
        data: [
            {
                id: "",
                title: "Всички",
                selected: true
            },
            {
                id: "0",
                title: "Юридически лица"
            },
            {
                id: "1",
                title: "Физически лица"
            }
        ],
        valueField: "id",
        textField: "title",
        editable: false,
        multiple: false,
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });

    jQuery("#search-mestnost").combobox({ disabled: true });
    jQuery("#acr-plot-mestnost").combobox({ disabled: true });

    jQuery("#search-year").combobox({
        data: ComboboxData.getFarmingYearWithCalendarYearSelectedCombobox,
        valueField: "id",
        textField: "farming_year",
        editable: false,
        onLoadSuccess: function() {
            //init add charged renta panel
            /* Duplicating requests. Execute tree load only on select.
            currentFarmingYear = Number(jQuery(this).combobox("getValue"));
            var queryParams = getQueryParams();
            initContractsTree(1, Object.assign({}, {
                year: currentFarmingYear,
            }, queryParams));
            */
        },
        onSelect: function(node) {
            if (!node || !node.id || node.id == "") {
                return;
            }
            currentFarmingYear = node.id;
            filterObj.year = currentFarmingYear;

            if (
                jQuery("#acr-year > input")
                    .data()
                    .hasOwnProperty("combobox")
            ) {
                jQuery("#acr-year > input").combobox("setValue", filterObj.year);
            }

            if(tmpOwnerName?.length > 0){
                jQuery('#search-owner-name').val(tmpOwnerName);
            }

            if(tmpCompanyName?.length > 0){
                jQuery('#search-company-name').val(tmpCompanyName);
            }

            setTimeout(() =>contractsFilter(tmpContractId));
        },
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

function updateMestnostCombobox(data, element) {
    var params = {
        ekates: []
    };
    if (typeof data === "string") params.ekates.push(data);
    if (data instanceof Array) params.ekates = data;
    var isAllSelected = params.ekates.find(function(ekatte) {
        return ekatte === "";
    });

    if (isAllSelected || params.ekates.length === 0) {
        jQuery(element).combobox({
            data: [],
            disabled: true
        });
        return;
    }
    TF.Rpc.Common.MestnostCombobox.read(params)
        .done(function(res) {
            jQuery(element).combobox({
                data: res,
                editable: false,
                valueField: "mestnost",
                textField: "text",
                multiple: true,
                disabled: false,
                onHidePanel: onHidePanelMultiSelect,
                onSelect: onComboMultiSelect,
                loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
                loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
            });
        })
        .fail(function(error) {
            jQuery.messager.alert("Грешка", error.getMessage(), "warning");
        });
}

function completeSavePayment(data) {
    generatePaymentDocument(data);
    jQuery("#contract-payments-tables").treegrid("reload");
}

function completeSaveNatPayment(data) {
    generatePaymentNatDocument(data);
    jQuery("#contract-payments-tables").treegrid("reload");
}

function generatePaymentNatDocument(data) {
    var orderWithDate = true;
    var rko = false;
    var weighingNote = false;
    var weighingNoteOnly = false;

    if (data) {
        var transaction_id = data.transaction_id,
            collection_id = data.collection_id,
            winDownload = jQuery("#win-download"),
            downloadFile = jQuery("#btn-download-file");

        if (data.payment_type == "cash") {
            if (jQuery("#payment-natura-date-checkbox").prop("checked")) {
                orderWithDate = false;
            }
            if (jQuery("#generate-natura-payment-order").prop("checked")) {
                rko = true;
            }
            if (jQuery("#weighing-note-natura-checkbox").prop("checked")) {
                weighingNote = true;
            }

            paymentSubjectText = null;
            paymentSubjectData = jQuery(
                "#np-payment-subjects-combobox"
            ).combobox("getValue");

            if (parseInt(paymentSubjectData) === RKO_MANUAL_TEXT_OPTION) {
                paymentSubjectText = jQuery("#np-payment-subjects-text").val();
            }

            var combineDocument =  jQuery("#combine-payment-document").prop("checked") ? true : false;

            var rko_type = jQuery('input[name="nat_rko_type"]:checked').val();
            var rko_type_declaration = jQuery('#nat_rko_type_declaration').is(':checked');
            var collectAllPaymentAmounts =  jQuery('#collect-all-payment-amounts').prop('checked') ? true : false;

            let paymentsParams = {};
            let owner_id = null;
            if(jQuery('#contract-payments-tables').length > 0){
                payments = jQuery('#contract-payments-tables').treegrid('getChecked');
            } else {
                payments = jQuery('#contracts-owner-payments-tables').datagrid('getChecked');
                owner_id = jQuery('#owners-contracts-tree').tree('getSelected').id;
            }

            for(let i = 0; i < payments.length; i++) {
                let contract_id = payments[i].annex_id  ?? payments[i].contract_id;;
                if(jQuery('#contract-payments-tables').length > 0){
                    owner_id = payments[i].owner_id;
                }
    
                if (!paymentsParams[contract_id]) {
                    paymentsParams[contract_id] = {};
                }
    
                if(!paymentsParams[contract_id][owner_id]){
                    paymentsParams[contract_id][owner_id] = {};
                }

                paymentsParams[contract_id][owner_id] = {
                    all_owner_area: payments[i].all_owner_area,
                    pu_area: payments[i].pu_area ?? 0,
                    owner_area: payments[i].owner_area,
                    paid_renta: payments[i].paid_renta,
                    unpaid_renta: payments[i].unpaid_renta,
                    personal_use_nat_types_names_arr: payments[i].personal_use_nat_types_names_arr,
                    personal_use_unpaid_treatments_arr: payments[i].personal_use_unpaid_treatments_arr,
                    renta_nat_type_arr: payments[i].nat_type_ids.length > 0 ? payments[i].renta_nat_type.split('</br>') : [],
                    unpaid_renta_nat_arr: payments[i].unpaid_renta_nat_text ? payments[i].unpaid_renta_nat_text.split('</br>') : [],
                    paid_renta_by_arr: payments[i].paid_renta_by ? payments[i].paid_renta_by.split('</br>') : [],
                    paid_renta_nat_by_arr: payments[i].paid_renta_nat_by_detailed ? payments[i].paid_renta_nat_by_detailed.split('</br>') : [],
                }
            };

            if (weighingNote && !rko) {
                TF.Rpc.Payments.ExportPayment.exportToPdfWeighingNote(
                    transaction_id,
                    orderWithDate
                ).done(function(dataObj) {
                    winDownload.window("open");
                    var path = dataObj.file_path;
                    _pathFile = path;
                    _fileName = dataObj.file_name;
                    downloadFile.attr("href", path);
                 }).fail(function(errorObj) {});
                return;
            } else if (!weighingNote && rko) {
                TF.Rpc.Payments.ExportPayment.exportToPdfPaymentOrder(
                    transaction_id,
                    null,
                    orderWithDate,
                    paymentSubjectData,
                    paymentSubjectText,
                    false,
                    false,
                    collection_id,
                    combineDocument,
                    collectAllPaymentAmounts,
                    rko_type,
                    rko_type_declaration,
                    JSON.stringify(paymentsParams)
                ).done(function(dataObj) {
                    winDownload.window("open");
                    var path = dataObj.file_path;
                    _pathFile = path;
                    _fileName = dataObj.file_name;
                    downloadFile.attr("href", path);
                });
                return;
            } else if (weighingNote && rko) {
                TF.Rpc.Payments.ExportPayment.exportToPdfCombined(
                    transaction_id,
                    orderWithDate,
                    paymentSubjectData,
                    paymentSubjectText,
                    [],
                    combineDocument,
                    collectAllPaymentAmounts,
                    rko_type,
                    rko_type_declaration,
                    JSON.stringify(paymentsParams)
                )
                    .done(function(dataObj) {
                        winDownload.window("open");
                        var path = dataObj.file_path;
                        _pathFile = path;
                        _fileName = dataObj.file_name;
                        downloadFile.attr("href", path);
                    })
                    .fail(function(errorObj) {});
            }
        } else if (data.payment_type == "bank") {
            if (jQuery("#payment-natura-date-checkbox").prop("checked")) {
                orderWithDate = false;
            }
            if (jQuery("#generate-natura-payment-order").prop("checked")) {
                rko = true;
            }
            if (jQuery("#weighing-note-natura-checkbox").prop("checked")) {
                weighingNote = true;
            }

            var farmingIban = jQuery(
                "#np-orderer-bank-account > input"
            ).combobox("getText");
            if (farmingIban == "-") {
                farmingIban = "";
            }

            paymentSubjectText = jQuery("#np-payment-subjects-text").val();
            paymentSubjectData = null;

            var combineDocument =  jQuery("#combine-payment-document").prop("checked") ? true : false;

            if (weighingNote && !rko) {
                TF.Rpc.Payments.ExportPayment.exportToPdfWeighingNote(
                    transaction_id,
                    orderWithDate
                )
                    .done(function(dataObj) {
                        winDownload.window("open");
                        var path = dataObj.file_path;
                        _pathFile = path;
                        _fileName = dataObj.file_name;
                        downloadFile.attr("href", path);
                    })
                    .fail(function(errorObj) {});
                return;
            } else if (!weighingNote && rko) {
                TF.Rpc.Payments.ExportPayment.exportToPdfBankPaymentOrder(
                    transaction_id,
                    orderWithDate,
                    farmingIban,
                    paymentSubjectData,
                    paymentSubjectText,
                    false,
                    combineDocument
                )
                    .done(function(dataObj) {
                        winDownload.window("open");
                        var path = dataObj.file_path;
                        _pathFile = path;
                        _fileName = dataObj.file_name;
                        downloadFile.attr("href", path);
                    })
                    .fail(function(errorObj) {});
                return;
            } else if (weighingNote && rko) {
                TF.Rpc.Payments.ExportPayment.exportToPdfBankCombined(
                    transaction_id,
                    orderWithDate,
                    farmingIban,
                    paymentSubjectData,
                    paymentSubjectText
                )
                    .done(function(dataObj) {
                        winDownload.window("open");
                        var path = dataObj.file_path;
                        _pathFile = path;
                        _fileName = dataObj.file_name;
                        downloadFile.attr("href", path);
                    })
                    .fail(function(errorObj) {});
            }
        } else if (data.payment_type == "natura") {
            if (jQuery("#np-weighing-note-checkbox").prop("checked")) {
                weighingNoteOnly = true;
            }

            if (weighingNoteOnly) {
                TF.Rpc.Payments.ExportPayment.exportToPdfWeighingNote(
                    transaction_id,
                    true
                )
                    .done(function(dataObj) {
                        winDownload.window("open");
                        var path = dataObj.file_path;
                        _pathFile = path;
                        _fileName = dataObj.file_name;
                        downloadFile.attr("href", path);
                    })
                    .fail(function(errorObj) {});
                return;
            }
        }
    }
}

function generatePaymentDocument(data) {
    var orderWithDate = true,
        rko = false,
        weighingNote = false,
        weighingNoteOnly = false,
        paymentSubject = false;
        postPгaymentPdf = false;

    if (data) {
        var transaction_id = data.transaction_id,
            collection_id = data.collection_id,
            winDownload = jQuery("#win-download"),
            downloadFile = jQuery("#btn-download-file");

        var combineDocument =  jQuery("#combine-payment-document").prop("checked") ? true : false;
        var rko_type = jQuery('input[name="rko_type"]:checked').val();
        var rko_type_declaration = jQuery('#rko_type_declaration').is(':checked');
        var collectAllPaymentAmounts =  jQuery('#collect-all-payment-amounts').prop('checked') ? true : false;

        let paymentsParams = {};
        let payments = [];
        let owner_id = null;

        if(jQuery('#contract-payments-tables').length > 0){
            payments = jQuery('#contract-payments-tables').treegrid('getChecked');
        } else {
            payments = jQuery('#contracts-owner-payments-tables').datagrid('getChecked');
            owner_id = jQuery('#owners-contracts-tree').tree('getSelected').id;
        }

        for(let i = 0; i < payments.length; i++) {
            let contract_id = payments[i].annex_id  ?? payments[i].contract_id;
            if(jQuery('#contract-payments-tables').length > 0){
                owner_id = payments[i].owner_id;
            }
            let path = payments[i].path ?? owner_id;

            if (!paymentsParams[contract_id]) {
                paymentsParams[contract_id] = {};
            }

            if(!paymentsParams[contract_id][owner_id]){
                paymentsParams[contract_id][owner_id] = {};
            }

            if (!paymentsParams[contract_id][owner_id][path]) {
                paymentsParams[contract_id][owner_id][path] = {};
            }

            paymentsParams[contract_id][owner_id][path] = {
                all_owner_area: payments[i].all_owner_area ?? payments[i].area,
                pu_area: payments[i].pu_area ?? 0,
                owner_area: payments[i].owner_area ?? payments[i].owned_area,
                paid_renta: payments[i].paid_renta,
                unpaid_renta: payments[i].unpaid_renta,
                personal_use_nat_types_names_arr: payments[i].personal_use_nat_types_names_arr,
                personal_use_unpaid_treatments_arr: payments[i].personal_use_unpaid_treatments_arr,
                renta_nat_type_arr: payments[i].nat_type_ids.length ? payments[i].renta_nat_type.split('</br>') : [],
                unpaid_renta_nat_arr: payments[i].unpaid_renta_nat_text ? payments[i].unpaid_renta_nat_text.split('</br>') : [],
                paid_renta_by_arr: payments[i].paid_renta_by ? payments[i].paid_renta_by.split('</br>') : [],
                paid_renta_nat_by_arr: payments[i].paid_renta_nat_by_detailed ? payments[i].paid_renta_nat_by_detailed.split('</br>') : [],
            }
        };

        if (data.payment_type == "cash") {
            if (jQuery("#payment-date-checkbox").prop("checked")) {
                orderWithDate = false;
            }
            if (jQuery("#payment-order-checkbox").prop("checked")) {
                rko = true;
            }
            if (jQuery("#weighing-note-checkbox").prop("checked")) {
                weighingNote = true;
            }

            paymentSubjectText = null;
            paymentSubjectData = jQuery("#payment-subjects-combobox").combobox(
                "getValue"
            );

            if (parseInt(paymentSubjectData) === RKO_MANUAL_TEXT_OPTION) {
                paymentSubjectText = jQuery("#payment-subjects-text").val();
            }

            if (rko) {
                TF.Rpc.Payments.ExportPayment.exportToPdfPaymentOrder(
                    transaction_id,
                    null,
                    orderWithDate,
                    paymentSubjectData,
                    paymentSubjectText,
                    false,
                    false,
                    collection_id,
                    combineDocument,
                    collectAllPaymentAmounts,
                    rko_type,
                    rko_type_declaration,
                    JSON.stringify(paymentsParams)
                ).done(function(dataObj) {
                    winDownload.window("open");
                    var path = dataObj.file_path;
                    _pathFile = path;
                    _fileName = dataObj.file_name;
                    downloadFile.attr("href", path);
                });
                return;
            }
        } else if (data.payment_type == "bank") {

            if (jQuery("#payment-date-checkbox").prop("checked")) {
                orderWithDate = false;
            }
            if (jQuery("#payment-order-checkbox").prop("checked")) {
                rko = true;
            }
            if (jQuery("#weighing-note-checkbox").prop("checked")) {
                weighingNote = true;
            }
            var farmingIban = jQuery("#payment-orderer-bank-account > input").combobox("getValue");

            paymentSubjectText = null;
            paymentSubjectData = jQuery("#payment-subjects-combobox").combobox(
                "getValue"
            );

            if (parseInt(paymentSubjectData) === RKO_MANUAL_TEXT_OPTION) {
                paymentSubjectText = jQuery("#payment-subjects-text").val();
            }
            var combineDocument =  jQuery("#combine-payment-document").prop("checked") ? true : false;

            if (rko) {
                TF.Rpc.Payments.ExportPayment.exportToPdfBankPaymentOrder(
                    transaction_id,
                    orderWithDate,
                    farmingIban,
                    paymentSubjectData,
                    paymentSubjectText,
                    false,
                    combineDocument,
                )
                    .done(function(dataObj) {
                        winDownload.window("open");
                        var path = dataObj.file_path;
                        _pathFile = path;
                        _fileName = dataObj.file_name;
                        downloadFile.attr("href", path);
                    })
                    .fail(function(errorObj) {});
                return;
            }
        } else if ((data.payment_type == "post")) {
            if (jQuery("#payment-date-checkbox").prop("checked")) {
                orderWithDate = false;
            }
            if (jQuery("#payment-order-checkbox").prop("checked")) {
                postPaymentPdf = true;
            }


            const senderData = PostPaymentSender.getPostPaymentValues();         
            const recipientData = PostPaymentOwner.getPostPaymentValues();
            recipientData.name = jQuery("#payment-recipient-text").val();
            recipientData.iban = jQuery("#payment-bank-account-text").val();

            if (postPaymentPdf === true) {
                TF.Rpc.Payments.ExportPayment.exportToPdfPostPaymentOrder(
                    transaction_id,
                    orderWithDate,
                    senderData,
                    recipientData
                )
                    .done(function(dataObj) {
                        winDownload.window("open");
                        var path = dataObj.file_path;
                        _pathFile = path;
                        _fileName = dataObj.file_name;
                        downloadFile.attr("href", path);
                    })
                    .fail(function(errorObj) {});
                
            }
                
            return;

        } else if (data.payment_type == "natura") {
            if (
                jQuery("#payment-date-nat-checkbox-money-to-nat").prop(
                    "checked"
                )
            ) {
                orderWithDate = false;
            }
            if (
                jQuery("#payment-order-checkbox-money-to-nat").prop("checked")
            ) {
                rko = true;
            }
            if (
                jQuery("#weighing-note-checkbox-money-to-nat").prop("checked")
            ) {
                weighingNote = true;
            }

            if (jQuery("#np-weighing-note-checkbox").prop("checked")) {
                weighingNoteOnly = true;
            }

            var combineDocument =  jQuery("#combine-payment-document").prop("checked") ? true : false;
            paymentSubjectData = RKO_MANUAL_TEXT_OPTION;
            paymentSubjectText = jQuery("#payment-subjects-text").val();

            if (weighingNote && !rko) {
                TF.Rpc.Payments.ExportPayment.exportToPdfWeighingNote(
                    transaction_id,
                    orderWithDate
                )
                    .done(function(dataObj) {
                        winDownload.window("open");
                        var path = dataObj.file_path;
                        _pathFile = path;
                        _fileName = dataObj.file_name;
                        downloadFile.attr("href", path);
                    })
                    .fail(function(errorObj) {});
                return;
            } else if (!weighingNote && rko) {
                TF.Rpc.Payments.ExportPayment.exportToPdfPaymentOrder(
                    transaction_id,
                    null,
                    orderWithDate,
                    paymentSubjectData,
                    paymentSubjectText,
                    false,
                    false,
                    collection_id,
                    combineDocument,
                    collectAllPaymentAmounts,
                    rko_type,
                    rko_type_declaration,
                    JSON.stringify(paymentsParams)
                )
                    .done(function(dataObj) {
                        winDownload.window("open");
                        var path = dataObj.file_path;
                        _pathFile = path;
                        _fileName = dataObj.file_name;
                        downloadFile.attr("href", path);
                    })
                    .fail(function(errorObj) {});
                return;
            } else if (weighingNote && rko) {
                TF.Rpc.Payments.ExportPayment.exportToPdfCombined(
                    transaction_id,
                    orderWithDate,
                    paymentSubjectData,
                    paymentSubjectText,
                    [],
                    combineDocument,
                    collectAllPaymentAmounts,
                    rko_type,
                    rko_type_declaration,
                    JSON.stringify(paymentsParams)
                )
                    .done(function(dataObj) {
                        winDownload.window("open");
                        var path = dataObj.file_path;
                        _pathFile = path;
                        _fileName = dataObj.file_name;
                        downloadFile.attr("href", path);
                    })
                    .fail(function(errorObj) {});
                return;
            } else if (weighingNoteOnly) {
                TF.Rpc.Payments.ExportPayment.exportToPdfWeighingNote(
                    transaction_id,
                    true
                )
                    .done(function(dataObj) {
                        winDownload.window("open");
                        var path = dataObj.file_path;
                        _pathFile = path;
                        _fileName = dataObj.file_name;
                        downloadFile.attr("href", path);
                    })
                    .fail(function(errorObj) {});
                return;
            }
        }
    }
}

function printCreditPaymentOrder() {
    var url = jQuery("#credit-payment-order-iframe").attr("src");
    window.open(url, "_blank").print();
}

function transactionsFilter() {
    var obj = {};

    obj.id = jQuery("#search-transaction-id").val();
    obj.c_num = jQuery("#search-contract-num").val();
    obj.c_num_complete_match = jQuery("#search-c_num-complete-match").is(
        ":checked"
    );
    obj.owners_list = transactionsOptionListOwners;
    obj.transaction_start_date = jQuery(
        "#search-transaction-start-date"
    ).datebox("getValue");
    obj.transaction_due_date = jQuery("#search-transaction-due-date").datebox(
        "getValue"
    );
    obj.transaction_types = jQuery("#search-transaction-type").combobox(
        "getValues"
    );
    obj.paid_to = jQuery("#search-transaction-paid-to").val();
    obj.paid_to_egn = jQuery("#search-transaction-paid-to-egn").val();
    obj.paid_by = jQuery("#search-transaction-paid-by").combobox("getValue");
    obj.cancelled_by = jQuery("#search-transaction-disabled-by").combobox(
        "getValue"
    );
    obj.farming_year = jQuery("#search-transaction-farming-year").combobox(
        "getValues"
    );
    obj.farming_id = jQuery("#search-transaction-farming").combobox("getValue");

    initTransactionsGrid(obj);
}

function clearTransactionFilterFields() {
    var obj = {};

    obj.id = "";
    obj.transaction_start_date = "";
    obj.transaction_due_date = "";
    obj.transaction_types = "";
    obj.paid_to = "";
    obj.paid_to_egn = "";
    obj.paid_by = "";
    obj.cancelled_by = "";
    obj.farming_year = "";
    obj.owners_list = [];

    jQuery("#search-transaction-type").combobox("setValue", "");
    jQuery("#search-transaction-paid-by").combobox("setValue", "");
    jQuery("#search-transaction-disabled-by").combobox("setValue", "");
    jQuery("#search-transaction-farming").combobox("setValue", "");

    var tmpTransactionsOptionListOwners = transactionsOptionListOwners.slice(0);
    for (var i = 0; i < tmpTransactionsOptionListOwners.length; i++) {
        jQuery("#transactions-filter-owner-name > input").trigger(
            "removeItem",
            [{ value: tmpTransactionsOptionListOwners[i] }]
        );
    }

    transactionsOptionListOwners = [];
    jQuery("#transactions-tables").datagrid({
        rpcParams: [
            {
                data: obj
            }
        ],
        loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
        loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
    });
}

function onDownloadWindowClose() {
    TF.Rpc.Plots.PlotsDeclarations.removeFile(_fileName).done();
}

function initSearchOnEnter() {
    jQuery("#win-contracts-filter").off("keyup").on("keyup", function (event) {
		// Listen for enter key
        if (13 !== event.keyCode) {
			return;
		}
		jQuery("#btn-filter-contracts-tree").click()
    });
}

function setTootip(tooltip, value) {
    return  '<span title=\"' + tooltip + '\" class=\"easyui-tooltip\" style="text-decoration: underline;"">'+value+'</span>';
}