var _pathFile = "";

function initChargedRentaHistoryGrid(params_id, pageNumber) {
	var page_number = pageNumber || 1;
	var chargedRentaGrid = jQuery('#charged-ranta-history-tables');
	var chargedRentaTree = jQuery('#charged-renta-history-tree');

	var toolbar = [
		{
			id: 'btnexportexcelhistory',
			text: 'Изтегли',
			iconCls: 'icon-csv',
			handler: function() {
				var getSelected = chargedRentaTree.tree('getSelected');

				if(getSelected) {
					var rows = chargedRentaGrid.datagrid('getRows');
					if(rows.length == 0){
						jQuery.messager.alert('Грешка', 'Няма засегнати имоти!', 'error');
						return;
					}

					var type = getSelected.text;
					var id = getSelected.id;

					TF.Rpc.Payments
						.ChargedRentaHistoryGrid
						.expChargedRentaHistory('exl', id, type)
						.done(function (dataObj) {
							jQuery('#win-download').window('open');                                
							var path = dataObj.file_path;
							_pathFile = path;
							_fileName = dataObj.file_name;
							jQuery('#btn-download-file').attr("href", path);
						})
						.fail(function (errorObj) {});
				} else {
					jQuery.messager.alert('Грешка', 'Моля изберете Тип!', 'error');
				}
			}
		}
	];

	initChargedRentaGrid(chargedRentaGrid, 'charged-renta-history-grid','read', params_id,true, toolbar, page_number, );
}

function initChargedRentaPrevGrid() {
	var chargedRentaGrid = jQuery('#charged-renta-prev-table');
	var rpcParams = getAddRentaFieldValues();
	if(!rpcParams) return false;
	jQuery('#charged-renta-prev-win').window('open');

	var toolbar = [
		{
			id: 'btnexportexcelhistory',
			text: 'Изтегли',
			iconCls: 'icon-csv',
			handler: function () {
				var rows = chargedRentaGrid.datagrid('getRows');
				if (rows.length == 0) {
					jQuery.messager.alert('Грешка', 'Няма засегнати имоти!', 'error');
					return;
				}
				TF.Rpc.Payments
					.AddChargedRenta
					.prevChargedRentaExport(rpcParams)
					.done(function (dataObj) {


						jQuery('#win-download').window('open');                                
						var path = dataObj.file_path;

						_fileName = dataObj.file_name;
						jQuery('#btn-download-file').attr("href", path);
					})
					.fail(function (errorObj) {
					});
			}
		}
	];

	initChargedRentaGrid(chargedRentaGrid, 'add-charged-renta', 'prevChargedRenta', rpcParams, false, toolbar);
}

function initChargedRentaGrid(chargedRentaGrid, serviceId = 'charged-renta-history-grid', rpcMethod = 'read', rpcParams, pagination = true, toolbar = [], pageNumber = 1) {
	chargedRentaGrid.datagrid({
		title: 'Засегнати имоти',
		iconCls: 'icon-tree-edit-geometry',
		nowrap: true,
		fit: true,
		fitColumns: true,
		showFooter: true,
		url: 'index.php?payments-rpc=' + serviceId,
		rpcMethod: rpcMethod,
		rpcParams: [rpcParams],
		rownumbers: true,
		sortName: 'id',
		sortOrder: 'asc',
		page: pageNumber,
		pageSize: 30,
		pagination: pagination,
		idField: 'id',
		columns: [[
			{
				field: 'ekate',
				title: '<b>Землище</b>',
				sortable: true,
				nowrap: false,
				width: 130,
				align: 'center',
				styler: cellStyler
			}, {
				field: 'mestnost',
				title: '<b>Местност</b>',
				sortable: true,
				width: 150,
				align: 'center',
				styler: cellStyler
			}, {
				field: 'kad_ident',
				title: '<b>Име на парцел</b>',
				sortable: true,
				width: 130,
				align: 'center',
				styler: cellStyler
			}, {
				field: 'category',
				title: '<b>Категория</b>',
				sortable: true,
				width: 110,
				align: 'center',
				styler: cellStyler
			}, {
				field: 'contract_id',
				title: '<b>Договор</b>',
				sortable: true,
				width: 80,
				align: 'center',
				styler: cellStyler
			}, {
				field: 'owner_id',
				title: '<b>Собственик</b>',
				sortable: true,
				width: 180,
				align: 'center',
				styler: cellStyler
			}, {
				field: 'owner_area',
				title: '<b>Използвана</br> площ (дка)</b>',
				sortable: true,
				width: 80,
				align: 'center',
				styler: cellStyler
			}, {
				field: 'charged_renta_txt',
				title: '<b>Начислена</br> сума</b>',
				sortable: true,
				width: 80,
				align: 'center',
				styler: cellStyler
			}, {
				field: 'charged_renta_nat',
				title: '<b>Начислено количество</b>',
				sortable: true,
				width: 150,
				align: 'center',
				styler: cellStyler
			},
		]],
		toolbar: toolbar,
		loader: EasyUIRPCLoaders.EasyUIGridCustomLoader.loader,
		loadFilter: EasyUIRPCLoaders.EasyUIGridCustomLoader.loadFilter
	});

	function cellStyler(value, row) {
		if (row.has_prev_charged_renta) {
			return 'background-color:#f5978f;';
		}

		if (!value) {
			return 'background-color:#99CCCC;';
		}
	}
}