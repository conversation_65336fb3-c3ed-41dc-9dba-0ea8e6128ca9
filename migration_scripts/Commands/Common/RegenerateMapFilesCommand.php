<?php

namespace TF\Commands\Common;

use PDO;
use TF\Engine\Plugins\Core\Layers\LayersController;
use TF\Engine\Plugins\Core\Users\UsersController;

/**
 * TS-515 command run on all databases.
 */
class RegenerateMapFilesCommand extends UserDbCommand
{
    /** @var TShellApplication */
    protected $pradoApp = false;
    protected $skipLogging = true;

    public function setPradoApp($pradoApp)
    {
        $this->pradoApp = $pradoApp;
    }

    public function getPradoApp()
    {
        return $this->pradoApp;
    }

    protected function configure()
    {
        parent::configure();
        $this
            ->setName('tf:regenerate_map_files')
            ->setDescription('Regenerate map files.');
    }

    protected function onDbExecute(PDO $pdo, $output, $input)
    {
        /** @var MTAuthManager $auth */
        $auth = $this->pradoApp->getModule('auth');
        $UsersController = new UsersController('Users');
        $users = $UsersController->getUsers([
            'return' => ['id', 'username', 'name', 'paid_support', 'keycloak_uid'],
            'where' => [
                'database' => ['column' => 'database', 'compare' => '=', 'value' => $this->userDbName],
                'level' => ['column' => 'level', 'compare' => '=', 'value' => 3],
                'keycloak_uid' => ['column' => 'keycloak_uid', 'compare' => 'is not', 'value' => null],
            ],
        ]);

        [$firstUser] = $users;
        if (!$firstUser['keycloak_uid']) {
            $output->writeln('No keycloak_uid found for user ' . $firstUser['username']);

            return;
        }
        $auth->switchUser($firstUser['username']);

        $dbhDev = new PDO('pgsql:host=' . DEFAULT_DB_HOST . ';port=' . DEFAULT_DB_PORT . ';dbname=' . DEFAULT_DB_DATABASE . ';', DEFAULT_DB_USERNAME, DEFAULT_DB_PASSWORD);
        $sql = $dbhDev->prepare('SELECT
                    DISTINCT(group_id), 
                    datname AS database
                FROM
                    pg_database d
                INNER JOIN su_users u ON d.datname = u.database
                AND datistemplate = FALSE
                AND u. database = :userdb');
        $sql->bindParam(':userdb', $this->userDbName);
        $sql->execute();
        $results = $sql->fetchAll();

        $LayersController = new LayersController('Layers');
        for ($i = 0; $i < count($results); $i++) {
            $options = [
                'user_id' => $results[$i]['group_id'],
                'database' => $results[$i]['database'],
            ];

            $LayersController->generateMapFile($options);
        }
    }
}
