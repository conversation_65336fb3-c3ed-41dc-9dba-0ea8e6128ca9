<?php

namespace TF\Commands\v5_1;

use TF\Commands\Common\UserDbCommand;

/**
 * Runs all scripts required for migration from Version 5 to 5.1.
 */
class UpdateToVersion5_1 extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v5.1:update-to-version-5.1')
            ->setDescription('Runs all scripts required for migration from version 5 to version 5.1');
    }

    protected function onDbExecute($pdo, $output, $input)
    {
        $commandsArr = [
        ];

        $count = count($commandsArr);
        for ($i = 0; $i < $count; $i++) {
            $command = $commandsArr[$i];
            $this->logScript($this->userDbName, $command->getName());
            $output->writeln('Executing command: ' . $command->getName());
            $command->onDbExecute($pdo, $output, $input);
        }
    }

    protected function logScript($user_db, $name = '', $status = 'success')
    {
        $date = date('Y-m-d H:i:s');

        $this->openConnection();

        $sql = $this->mainConnection->prepare("
            INSERT INTO scripts_log (db_name, name, date)
            VALUES ('{$user_db}', '{$name}', '{$date}')
        ");

        return $sql->execute();
    }
}
