<?php

namespace TF\Commands\v5;

use TF\Commands\Common\UserDbCommand;

/**
 * Runs all scripts required for migration from Version 4.1 to 4.2.
 */
class UpdateToVersion5 extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v5:update-to-version-5')
            ->setDescription('Runs all scripts required for migration from version 4.2 to version 5');
    }

    protected function onDbExecute($pdo, $output, $input)
    {
        $commandsArr = [
            new GPS4520Command(),
            new GPS4553Command(),
            new GPS4521Command(),
            new GPS4606Command(),
            new GPS4713Command(),
            new GPS4716Command(),
            new GPS4664Command(),
            new GPS4742_1Command(),
            new GPS4742_2Command(),
            new GPS4742_3Command(),
            new GPS4742_4Command(),
            new GPS4742_5Command(),
            new GPS4808_1Command(),
            new GPS4808_2Command(),
            new GPS4878_1Command(),
            new GPS4878_2Command(),
            new GPS4878_3Command(),
            new GPS4896_1Command(),
            new GPS4896_2Command(),
            new GPS4896_3Command(),
            new GPS4896_4Command(),
            new GPS4918Command(),
            new GPS4952Command(),
            new GPS4959Command(),
            new GPS4958Command(),
            new GPS4950Command(),
            new GPS4936Command(),
            new GPS4926_1Command(),
            new GPS4926_2Command(),
            new GPS4926_3Command(),
            new GPS4926_4Command(),
            new GPS4926_5Command(),
            new GPS4926_6Command(),
            new GPS4926_7Command(),
            new GPS4926_8Command(),
            new GPS4926_9Command(),
            new GPS4926_10Command(),

            new GPS5006Command(),
            new GPS5039Command(),
            new GPS5064Command(),
            new GPS4862Command(),
            new GPS5084Command(),
            new GPS5127Command(),
            new GPS5161Command(),
            new GPS5080Command(),
        ];

        $count = count($commandsArr);
        for ($i = 0; $i < $count; $i++) {
            $command = $commandsArr[$i];
            $this->logScript($this->userDbName, $command->getName());
            $output->writeln('Executing command: ' . $command->getName());
            $command->onDbExecute($pdo, $output, $input);
        }
    }

    protected function logScript($user_db, $name = '', $status = 'success')
    {
        $date = date('Y-m-d H:i:s');

        $this->openConnection();

        $sql = $this->mainConnection->prepare("
            INSERT INTO scripts_log (db_name, name, date)
            VALUES ('{$user_db}', '{$name}', '{$date}')
        ");

        return $sql->execute();
    }
}
