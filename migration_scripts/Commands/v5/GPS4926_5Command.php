<?php

namespace TF\Commands\v5;

use PDO;
use TF\Commands\Common\UserDbCommand;

class GPS4926_5Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v5:GPS-4926_5')
            ->setDescription('Add colums pc_rel_id and plot_rent_id in table su_charged_renta');
    }

    protected function onDbExecute(PDO $pdo, $output, $input)
    {
        $pdo->exec('ALTER TABLE public.su_charged_renta ADD pc_rel_id int4 NULL;');
        $pdo->exec('ALTER TABLE public.su_charged_renta ADD CONSTRAINT su_charged_renta_su_contracts_plots_rel_fk FOREIGN KEY (pc_rel_id) REFERENCES public.su_contracts_plots_rel(id) ON DELETE CASCADE;');
        // $pdo->exec('ALTER TABLE public.su_charged_renta ALTER COLUMN pc_rel_id SET NOT NULL;');

        $pdo->exec('ALTER TABLE public.su_charged_renta ADD plot_rent_id int4 NULL;');
        $pdo->exec('ALTER TABLE public.su_charged_renta ADD CONSTRAINT su_charged_renta_su_plots_rents_fk FOREIGN KEY (plot_rent_id) REFERENCES public.su_plots_rents(id) ON DELETE CASCADE;');
    }
}
