<?php

namespace TF\Commands\v5;

use PDO;
use TF\Commands\Common\UserDbCommand;

/**
 * Revert all scripts required for specific rents migrations.
 */
class RevertSpecificRentsMigrations extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v5:revert-specific-rents-migrations')
            ->setDescription('Revert all scripts required for specific rents');
    }

    protected function onDbExecute(PDO $pdo, $output, $input)
    {
        $pdo->exec('ALTER TABLE public.su_charged_renta DROP CONSTRAINT IF EXISTS su_charged_renta_su_contracts_plots_rel_fk;');
        $pdo->exec('ALTER TABLE public.su_charged_renta DROP CONSTRAINT IF EXISTS su_charged_renta_su_plots_rents_fk;');
        $pdo->exec('ALTER TABLE public.su_charged_renta DROP COLUMN IF EXISTS pc_rel_id;');
        $pdo->exec('ALTER TABLE public.su_charged_renta DROP COLUMN IF EXISTS plot_rent_id;');
        $pdo->exec('ALTER TABLE public.su_contracts_plots_rel DROP COLUMN IF EXISTS virtual_non_arable_area;');
        $pdo->exec('ALTER TABLE su_charged_renta_params DROP COLUMN IF EXISTS sr_category;');
        $pdo->exec('ALTER TABLE su_charged_renta_params DROP COLUMN IF EXISTS sr_ntp;');
        $pdo->exec('ALTER TABLE su_charged_renta_params DROP COLUMN IF EXISTS sr_arable;');
        $pdo->exec('DROP TABLE IF EXISTS public.su_renta_types_options;');
        $pdo->exec('DROP TABLE IF EXISTS public.su_plots_rents;');
        $pdo->exec('DROP TABLE IF EXISTS public.su_contracts_rents_types;');
        $pdo->exec('DROP TYPE IF EXISTS public.plot_renta_type_enum;');
    }
}
