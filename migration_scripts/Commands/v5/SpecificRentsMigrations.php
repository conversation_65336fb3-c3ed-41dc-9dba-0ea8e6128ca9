<?php

namespace TF\Commands\v5;

use PDO;
use TF\Commands\Common\UserDbCommand;

/**
 * Runs all scripts required for specific rents.
 */
class SpecificRentsMigrations extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v5:specific-rents-migrations')
            ->setDescription('Runs all scripts required for specific rents');
    }

    protected function onDbExecute(PDO $pdo, $output, $input)
    {
        $commandsArr = [
            new GPS4926_1Command(),
            new GPS4926_5Command(),
            new GPS4926_8Command(),
            new GPS4926_9Command(),
            new GPS4926_10Command(),
        ];

        foreach ($commandsArr as $command) {
            $output->writeln('Executing command: ' . $command->getName());

            $command->onDbExecute($pdo, $output, $input);

            $this->logScript($this->userDbName, $command->getName());
        }
    }

    protected function logScript($user_db, $name = '', $status = 'success')
    {
        $date = date('Y-m-d H:i:s');

        $this->openConnection();

        $sql = $this->mainConnection->prepare("
            INSERT INTO scripts_log (db_name, name, date)
            VALUES ('{$user_db}', '{$name}', '{$date}')
        ");

        return $sql->execute();
    }
}
