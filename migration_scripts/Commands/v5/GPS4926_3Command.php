<?php

namespace TF\Commands\v5;

use PDO;
use TF\Commands\Common\UserDbCommand;

class GPS4926_3Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v5:GPS-4926_3')
            ->setDescription('Create functions restriction_area_su_plots_rents and restriction_area_for_rent_su_contracts_plots_rel');
    }

    protected function onDbExecute(PDO $pdo, $output, $input)
    {
        $pdo->exec(
            "CREATE OR REPLACE FUNCTION public.restriction_area_su_plots_rents()
                RETURNS trigger
                LANGUAGE plpgsql
                AS $$
                DECLARE
                    total_rented_area numeric := 0;
                    allowed_area numeric := 0;
                    new_area numeric := COALESCE(NEW.area, 0);
                    old_area numeric := COALESCE(OLD.area, 0);
                BEGIN
                    -- Изчисляване на общата наета площ, без текущия запис (ако UPDATE)
                    SELECT COALESCE(SUM(area), 0)
                    INTO total_rented_area
                    FROM su_plots_rents
                    WHERE pc_rel_id = NEW.pc_rel_id
                    AND (id <> NEW.id OR NEW.id IS NULL); -- изключваме текущия ред ако е UPDATE

                    -- Вземи позволената площ
                    SELECT area_for_rent::numeric
                    INTO allowed_area
                    FROM su_contracts_plots_rel
                    WHERE id = NEW.pc_rel_id;

                    -- Прибавяме новата площ и сравняваме
                    IF total_rented_area + new_area > allowed_area THEN
                        RAISE EXCEPTION
                            'Общата площ на наеманите парцели (%) надвишава позволената площ (%) за тази връзка.',
                            total_rented_area + new_area, allowed_area;
                    END IF;

                    RETURN NULL;
                END;
                $$;"
        );

        $pdo->exec(
            "CREATE OR REPLACE FUNCTION public.restriction_area_for_rent_su_contracts_plots_rel()
            RETURNS trigger
            LANGUAGE plpgsql
            AS $$
            DECLARE
                total_rented_area numeric := 0;
                new_allowed_area numeric := COALESCE(NEW.area_for_rent, 0);
            BEGIN
                -- Пресмята сумата на вече наетите площи за тази релация
                SELECT COALESCE(SUM(sp.area), 0)
                INTO total_rented_area
                FROM su_plots_rents sp
                WHERE sp.pc_rel_id = NEW.id;

                -- Проверка дали новата разрешена площ е по-малка от вече разпределената
                IF new_allowed_area < total_rented_area THEN
                    RAISE EXCEPTION
                        'Новата стойност за \"area_for_rent\" (%) е по-малка от вече наетата площ (%)',
                        new_allowed_area, total_rented_area;
                END IF;

                RETURN NEW;
            END;
            $$;"
        );
    }
}
