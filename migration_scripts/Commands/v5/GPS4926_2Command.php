<?php

namespace TF\Commands\v5;

use PDO;
use TF\Commands\Common\UserDbCommand;

class GPS4926_2Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v5:GPS-4926_2')
            ->setDescription('Fill table su_plots_rents with the data from su_contracts_plots_rel');
    }

    protected function onDbExecute(PDO $pdo, $output, $input)
    {
        // $pdo->exec(
        //     "INSERT INTO su_plots_rents (id, pc_rel_id, area, type, value, rents)
        //         SELECT
        //             cp.id,
        //             cp.id AS pc_rel_id,
        //             cp.area_for_rent,
        //             'generic'::plot_renta_type_enum AS type,
        //             NULL::varchar AS value,
        //             jsonb_build_object(
        //                 'money', c.renta,
        //                 'overall_money', c.overall_renta,
        //                 'rent_per_plot', cp.rent_per_plot,
        //                 'rent_kinds', coalesce(jsonb_agg(
        //                     jsonb_build_object(
        //                         'id', cr.renta_id,
        //                         'value', cr.renta_value
        //                     )
        //                 ), '[]'::jsonb)
        //             ) AS rents
        //         FROM
        //             su_contracts_plots_rel cp
        //         JOIN
        //             su_contracts c ON cp.contract_id = c.id
        //         LEFT JOIN
        //             su_contracts_rents cr ON c.id = cr.contract_id
        //         GROUP BY
        //             cp.id, c.renta, c.overall_renta;"
        // );
    }
}
