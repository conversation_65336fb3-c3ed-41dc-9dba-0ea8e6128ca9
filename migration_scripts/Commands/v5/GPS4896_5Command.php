<?php

namespace TF\Commands\v5;

use PDO;
use TF\Application\Common\Config;
use TF\Commands\Common\MainDbCommand;

class GPS4896_5Command extends MainDbCommand
{
    public function onDbExecute(PDO $pdo, $output, $input)
    {
        $layerType = Config::LAYER_TYPE_CSD;

        $pdo->exec("WITH 
            layers_definitions AS (
                SELECT
                    sul.id AS layer_id,
                    JSONB_AGG(
                        CASE 
                            WHEN d->>'col_name' = 'number_of_documents'  THEN d || '{\"col_category\": \"text\"}'::JSONB
                            ELSE d
                        END
                    ) AS new_definitions
                FROM 
                    su_users_layers AS sul,
                    jsonb_array_elements(definitions) AS d
                WHERE
                    layer_type = {$layerType}
                GROUP BY
                    sul.id
            )
            UPDATE su_users_layers
            SET
                definitions = new_definitions
            FROM 
                layers_definitions
            WHERE
                id = layer_id
        ");
    }

    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v5:GPS-4896-5')
            ->setDescription('Update definitions for layer of type LAYER_TYPE_CSD - change the category of column number_of_documents from number to text (jsonb)');
    }
}
