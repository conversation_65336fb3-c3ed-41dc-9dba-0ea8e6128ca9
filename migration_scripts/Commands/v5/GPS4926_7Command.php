<?php

namespace TF\Commands\v5;

use PDO;
use TF\Commands\Common\UserDbCommand;

class GPS4926_7Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v5:GPS-4926_7')
            ->setDescription('Create table su_contracts_rents_types');
    }

    protected function onDbExecute(PDO $pdo, $output, $input)
    {
        $pdo->exec(
            'CREATE TABLE public.su_contracts_rents_types (
            id serial4 NOT NULL,
            contract_id int4 NOT NULL,
            "type" public."plot_renta_type_enum" NOT NULL,
            value varchar NOT NULL,
            rents jsonb NOT NULL,
            created_at timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL,
            updated_at timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL,
            CONSTRAINT su_contracts_rents_types_pk PRIMARY KEY (id),
            CONSTRAINT su_contracts_rents_types_su_contracts_fk FOREIGN KEY (contract_id) REFERENCES public.su_contracts(id) ON DELETE CASCADE);'
        );

        // $pdo->exec(
        //     'create trigger set_updated_at before
        //     update
        //         on
        //         public.su_contracts_rents_types for each row execute function update_updated_at_column();'
        // );
    }
}
