<?php

namespace TF\Commands\v5;

use PDO;
use TF\Commands\Common\UserDbCommand;

class GPS4926_8Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v5:GPS-4926_8')
            ->setDescription('Create table su_renta_types_options and fill it with data');
    }

    protected function onDbExecute(PDO $pdo, $output, $input)
    {
        $pdo->exec(
            'CREATE TABLE public.su_renta_types_options (
                            id serial4 NOT NULL,
                            title varchar NOT NULL,
                            value public."plot_renta_type_enum" NOT NULL,
                            CONSTRAINT su_renta_types_options_pkey PRIMARY KEY (id)
                        );'
        );

        $pdo->exec(
            "INSERT INTO public.su_renta_types_options (title, value) VALUES 
                            ('Категория', 'category'),
                            ('НТП', 'ntp'),
                            ('Обработваема площ', 'arable');"
        );
    }
}
