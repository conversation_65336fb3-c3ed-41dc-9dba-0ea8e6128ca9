<?php

namespace TF\Commands\v5;

use PDO;
use TF\Commands\Common\UserDbCommand;

/**
 * GPS4926Command run on main database.
 */
class GPS4926_1Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v5:GPS-4926_1')
            ->setDescription('Create an enum plot_renta_type_enum and table su_plots_rents');
    }

    protected function onDbExecute(PDO $pdo, $output, $input)
    {
        $pdo->exec(
            "CREATE TYPE public.plot_renta_type_enum AS ENUM (
            'category',
            'ntp',
            'arable',
            'generic');"
        );

        $pdo->exec(
            'CREATE TABLE public.su_contracts_rents_types (
            id serial4 NOT NULL,
            contract_id int4 NOT NULL,
            "type" public."plot_renta_type_enum" NOT NULL,
            value varchar NOT NULL,
            rents jsonb NOT NULL,
            created_at timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL,
            updated_at timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL,
            CONSTRAINT su_contracts_rents_types_pk PRIMARY KEY (id),
            CONSTRAINT su_contracts_rents_types_su_contracts_fk FOREIGN KEY (contract_id) REFERENCES public.su_contracts(id) ON DELETE CASCADE);'
        );

        $pdo->exec(
            'CREATE TABLE public.su_plots_rents (
                            id serial4 NOT NULL,
                            pc_rel_id int4 NOT NULL,
                            rent_type_id int4 NULL,
                            area numeric NOT NULL,
                        CONSTRAINT su_plots_rents_pk PRIMARY KEY (id),
                        CONSTRAINT su_plots_rents_su_contracts_plots_rel_fk FOREIGN KEY (pc_rel_id) REFERENCES public.su_contracts_plots_rel(id) ON DELETE CASCADE,
                        CONSTRAINT su_plots_rents_su_contracts_rents_types_fk FOREIGN KEY (rent_type_id) REFERENCES public.su_contracts_rents_types(id) ON DELETE CASCADE
                    );'
        );
    }
}
