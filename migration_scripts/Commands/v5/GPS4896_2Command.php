<?php

namespace TF\Commands\v5;

use PDO;
use TF\Commands\Common\UserDbCommand;

class GPS4896_2Command extends UserDbCommand
{
    public function onDbExecute(PDO $pdo, $output, $input)
    {
        $pdo->exec("ALTER TYPE contract_status_enum ADD VALUE IF NOT EXISTS 'Upcoming';");

        $pdo->exec('DROP FUNCTION IF EXISTS get_contract_status;');

        // Update function for getting contract status to use new status 'Upcoming'
        $pdo->exec("CREATE FUNCTION get_contract_status(
                contract_id int,
                active boolean,
                start_date timestamp without time zone,
                due_date timestamp without time zone,
                check_annexes boolean DEFAULT true
            ) 
            RETURNS contract_status_enum 
            IMMUTABLE 
            AS $$       
            DECLARE
                contract_status contract_status_enum;
                annexed_contract_count int := 0;
            BEGIN
                IF check_annexes THEN
                    SELECT COUNT(*) INTO annexed_contract_count
                    FROM su_contracts AS c
                    WHERE c.parent_id = contract_id AND c.active = TRUE;
                END IF;

                contract_status := CASE 
                    WHEN check_annexes AND annexed_contract_count > 0 THEN 'Annexed'::contract_status_enum
                    WHEN active = FALSE THEN 'Canceled'::contract_status_enum
                    WHEN active = TRUE AND start_date > now() THEN 'Upcoming'::contract_status_enum
                    WHEN active = TRUE AND (COALESCE(due_date, '9999-01-01')::date >= now()::date) THEN 'Active'::contract_status_enum
                    ELSE 'Expired'::contract_status_enum
                END;

                RETURN contract_status;
            END;
            $$ LANGUAGE plpgsql;
        ");

        $pdo->exec('ALTER TABLE su_sales_contracts DROP COLUMN IF EXISTS virtual_contract_status;');
        $pdo->exec('ALTER TABLE su_sales_contracts ADD COLUMN virtual_contract_status contract_status_enum GENERATED ALWAYS AS (get_contract_status(id, active, start_date, due_date, false)) STORED;');

        $pdo->exec('ALTER TABLE su_contracts DROP COLUMN IF EXISTS virtual_contract_status;');
        $pdo->exec('ALTER TABLE su_contracts ADD COLUMN virtual_contract_status contract_status_enum GENERATED ALWAYS AS (get_contract_status(id, active, start_date, due_date)) STORED;');
    }

    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v5:GPS-4896-2')
            ->setDescription('Add \'Upcoming\' value to contract_status_enum and create update db function get_contract_status to use the new value. Update the virtual columns using this function.');
    }
}
