<?php

namespace TF\Commands\v5;

use PDO;
use TF\Commands\Common\UserDbCommand;

class GPS4926_6Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v5:GPS-4926_6')
            ->setDescription('Add colums arable_renta in table su_contracts');
    }

    protected function onDbExecute(PDO $pdo, $output, $input)
    {
        $pdo->exec('ALTER TABLE public.su_contracts ADD arable_renta bool DEFAULT false NOT NULL;');
    }
}
