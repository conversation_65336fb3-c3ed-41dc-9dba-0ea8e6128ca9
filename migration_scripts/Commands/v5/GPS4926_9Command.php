<?php

namespace TF\Commands\v5;

use PDO;
use TF\Commands\Common\UserDbCommand;

class GPS4926_9Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v5:GPS-4926_9')
            ->setDescription('Add virtual_non_arable_area column to su_contracts_plots_rel table');
    }

    protected function onDbExecute(PDO $pdo, $output, $input)
    {
        $pdo->exec(
            'ALTER TABLE public.su_contracts_plots_rel
                        ADD COLUMN virtual_non_arable_area float4 GENERATED ALWAYS AS (
                            area_for_rent - kvs_allowable_area
                        ) STORED;'
        );
    }
}
