<?php

namespace TF\Commands\v5;

use PDO;
use TF\Commands\Common\UserDbCommand;

class GPS4896_3Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v5:GPS-4896-3')
            ->setDescription('Create db functions user_contracts_count_by_plot_and_status and plot_number_of_contracts');
    }

    protected function onDbExecute(PDO $pdo, $output, $input)
    {
        $pdo->exec('DROP FUNCTION IF EXISTS user_contracts_count_by_plot_and_status(keycloak_uid TEXT);');

        $pdo->exec("CREATE FUNCTION user_contracts_count_by_plot_and_status(
                keycloak_uid TEXT
            ) RETURNS TABLE (
                plot_id int,
                contracts_count_by_status jsonb
            )
            AS $$
                BEGIN
                    RETURN QUERY 
                    WITH plot_contract_status AS (
                    -- Main Contracts
                        SELECT 
                            kvs.gid,
                            c.id AS contract_id,
                            c.farming_id,
                            get_contract_status(c.id, c.active, c.start_date, c.due_date) AS status
                        FROM
                            layer_kvs AS kvs
                        JOIN su_contracts_plots_rel AS scpr
                            ON scpr.plot_id = kvs.gid
                        JOIN su_contracts AS c
                            ON c.id = scpr.contract_id
                            AND c.nm_usage_rights <> 4
                        UNION ALL
                        -- Sublease Contracts
                        SELECT 
                            kvs.gid,
                            sbc.id AS contract_id,
                            sbc.farming_id,
                            get_contract_status(sbc.id, sbc.active, sbc.start_date, sbc.due_date, false) AS contract_status
                        FROM
                            layer_kvs AS kvs
                        JOIN su_contracts_plots_rel AS scpr
                            ON scpr.plot_id = kvs.gid
                        JOIN su_subleases_plots_contracts_rel AS sspcr
                            ON sspcr.pc_rel_id = scpr.id
                        JOIN su_contracts AS sbc
                            ON sbc.id = sspcr.sublease_id
                        UNION ALL
                        -- Sales Contracts
                        SELECT 
                            kvs.gid,
                            sac.id AS contract_id,
                            sac.farming_id,
                            get_contract_status(sac.id, sac.active, sac.start_date, sac.due_date, false) AS contract_status
                        FROM
                            layer_kvs AS kvs
                        JOIN su_contracts_plots_rel AS scpr
                            ON scpr.plot_id = kvs.gid
                        JOIN su_sales_contracts_plots_rel AS sscpr
                            ON sscpr.pc_rel_id = scpr.id
                        JOIN su_sales_contracts AS sac
                            ON sac.id = sscpr.sales_contract_id
                    ),
                    number_of_contracts_by_status AS (
                        SELECT 
                            pcs.gid,
                            pcs.status,
                            COALESCE(count(pcs.contract_id), 0) AS count
                        FROM 
                            plot_contract_status AS pcs
                        JOIN user_farming_permissions AS ufp
                            ON ufp.farming_id = pcs.farming_id
                            AND ufp.\"permission\" = 'read'
                            AND ufp.keycloak_uid::TEXT=user_contracts_count_by_plot_and_status.keycloak_uid
                        GROUP BY
                            pcs.gid, pcs.status
                    )
                        SELECT
                            gid AS plot_id,
                            JSONB_OBJECT_AGG(status, count) ||
                            jsonb_build_object(
                                'All', sum(count)
                            ) AS contracts_count_by_status
                        FROM 
                            number_of_contracts_by_status
                        GROUP BY gid;
                END;
            $$ LANGUAGE plpgsql;
        ");

        // Note:
        // This function is a wrapper for the original function, which accepts UUID as keycloak_uid.
        // In the original function the parameter keycloak_uid is of type TEXT so it does not throw an error
        // if an invalid UUID is passed, but returns null instead
        $pdo->exec('DROP FUNCTION IF EXISTS user_contracts_count_by_plot_and_status(keycloak_uid UUID);');
        $pdo->exec('CREATE FUNCTION
            user_contracts_count_by_plot_and_status(
                keycloak_uid UUID
            ) RETURNS TABLE (
                plot_id int,
                contracts_count_by_status jsonb
            ) AS $$
                BEGIN
                    RETURN QUERY 
                    SELECT * FROM user_contracts_count_by_plot_and_status(keycloak_uid::TEXT);
                END;
            $$
            LANGUAGE plpgsql;
        ');
    }
}
