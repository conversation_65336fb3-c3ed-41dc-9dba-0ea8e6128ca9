<?php

namespace TF\Commands\v5;

use PDO;
use TF\Commands\Common\UserDbCommand;

/**
 * GPS4926_10Command run on all databases.
 *
 * This command will add columns sr_category, sr_ntp and sr_arable to su_charged_renta_params table
 */
class GPS4926_10Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v5:GPS-4926_10')
            ->setDescription('Add columns sr_category, sr_ntp and sr_arable to su_charged_renta_params table');
    }

    protected function onDbExecute(PDO $pdo, $output, $input)
    {
        // Add sr_category column
        $pdo->exec('ALTER TABLE su_charged_renta_params ADD COLUMN IF NOT EXISTS sr_category varchar(255) DEFAULT NULL;');

        // Add sr_ntp column
        $pdo->exec('ALTER TABLE su_charged_renta_params ADD COLUMN IF NOT EXISTS sr_ntp varchar(255) DEFAULT NULL;');

        // Add sr_arable column
        $pdo->exec('ALTER TABLE su_charged_renta_params ADD COLUMN IF NOT EXISTS sr_arable bool DEFAULT null;');
    }
}
