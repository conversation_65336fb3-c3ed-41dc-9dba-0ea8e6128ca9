<?php

namespace TF\Commands\v5;

use PDO;
use TF\Commands\Common\UserDbCommand;

class GPS4926_4Command extends UserDbCommand
{
    protected function configure()
    {
        parent::configure();
        $this
            ->setName('v5:GPS-4926_4')
            ->setDescription('Create triggers restriction_area_su_plots_rents and restriction_area_for_rent_su_contracts_plots_rel');
    }

    protected function onDbExecute(PDO $pdo, $output, $input)
    {
        // $pdo->exec('DROP TRIGGER IF EXISTS restriction_area_su_plots_rents_trigger ON public.su_plots_rents;');
        // $pdo->exec(
        //     '
        //     CREATE CONSTRAINT TRIGGER restriction_area_su_plots_rents_trigger
        //     AFTER INSERT OR UPDATE ON public.su_plots_rents
        //     DEFERRABLE INITIALLY DEFERRED
        //     FOR EACH ROW
        //     EXECUTE FUNCTION public.restriction_area_su_plots_rents();'
        // );

        // $pdo->exec('DROP TRIGGER IF EXISTS restriction_area_for_rent_su_contracts_plots_rel_trigger ON public.su_contracts_plots_rel;');
        // $pdo->exec(
        //     '
        //     CREATE CONSTRAINT TRIGGER restriction_area_for_rent_su_contracts_plots_rel_trigger
        //     AFTER INSERT OR UPDATE ON public.su_contracts_plots_rel
        //     DEFERRABLE INITIALLY DEFERRED
        //     FOR EACH ROW
        //     EXECUTE FUNCTION public.restriction_area_for_rent_su_contracts_plots_rel();'
        // );
    }
}
