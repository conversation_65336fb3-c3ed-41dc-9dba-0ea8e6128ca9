{"Home": {"src": ["public/lib/js_external/Namespace.js", "public/lib/js/TF/Rpc/ExceptionsList.js", "public/lib/js/TF/Rpc/Exception.js", "public/lib/js/TF/Rpc/Rpc.js", "public/lib/js/TF/Rpc/Login/Login.js", "public/lib/js/TF/Rpc/Login/LoginForm.js", "public/lib/js/TF/Rpc/Login/ForgottenPassword.js", "public/lib/js_external/loading-items.js", "public/lib/js/users/users-login.js", "public/lib/js/home-track/home-track.js", "public/lib/js/TF/Rpc/Diary/Diary.js", "public/lib/js/TF/Rpc/Diary/WialonActions.js"], "dest": "public/lib/js/main/", "fileName": "home-page-items", "taskName": "concat-home-page", "taskDescription": "Конкатенира JS файловете в началната страница", "minify": true}, "MainLayoutCommon": {"src": ["public/lib/js_external/tinymce/tinymce.min.js", "public/lib/js_external/jquery.min.js", "public/lib/js_external/jquery-ui.min.js", "public/lib/js_external/underscore-min.js", "public/lib/js_external/Namespace.js", "public/lib/js_external/jquery.easyui.min.js", "public/lib/js_external/locale/easyui-lang-bg.js", "public/lib/js_external/jquery.fcbkcomplete.min.js", "public/lib/js_external/typeahead/typeahead.bundle.min.js", "public/lib/js_external/fraction.min.js", "public/lib/js_external/jsts/javascript.util.js", "public/lib/js_external/jsts/jsts.js", "public/lib/js_external/proj4js-compressed.js", "public/lib/js/defs/EPSG900913.js", "public/lib/js_external/OpenLayers.js", "public/lib/js_external/dynamicmeasure.js", "public/lib/js_external/ModifyFeature-tools.js", "public/lib/js_external/fileupload/plupload.full.js", "public/lib/js_external/fileupload/jquery.plupload.queue/jquery.plupload.queue.js", "public/lib/js_external/colorpicker/colorpicker.js", "public/lib/js_external/colorpicker/eye.js", "public/lib/js_external/colorpicker/utils.js", "public/lib/js_external/colorpicker/layout.js", "public/lib/js/TF/Loading/Loading.js", "public/lib/js_external/pdfobject.min.js"], "excludePages": ["Warehouse", "WarehouseAddTransaction", "WarehouseAddProductionTransaction", "WarehouseReturnTransaction", "WarehouseSubContragentsTransaction", "WarehouseSubPlotsTransaction", "WarehouseSubMachinesTransaction", "WarehouseTransferTransaction", "WarehouseReports"], "dest": "public/lib/js/main/", "fileName": "main-concat", "taskName": "concat-main-layout", "taskDescription": "Конкатенира общите, минифицирани библиотеки като jQuery, OpenLayers и др.", "minify": true}, "MainLayout": {"src": ["public/lib/js/TF/Rpc/ExceptionsList.js", "public/lib/js/TF/Rpc/Exception.js", "public/lib/js/TF/Rpc/Rpc.js", "public/lib/js/main/rpc_errors_handler.js", "public/lib/js/TF/Rpc/Common/Common.js", "public/lib/js/TF/Rpc/Common/RentaTypeGrid.js", "public/lib/js/TF/Rpc/Common/TemplatesGrid.js", "public/lib/js/TF/Rpc/Common/ContractTemplateVariables.js", "public/lib/js/TF/Rpc/Common/ChangePassword.js", "public/lib/js/TF/Rpc/Common/RemoveFile.js", "public/lib/js/TF/Rpc/Common/PaymentSubjectsGrid.js", "public/lib/js/TF/Rpc/Common/PaymentSubjectsCombobox.js", "public/lib/js/TF/Rpc/Common/CombinedComboboxData.js", "public/lib/js/TF/Rpc/Common/ContactForm.js", "public/lib/js/TF/Rpc/Payments/Payments.js", "public/lib/js/TF/Rpc/Payments/AddPayment.js", "public/lib/js/TF/Rpc/Notifications/Notifications.js", "public/lib/js/TF/Rpc/Notifications/NotificationsMaingrid.js", "public/lib/js/TF/Rpc/GlobalNotifications/GlobalNotifications.js", "public/lib/js/TF/Rpc/GlobalNotifications/GlobalNotificationsMaingrid.js", "public/lib/js/TF/Rpc/Contracts/Contracts.js", "public/lib/js/TF/Rpc/Contracts/ContractsExports.js", "public/lib/js/TF/Rpc/SalesContracts/SalesContracts.js", "public/lib/js/TF/Rpc/SalesContracts/SalesContractsExports.js", "public/lib/js/TF/Rpc/Login/Login.js", "public/lib/js/TF/Rpc/Login/LoginForm.js", "public/lib/js_external/egn.js", "public/lib/js/main/EasyUIRPCLoaders.js", "public/lib/js/notifications/alerts-settings.js", "public/lib/js/utilities/payment-subjects.js", "public/lib/js/utilities/contracts-templates.js", "public/lib/js/main/navigation-menu.js", "public/lib/js/main/main.js", "public/lib/js/utilities/renta-type.js", "public/lib/js/utilities/rko-start-numbering.js", "public/lib/js_external/easyui.TFextents.js", "public/lib/js/notifications/notifications-grid.js", "public/lib/js/notifications/global-notifications-grid.js"], "dest": "public/lib/js/main/", "fileName": "main", "taskName": "minify-main-layout", "taskDescription": "Конкатенира и минифицира общите файлове в MainLayout", "minify": true}, "LoginLayout": {"src": ["public/lib/js/TF/Rpc/Rpc.js", "public/lib/js/TF/Rpc/ExceptionsList.js", "public/lib/js/TF/Rpc/Exception.js", "public/lib/js/main/rpc_errors_handler.js", "public/lib/js/TF/Rpc/Login/Login.js", "public/lib/js/TF/Rpc/Login/ForgottenPassword.js", "public/lib/js/main/forgottenPassword.js", "public/lib/js/TF/Loading/Loading.js"], "dest": "public/lib/js/main/", "fileName": "main-login", "taskName": "minify-login-layout", "taskDescription": "Конкатенира и минифицира JS файлове в LoginLayout", "minify": true}, "Agreements": {"src": ["public/lib/js_external/loading-items.js", "public/lib/js/agreements/agreements-home.js", "public/lib/js/agreements/agreements-map.js", "public/lib/js/agreements/agreements-datagrid.js", "public/lib/js/agreements/agreement-layers-tree.js", "public/lib/js/TF/Rpc/Agreements/Agreements.js", "public/lib/js/TF/Rpc/Agreements/AgreementsLayersTree.js", "public/lib/js/TF/Rpc/Agreements/AgreementsDataGrid.js", "public/lib/js/TF/Rpc/Agreements/AgreementsGrid.js", "public/lib/js/TF/Rpc/Agreements/AgreementsEkateCombobox.js", "public/lib/js/TF/Rpc/Agreements/AgreementsMap.js", "public/lib/js/TF/Rpc/Users/<USER>", "public/lib/js/TF/Rpc/Users/<USER>", "public/lib/js/users/user-rights.js"], "dest": "public/lib/js/agreements/", "fileName": "agreements", "taskName": "minify-agreements-page", "taskDescription": "Конкатенира и минифицира JS файлове в 'Споразумения'", "minify": true}, "Annexes": {"src": ["public/lib/js/TF/Rpc/Annexes/Annexes.js", "public/lib/js/TF/Rpc/Annexes/AnnexesTree.js", "public/lib/js/TF/Rpc/Annexes/AnnexesFiles.js", "public/lib/js/TF/Rpc/Annexes/AnnexesPlots.js", "public/lib/js/TF/Rpc/Contracts/Contracts.js", "public/lib/js/TF/Rpc/Contracts/ContractOwnerData.js", "public/lib/js/TF/Rpc/Contracts/ContractsOwnersGrid.js", "public/lib/js/TF/Rpc/Contracts/ContractsExports.js", "public/lib/js/common/add-owner-heritor.js", "public/lib/js/TF/Rpc/Owners/Owners.js", "public/lib/js/TF/Rpc/Owners/OwnersTree.js", "public/lib/js/TF/Rpc/Owners/OwnersHeritorsTree.js", "public/lib/js/TF/Rpc/Owners/OwnersRepresentedGrid.js", "public/lib/js/TF/Rpc/Users/<USER>", "public/lib/js/TF/Rpc/Users/<USER>", "public/lib/js/users/user-rights.js", "public/lib/js_external/loading-items.js", "public/lib/js_external/datagrid-bufferview.js", "public/lib/js/contracts/contracts-owners-grid.js", "public/lib/js/contracts/contracts-owners-reps.js", "public/lib/js/annexes/annexes-home.js", "public/lib/js/annexes/annexes-tree.js", "public/lib/js/annexes/annexes-plots.js", "public/lib/js/annexes/annexes-files.js", "public/lib/js/annexes/annexes-owners.js", "public/lib/js/annexes/annexes-farmings.js", "public/lib/js/common/contracts/copy-contracts-error.js", "public/lib/js/common/contracts/contracts-tree-filter.js", "public/lib/js/common/contracts/contract-plots.js", "public/lib/js/common/validators.js", "public/lib/js/common/contracts/sales-contracts-error.js"], "dest": "public/lib/js/annexes/", "fileName": "annexes", "taskName": "minify-annexes-page", "taskDescription": "Конкатенира и минифицира JS файлове в 'Анекси'", "minify": true}, "Collections": {"src": ["public/lib/js_external/tf.filterwidget.js", "public/lib/js_external/loading-items.js", "public/lib/js_external/datagrid-bufferview.js", "public/lib/js/TF/Rpc/Users/<USER>", "public/lib/js/TF/Rpc/Users/<USER>", "public/lib/js/TF/Rpc/Collections/Collections.js", "public/lib/js/TF/Rpc/Collections/CollectionPaymentsGrid.js", "public/lib/js/TF/Rpc/Collections/CollectionsContractsGrid.js", "public/lib/js/TF/Rpc/Collections/ExportCollection.js", "public/lib/js/users/user-rights.js", "public/lib/js/collections/home.js", "public/lib/js/collections/collections-contracts-grid.js", "public/lib/js/collections/collection-payments-grid.js"], "dest": "public/lib/js/collections/", "fileName": "collections", "taskName": "minify-collections-page", "taskDescription": "Конкатенира и минифицира JS файлове във 'Вземания'", "minify": true}, "Contracts": {"src": ["public/lib/js/TF/Rpc/Plots/Plots.js", "public/lib/js/TF/Rpc/Plots/PlotsContractsDatagrid.js", "public/lib/js/TF/Rpc/Contracts/Contracts.js", "public/lib/js/TF/Rpc/Contracts/ContractOwnerData.js", "public/lib/js/TF/Rpc/Contracts/ContractsOwnersGrid.js", "public/lib/js/TF/Rpc/Contracts/ContractsFilesGrid.js", "public/lib/js/TF/Rpc/Contracts/ContractsPlotsGrid.js", "public/lib/js/TF/Rpc/Contracts/ContractsGroupsGrid.js", "public/lib/js/TF/Rpc/Contracts/PlotContractsGrid.js", "public/lib/js/TF/Rpc/Contracts/EditPlotAreas.js", "public/lib/js/TF/Rpc/Contracts/ContractsTree.js", "public/lib/js/TF/Rpc/Contracts/ContractsExports.js", "public/lib/js/common/add-owner-heritor.js", "public/lib/js/TF/Rpc/Owners/Owners.js", "public/lib/js/TF/Rpc/Owners/OwnersTree.js", "public/lib/js/TF/Rpc/Owners/OwnersHeritorsTree.js", "public/lib/js/TF/Rpc/Owners/OwnersRepresentedGrid.js", "public/lib/js/common/post-payment.js", "public/lib/js/TF/Rpc/Users/<USER>", "public/lib/js/TF/Rpc/Users/<USER>", "public/lib/js/users/user-rights.js", "public/lib/js_external/loading-items.js", "public/lib/js_external/datagrid-bufferview.js", "public/lib/js/contracts/contracts-home.js", "public/lib/js/contracts/contracts-groups.js", "public/lib/js/contracts/contracts-plots-grid.js", "public/lib/js/contracts/contracts-owners-grid.js", "public/lib/js/contracts/contracts-farming-grid.js", "public/lib/js/contracts/contracts-files-grid.js", "public/lib/js/contracts/contracts-annexes.js", "public/lib/js/contracts/contracts-owners-reps.js", "public/lib/js/contracts/contracts-owners-edit-reps.js", "public/lib/js/contracts/contracts-tree.js", "public/lib/js/contracts/contracts-plots-confirmation-grid.js", "public/lib/js/contracts/plot-contracts-grid.js", "public/lib/js/TF/Rpc/Owners/RepresentativesTree.js", "public/lib/js/owners/owners-representatives.js", "public/lib/js/common/contracts/copy-contracts-error.js", "public/lib/js/common/contracts/contracts-tree-filter.js", "public/lib/js/common/contracts/contract-plots.js", "public/lib/js/TF/Rpc/Annexes/Annexes.js", "public/lib/js/TF/Rpc/Annexes/AnnexesPlots.js", "public/lib/js/common/validators.js", "public/lib/js_external/datagrid-detailview.js", "public/lib/js/common/contracts/sales-contracts-error.js"], "dest": "public/lib/js/contracts/", "fileName": "contracts", "taskName": "minify-contracts-page", "taskDescription": "Конкатенира и минифицира JS файлове в 'Договори'", "minify": true}, "Cooperators": {"src": ["public/lib/js/TF/Rpc/Cooperators/Cooperators.js", "public/lib/js/TF/Rpc/Cooperators/CooperatorsTree.js", "public/lib/js/TF/Rpc/Cooperators/SummaryReportCooperatorsGrid.js", "public/lib/js/TF/Rpc/Cooperators/ExcludedReportCooperatorsGrid.js", "public/lib/js/TF/Rpc/Cooperators/CooperatorHeritorsTree.js", "public/lib/js/TF/Rpc/Cooperators/CooperatorHeritorsCombobox.js", "public/lib/js/TF/Rpc/Cooperators/CooperatorsCapitalGrid.js", "public/lib/js/TF/Rpc/Cooperators/CooperatorsDividendGrid.js", "public/lib/js/TF/Rpc/Cooperators/CooperatorsTemplatesGrid.js", "public/lib/js/TF/Rpc/Cooperators/ExportCooperatorsBlank.js", "public/lib/js/TF/Rpc/Cooperators/ExportCooperatorPaymentsBlanks.js", "public/lib/js/TF/Rpc/Cooperators/CooperatorsFilesGrid.js", "public/lib/js_external/loading-items.js", "public/lib/js/cooperators/cooperators-home.js", "public/lib/js/cooperators/cooperators-tree.js", "public/lib/js/cooperators/reports-cooperators.js", "public/lib/js/cooperators/cooperators-files-grid.js", "public/lib/js/cooperators/cooperator-heritors-tree.js", "public/lib/js/cooperators/cooperators-capital-grid.js", "public/lib/js/cooperators/cooperators-dividend-grid.js", "public/lib/js/cooperators/cooperators-templates.js", "public/lib/js/TF/Rpc/Users/<USER>", "public/lib/js/TF/Rpc/Users/<USER>", "public/lib/js/users/user-rights.js", "public/lib/js/common/validators.js"], "dest": "public/lib/js/cooperators/", "fileName": "cooperators", "taskName": "minify-cooperators-page", "taskDescription": "Конкатенира и минифицира JS файлове в 'Кооператори'", "minify": true}, "Coverage": {"src": ["public/lib/js_external/store.min.js", "public/lib/js_external/loading-items.js", "public/lib/js/coverage/home.js", "public/lib/js/coverage/files-tree.js", "public/lib/js/coverage/file-usb-grid.js", "public/lib/js/coverage/zp-layers-tree.js", "public/lib/js/coverage/controls.js", "public/lib/js/coverage/file-data-tree.js", "public/lib/js/coverage/btn-controls.js", "public/lib/js/coverage/map.js", "public/lib/js/coverage/zp-data-tree.js", "public/lib/js/TF/Rpc/Coverage/Coverage.js", "public/lib/js/TF/Rpc/Coverage/CoverageMap.js", "public/lib/js/TF/Rpc/Coverage/FilesTree.js", "public/lib/js/TF/Rpc/Coverage/FileStructureTree.js", "public/lib/js/TF/Rpc/Coverage/ZPLayerDataTree.js", "public/lib/js/TF/Rpc/Coverage/ZPLayersTree.js", "public/lib/js/TF/Rpc/Users/<USER>", "public/lib/js/TF/Rpc/Users/<USER>", "public/lib/js/users/user-rights.js"], "dest": "public/lib/js/coverage/", "fileName": "coverage", "taskName": "minify-coverage-page", "taskDescription": "Конкатенира и минифицира JS файлове в 'Данни от машини'", "minify": true}, "CropRotation": {"src": ["public/lib/js_external/loading-items.js", "public/lib/js/croprotation/crops.js", "public/lib/js/croprotation/soil-samples-grid.js", "public/lib/js/croprotation/layers-tree.js", "public/lib/js/croprotation/layer-data-grid.js", "public/lib/js/croprotation/reports.js", "public/lib/js/croprotation/soil-samples-files.js", "public/lib/js/croprotation/soil-norm-grid.js", "public/lib/js/croprotation/soil-norm-avg-grid.js", "public/lib/js/croprotation/sample-files-tree.js", "public/lib/js/TF/Rpc/CropRotation/CropRotation.js", "public/lib/js/TF/Rpc/CropRotation/CropLayersTree.js", "public/lib/js/TF/Rpc/CropRotation/LayerDataGrid.js", "public/lib/js/TF/Rpc/CropRotation/SoilSamplesGrid.js", "public/lib/js/TF/Rpc/CropRotation/CropRotationExports.js", "public/lib/js/TF/Rpc/CropRotation/SoilSamplesFilesTree.js", "public/lib/js/TF/Rpc/CropRotation/CropLayerIsakCombobox.js", "public/lib/js/TF/Rpc/CropRotation/OverlapReportGrid.js", "public/lib/js/TF/Rpc/CropRotation/CultureReportGrid.js", "public/lib/js/TF/Rpc/CropRotation/ApplySoilSampleFileCombobox.js", "public/lib/js/TF/Rpc/CropRotation/AVGSoilSamplesNormGrid.js", "public/lib/js/TF/Rpc/CropRotation/SoilSamplesNormGrid.js", "public/lib/js/TF/Rpc/CropRotation/CropDataIsakCombobox.js", "public/lib/js/TF/Rpc/CropRotation/SoilSamplesFilesGrid.js", "public/lib/js/TF/Rpc/CropRotation/SoilSamplesFilesDataGrid.js", "public/lib/js/TF/Rpc/Users/<USER>", "public/lib/js/TF/Rpc/Users/<USER>", "public/lib/js/users/user-rights.js"], "dest": "public/lib/js/croprotation/", "fileName": "croprotation", "taskName": "minify-croprotation-page", "taskDescription": "Конкатенира и минифицира JS файлове в 'Сеитбооборот'", "minify": true}, "Diary": {"src": ["public/lib/js_external/store.min.js", "public/lib/js_external/loading-items.js", "public/lib/js/diary/diary-home.js", "public/lib/js/diary/zp-tree.js", "public/lib/js/diary/machine-types.js", "public/lib/js/diary/machines.js", "public/lib/js/diary/attachment-types.js", "public/lib/js/diary/attachments.js", "public/lib/js/diary/substance-types.js", "public/lib/js/diary/substance-units.js", "public/lib/js/diary/substance-technics.js", "public/lib/js/diary/event-types.js", "public/lib/js/diary/event-subtypes.js", "public/lib/js/diary/performers.js", "public/lib/js/diary/diary-reports.js", "public/lib/js/diary/map.js", "public/lib/js/diary/zplot-events-grid.js", "public/lib/js/diary/map-layers-tree.js", "public/lib/js/diary/fuel-diary.js", "public/lib/js/diary/chemical-diary.js", "public/lib/js/diary/expenses-grid.js", "public/lib/js/diary/units-trace.js", "public/lib/js/diary/filter-kvs.js", "public/lib/js/TF/Rpc/Diary/Diary.js", "public/lib/js/TF/Rpc/Diary/DiaryAuxiliaryItems.js", "public/lib/js/TF/Rpc/Diary/DiaryConfigs.js", "public/lib/js/TF/Rpc/Diary/DiaryExpenses.js", "public/lib/js/TF/Rpc/Diary/ZPlotEventsGrid.js", "public/lib/js/TF/Rpc/Diary/ZPTree.js", "public/lib/js/TF/Rpc/Diary/DiaryMap.js", "public/lib/js/TF/Rpc/Diary/DiaryReportsGrid.js", "public/lib/js/TF/Rpc/Diary/WialonActions.js", "public/lib/js/TF/Rpc/Users/<USER>", "public/lib/js/TF/Rpc/Users/<USER>", "public/lib/js/users/user-rights.js"], "dest": "public/lib/js/diary/", "fileName": "diary", "taskName": "minify-diary-page", "taskDescription": "Конкатенира и минифицира JS файлове в 'Данни от машини'", "minify": true}, "Dividends": {"src": ["public/lib/js/TF/Rpc/Dividents/Dividents.js", "public/lib/js/TF/Rpc/Dividents/AnnualReportTree.js", "public/lib/js/TF/Rpc/Dividents/DividentsGrid.js", "public/lib/js/TF/Rpc/Dividents/ExportDividendPaymentsBlanks.js", "public/lib/js_external/loading-items.js", "public/lib/js/dividends/dividends-home.js", "public/lib/js/dividends/dividends-tree.js", "public/lib/js/dividends/dividends-grid.js", "public/lib/js/TF/Rpc/Users/<USER>", "public/lib/js/TF/Rpc/Users/<USER>", "public/lib/js/users/user-rights.js"], "dest": "public/lib/js/dividends/", "fileName": "dividends", "taskName": "minify-dividends-page", "taskDescription": "Конкатенира и минифицира JS файлове в 'Дивиденти'", "minify": true}, "Farming": {"src": ["public/lib/js/TF/Rpc/Farming/Farming.js", "public/lib/js/TF/Rpc/Farming/FarmingGrid.js", "public/lib/js_external/loading-items.js", "public/lib/js/farming/farming-home.js", "public/lib/js/TF/Rpc/Contracts/Contracts.js", "public/lib/js/TF/Rpc/Contracts/ContractOwnerData.js", "public/lib/js/plots/plots-owners-reps.js", "public/lib/js/TF/Rpc/Users/<USER>", "public/lib/js/TF/Rpc/Users/<USER>", "public/lib/js/users/user-rights.js", "public/lib/js/users/users-consts.js", "public/lib/js/users/users-maingrid.js", "public/lib/js/common/post-payment.js", "public/lib/js/common/validators.js"], "dest": "public/lib/js/farming/", "fileName": "farming", "taskName": "minify-farming-page", "taskDescription": "Конкатенира и минифицира JS файлове в 'Стопанства'", "minify": true}, "Files": {"src": ["public/lib/js_external/loading-items.js", "public/lib/js/files/files-home.js", "public/lib/js/TF/Rpc/Files/Files.js", "public/lib/js/TF/Rpc/Files/FilesGrid.js", "public/lib/js/TF/Rpc/Files/KVSDefinition.js", "public/lib/js/TF/Rpc/Users/<USER>", "public/lib/js/TF/Rpc/Users/<USER>", "public/lib/js/users/user-rights.js"], "dest": "public/lib/js/files/", "fileName": "files", "taskName": "minify-files-page", "taskDescription": "Конкатенира и минифицира JS файлове в 'Зареждане на данни'", "minify": true}, "Hypothecs": {"src": ["public/lib/js/TF/Rpc/Hypothecs/Hypothecs.js", "public/lib/js/TF/Rpc/Hypothecs/HypothecsTree.js", "public/lib/js/TF/Rpc/Hypothecs/HypothecsPlotsGrid.js", "public/lib/js/TF/Rpc/Hypothecs/HypothecsPaymentsGrid.js", "public/lib/js/TF/Rpc/Hypothecs/HypothecsFilesGrid.js", "public/lib/js/TF/Rpc/Hypothecs/HypothecsCreditors.js", "public/lib/js/TF/Rpc/Users/<USER>", "public/lib/js/TF/Rpc/Users/<USER>", "public/lib/js/users/user-rights.js", "public/lib/js/hypothecs/Home.js", "public/lib/js/hypothecs/HypothecsTree.js", "public/lib/js/hypothecs/HypothecsPlotsGrid.js", "public/lib/js/hypothecs/HypothecsFilesGrid.js", "public/lib/js/hypothecs/HypothecsPaymentsGrid.js", "public/lib/js/hypothecs/PrintTemplatesGrid.js"], "dest": "public/lib/js/hypothecs/", "fileName": "hypothecs", "taskName": "minify-hypothecs-page", "taskDescription": "Конкатенира и минифицира JS файлове в 'Ипотеки'", "minify": true}, "Isak": {"src": ["public/lib/js_external/store.min.js", "public/lib/js_external/loading-items.js", "public/lib/js/isak/isak-home.js", "public/lib/js/isak/reports.js", "public/lib/js/isak/layers-tree.js", "public/lib/js/isak/maingrid.js", "public/lib/js/TF/Rpc/Isak/Isak.js", "public/lib/js/TF/Rpc/Isak/IsakDiffAllowableGrid.js", "public/lib/js/TF/Rpc/Users/<USER>", "public/lib/js/TF/Rpc/Users/<USER>", "public/lib/js/users/user-rights.js"], "dest": "public/lib/js/isak/", "fileName": "isak", "taskName": "minify-isak-page", "taskDescription": "Конкатенира и минифицира JS файлове в 'ИСАК'", "minify": true}, "KVSContractsUpdate": {"src": ["public/lib/js/TF/Rpc/Common/AllowableEkateCombobox.js", "public/lib/js/TF/Rpc/Common/PZPEkateCombobox.js", "public/lib/js/TF/Rpc/Common/LFAEkateCombobox.js", "public/lib/js/TF/Rpc/KVSContractsUpdate/KVSContractsUpdate.js", "public/lib/js/TF/Rpc/KVSContractsUpdate/KVSContractsPlotsForUpdateGrid.js", "public/lib/js/TF/Rpc/Map/Map.js", "public/lib/js/TF/Rpc/Map/MapTools.js", "public/lib/js/TF/Rpc/Files/Files.js", "public/lib/js/TF/Rpc/Files/FilesGrid.js", "public/lib/js_external/loading-items.js", "public/lib/js/kvs_contracts_update/kvs-contracts-update-home.js", "public/lib/js/kvs_contracts_update/map-home.js", "public/lib/js/map/controls.js", "public/lib/js/map/layers-clipping.js", "public/lib/js/map/plot-propertygrid.js"], "dest": "public/lib/js/kvs_contracts_update/", "fileName": "kvscontractsupdate", "taskName": "minify-kvscontractsupdate-page", "taskDescription": "Конкатенира и минифицира JS файлове в 'Обновяване на договори'", "minify": true}, "KVSInvalidGeometry": {"src": ["public/lib/js/TF/Rpc/Map/Map.js", "public/lib/js/TF/Rpc/Map/MapTools.js", "public/lib/js/TF/Rpc/KVSInvalidGeometry/KVSInvalidGeometry.js", "public/lib/js/TF/Rpc/KVSInvalidGeometry/KVSInvalidGeometryPlotsGrid.js", "public/lib/js/TF/Rpc/KVSInvalidGeometry/KVSInvalidGeometryMapTools.js", "public/lib/js/TF/Rpc/Files/Files.js", "public/lib/js/TF/Rpc/Files/FilesGrid.js", "public/lib/js_external/loading-items.js", "public/lib/js/kvs_invalid_geometry/kvs_invalid_geometry-home.js", "public/lib/js/kvs_invalid_geometry/map-home.js", "public/lib/js/kvs_invalid_geometry/controls.js", "public/lib/js/kvs_invalid_geometry/map-tools.js", "public/lib/js/map/plot-propertygrid.js"], "dest": "public/lib/js/kvs_invalid_geometry/", "fileName": "kvsinvalidgeometry", "taskName": "minify-kvsinvalidgeometry-page", "taskDescription": "Конкатенира и минифицира JS файлове в 'Невалидни геометрии'", "minify": true}, "Map": {"src": ["public/lib/js_external/tf.filterwidget.js", "public/lib/js_external/store.min.js", "public/lib/js/common/spectrum.js", "public/lib/js_external/loading-items.js", "public/lib/js/map/map-home.js", "public/lib/js/map/controls.js", "public/lib/js/map/plot-propertygrid.js", "public/lib/js/map/layer-copy.js", "public/lib/js/map/zp-grid.js", "public/lib/js/map/isak-grid.js", "public/lib/js/map/for-isak-grid.js", "public/lib/js/map/kvs-grid.js", "public/lib/js/map/kms-grid.js", "public/lib/js/map/allowable-grid.js", "public/lib/js/map/lfa-grid.js", "public/lib/js/map/natura-2000-grid.js", "public/lib/js/map/gaski-chervenogushi-grid.js", "public/lib/js/map/gaski-zimni-grid.js", "public/lib/js/map/livaden-blatar-grid.js", "public/lib/js/map/orli-leshoyadi-grid.js", "public/lib/js/map/pzp-grid.js", "public/lib/js/map/gps-grid.js", "public/lib/js/map/attr-tables.js", "public/lib/js/map/work-layer-grid.js", "public/lib/js/map/kvs-split.js", "public/lib/js/map/kvs-merge.js", "public/lib/js/plots/init-filter-components.js", "public/lib/js/map/map-layers-tree.js", "public/lib/js/map/map-tools.js", "public/lib/js/map/layers-clipping.js", "public/lib/js/TF/Rpc/Map/Map.js", "public/lib/js/TF/Rpc/Map/MapExportLayer.js", "public/lib/js/TF/Rpc/Map/MapLayerChange.js", "public/lib/js/TF/Rpc/Map/MapAttributeTables.js", "public/lib/js/TF/Rpc/Map/MapTools.js", "public/lib/js/TF/Rpc/Map/KVSMerge.js", "public/lib/js/TF/Rpc/Map/MapExportLayerGraphically.js", "public/lib/js/TF/Rpc/Map/KVSGrid.js", "public/lib/js/TF/Rpc/Map/KMSGrid.js", "public/lib/js/TF/Rpc/Map/WorkLayerGrid.js", "public/lib/js/TF/Rpc/Map/GpsGrid.js", "public/lib/js/TF/Rpc/Map/ZPGrid.js", "public/lib/js/TF/Rpc/Map/IsakGrid.js", "public/lib/js/TF/Rpc/Common/AllowableEkateCombobox.js", "public/lib/js/TF/Rpc/Common/PZPEkateCombobox.js", "public/lib/js/TF/Rpc/Common/LFAEkateCombobox.js", "public/lib/js/TF/Rpc/Common/LabelNamesCombobox.js", "public/lib/js/TF/Rpc/Users/<USER>", "public/lib/js/TF/Rpc/Users/<USER>", "public/lib/js/users/user-rights.js"], "dest": "public/lib/js/map/", "fileName": "map", "taskName": "minify-map-page", "taskDescription": "Конкатенира и минифицира JS файлове в 'Карта'", "minify": true}, "OSZ": {"src": ["public/lib/js/TF/Rpc/Rpc.js", "public/lib/js/TF/Rpc/ExceptionsList.js", "public/lib/js/TF/Rpc/Exception.js", "public/lib/js/TF/Rpc/OSZ/OSZ.js", "public/lib/js/TF/Rpc/OSZ/OSZFiles.js", "public/lib/js/TF/Rpc/OSZ/OSZFilesPlots.js", "public/lib/js/TF/Rpc/Users/<USER>", "public/lib/js/TF/Rpc/Users/<USER>", "public/lib/js/users/user-rights.js", "public/lib/js/osz/Home.js", "public/lib/js/osz/OSZFilesTree.js", "public/lib/js/osz/OSZFilesPlotsGrid.js"], "dest": "public/lib/js/osz/", "fileName": "osz", "taskName": "minify-osz-page", "taskDescription": "Конкатенира и минифицира JS файлове в 'Данни от ОСЗ'", "minify": true}, "Overlaps": {"src": ["public/lib/js/TF/Rpc/Users/<USER>", "public/lib/js/TF/Rpc/Users/<USER>", "public/lib/js/users/user-rights.js", "public/lib/js_external/loading-items.js", "public/lib/js_external/store.min.js", "public/lib/js/overlaps/kvs-info.js", "public/lib/js/overlaps/overlaps-map.js", "public/lib/js/overlaps/overlaps-home.js", "public/lib/js/overlaps/overlaps-datagrids.js", "public/lib/js/overlaps/overlaps-reports.js", "public/lib/js/overlaps/overlap-layers-tree.js", "public/lib/js/TF/Rpc/Common/Common.js", "public/lib/js/TF/Rpc/Common/OwnersInfo.js", "public/lib/js/TF/Rpc/Common/PlotInfo.js", "public/lib/js/TF/Rpc/Common/ContractInfo.js", "public/lib/js/TF/Rpc/Overlaps/Overlaps.js", "public/lib/js/TF/Rpc/Overlaps/OverlapsLayersTree.js", "public/lib/js/TF/Rpc/Overlaps/OverlapsDataGrid.js", "public/lib/js/TF/Rpc/Overlaps/OverlapsMapTools.js"], "dest": "public/lib/js/overlaps/", "fileName": "overlaps", "taskName": "minify-overlaps-page", "taskDescription": "Конкатенира и минифицира JS файлове в 'Застъпвания'", "minify": true}, "OwnerPayments": {"src": ["public/lib/js/TF/Rpc/Common/PayerNamesCombobox.js", "public/lib/js/TF/Rpc/Common/MestnostCombobox.js", "public/lib/js/TF/Rpc/OwnerPayments/OwnerPayments.js", "public/lib/js/TF/Rpc/OwnerPayments/ContractsByOwnerPaymentsGrid.js", "public/lib/js/TF/Rpc/OwnerPayments/OwnersTree.js", "public/lib/js/TF/Rpc/OwnerPayments/PersonalUseGrid.js", "public/lib/js/TF/Rpc/Payments/ContractsTree.js", "public/lib/js/TF/Rpc/Payments/AddPayment.js", "public/lib/js/TF/Rpc/Payments/AddChargedRenta.js", "public/lib/js/TF/Rpc/Payments/ContractPaymentsGrid.js", "public/lib/js/TF/Rpc/Payments/ExportPayment.js", "public/lib/js/TF/Rpc/Payments/PersonalUseGrid.js", "public/lib/js/TF/Rpc/Payments/ChargedRentaHistoryGrid.js", "public/lib/js/TF/Rpc/Payments/ChargedRentaHistoryTree.js", "public/lib/js/TF/Rpc/Payments/InfoSummaryReportByEkateMoney.js", "public/lib/js/TF/Rpc/Payments/PaymentsReportsByDateGrid.js", "public/lib/js/TF/Rpc/Payments/PaymentsReportsByDateGridExportAndPrint.js", "public/lib/js/TF/Rpc/Payments/PaymentsReportsGrid.js", "public/lib/js/TF/Rpc/Payments/PaymentsReportsGridExportAndPrint.js", "public/lib/js/TF/Rpc/Payments/PaymentsReportsRentaNaturaGridExportAndPrint.js", "public/lib/js/TF/Rpc/Payments/PaymentsBankPaymentReport.js", "public/lib/js/TF/Rpc/Payments/PaymentsBankAndNaturaPaymentReport.js", "public/lib/js/TF/Rpc/Payments/TransactionsGrid.js", "public/lib/js/TF/Rpc/Users/<USER>", "public/lib/js/TF/Rpc/Users/<USER>", "public/lib/js/users/user-rights.js", "public/lib/js/TF/Rpc/Owners/Owners.js", "public/lib/js/TF/Rpc/Owners/OwnersList.js", "public/lib/js_external/loading-items.js", "public/lib/js/owners_payments/home.js", "public/lib/js/common/post-payment.js", "public/lib/js/payments/home.js", "public/lib/js/payments/full-payments.js", "public/lib/js/payments/full-nat-payments.js", "public/lib/js/payments/charged-renta-history-tree.js", "public/lib/js/payments/charged-renta-history-grid.js", "public/lib/js/payments/contracts-tree.js", "public/lib/js/payments/personal-use-plots.js", "public/lib/js/payments/edit-personal-use.js", "public/lib/js/owners_payments/owners-contracts-tree.js", "public/lib/js/owners_payments/contract-payments.js", "public/lib/js/owners_payments/personal-use.js", "public/lib/js/payments/transactions.js", "public/lib/js/payments/transaction-payments.js", "public/lib/js/payments/payments-reports.js", "public/lib/js/payments/weighing-note-grid.js", "public/lib/js/common/charged-renta-filter.js", "public/lib/js/common/owners-payments/owners-contracts-tree-filter.js"], "dest": "public/lib/js/owner_payments/", "fileName": "ownerpayments", "taskName": "minify-ownerpayments-page", "taskDescription": "Конкатенира и минифицира JS файлове в 'Ренти по собственици'", "minify": true}, "Owners": {"src": ["public/lib/js/TF/Rpc/Owners/Owners.js", "public/lib/js/TF/Rpc/Owners/OwnersTree.js", "public/lib/js/TF/Rpc/Owners/OwnersHeritorsTree.js", "public/lib/js/TF/Rpc/Owners/OwnersDocuments.js", "public/lib/js/TF/Rpc/Owners/OwnersFiles.js", "public/lib/js/TF/Rpc/Owners/RepresentativesTree.js", "public/lib/js/TF/Rpc/Owners/OwnersRepresentedGrid.js", "public/lib/js/TF/Rpc/Users/<USER>", "public/lib/js/TF/Rpc/Users/<USER>", "public/lib/js/users/user-rights.js", "public/lib/js_external/loading-items.js", "public/lib/js/common/post-payment.js", "public/lib/js/owners/owners-home.js", "public/lib/js/owners/owners-tree.js", "public/lib/js/owners/owners-plots-grid.js", "public/lib/js/owners/owners-inherited-plots-grid.js", "public/lib/js/owners/owners-payments-grid.js", "public/lib/js/owners/owners-documents-grid.js", "public/lib/js/owners/owners-files-grid.js", "public/lib/js/owners/owners-heritors-tree.js", "public/lib/js/owners/owners-representatives.js", "public/lib/js/common/post-payment.js", "public/lib/js/common/validators.js"], "dest": "public/lib/js/owners/", "fileName": "owners", "taskName": "minify-owners-page", "taskDescription": "Конкатенира и минифицира JS файлове в 'Собственици'", "minify": true}, "Payments": {"src": ["public/lib/js/TF/Rpc/Common/PayerNamesCombobox.js", "public/lib/js/TF/Rpc/Common/MestnostCombobox.js", "public/lib/js/TF/Rpc/Payments/ContractsTree.js", "public/lib/js/TF/Rpc/Payments/AddChargedRenta.js", "public/lib/js/TF/Rpc/Payments/ContractPaymentsGrid.js", "public/lib/js/TF/Rpc/Payments/ExportPayment.js", "public/lib/js/TF/Rpc/Payments/PersonalUseGrid.js", "public/lib/js/TF/Rpc/Payments/ChargedRentaHistoryGrid.js", "public/lib/js/TF/Rpc/Payments/ChargedRentaHistoryTree.js", "public/lib/js/TF/Rpc/Payments/InfoSummaryReportByEkateMoney.js", "public/lib/js/TF/Rpc/Payments/PaymentsReportsByDateGrid.js", "public/lib/js/TF/Rpc/Payments/PaymentsReportsByDateGridExportAndPrint.js", "public/lib/js/TF/Rpc/Payments/PaymentsReportsGrid.js", "public/lib/js/TF/Rpc/Payments/PaymentsReportsGridExportAndPrint.js", "public/lib/js/TF/Rpc/Payments/PaymentsReportsRentaNaturaGridExportAndPrint.js", "public/lib/js/TF/Rpc/Payments/PaymentsBankPaymentReport.js", "public/lib/js/TF/Rpc/Payments/PaymentsBankAndNaturaPaymentReport.js", "public/lib/js/TF/Rpc/Payments/TransactionsGrid.js", "public/lib/js/TF/Rpc/Owners/Owners.js", "public/lib/js/TF/Rpc/Owners/OwnersList.js", "public/lib/js/TF/Rpc/Users/<USER>", "public/lib/js/TF/Rpc/Users/<USER>", "public/lib/js/users/user-rights.js", "public/lib/js_external/loading-items.js", "public/lib/js/common/post-payment.js", "public/lib/js/payments/contracts-tree.js", "public/lib/js/payments/home.js", "public/lib/js/payments/full-payments.js", "public/lib/js/payments/charged-renta-history-tree.js", "public/lib/js/payments/charged-renta-history-grid.js", "public/lib/js/payments/full-nat-payments.js", "public/lib/js/payments/contract-payments.js", "public/lib/js/payments/personal-use.js", "public/lib/js/payments/personal-use-plots.js", "public/lib/js/payments/edit-personal-use.js", "public/lib/js/payments/transactions.js", "public/lib/js/payments/transaction-payments.js", "public/lib/js/payments/payments-reports.js", "public/lib/js/payments/weighing-note-grid.js", "public/lib/js_external/datagrid-scrollview.js", "public/lib/js/common/charged-renta-filter.js", "public/lib/js/common/payments/contracts-tree-filter.js"], "dest": "public/lib/js/payments/", "fileName": "payments", "taskName": "minify-payments-page", "taskDescription": "Конкатенира и минифицира JS файлове в 'Ренти по договори'", "minify": true}, "Payroll": {"src": ["public/lib/js/TF/Rpc/Payroll/Payroll.js", "public/lib/js/TF/Rpc/Payroll/PayrollGridExportAndPrint.js", "public/lib/js/TF/Rpc/Payroll/PayrollGrid.js", "public/lib/js/TF/Rpc/Payroll/PayrollExportsGrid.js", "public/lib/js/TF/Rpc/Users/<USER>", "public/lib/js/TF/Rpc/Users/<USER>", "public/lib/js/TF/Rpc/Owners/Owners.js", "public/lib/js/TF/Rpc/Owners/OwnersList.js", "public/lib/js/users/user-rights.js", "public/lib/js_external/loading-items.js", "public/lib/js/payroll/home.js", "public/lib/js/payroll/payroll-owner-grid.js", "public/lib/js/payroll/payroll-plot-grid.js", "public/lib/js/payroll/payroll-print-export.js", "public/lib/js_external/datagrid-scrollview.js"], "dest": "public/lib/js/payroll/", "fileName": "payroll", "taskName": "minify-payroll-page", "taskDescription": "Конкатенира и минифицира JS файлове във 'Ведомост'", "minify": true}, "Plots": {"src": ["public/lib/js_external/tf.filterwidget.js", "public/lib/js/TF/Rpc/Plots/Plots.js", "public/lib/js/TF/Rpc/Plots/PlotsTree.js", "public/lib/js/TF/Rpc/Plots/PlotsContractsDatagrid.js", "public/lib/js/TF/Rpc/Plots/PlotsFarmingGrid.js", "public/lib/js/TF/Rpc/Plots/PlotsDeclarations.js", "public/lib/js/TF/Rpc/Plots/ReportsShowMap.js", "public/lib/js/TF/Rpc/Plots/PlotMap.js", "public/lib/js/TF/Rpc/Plots/DeclarationChosenGrid.js", "public/lib/js/TF/Rpc/Plots/PlotReports.js", "public/lib/js/TF/Rpc/Plots/OwnPlotsReportGrid.js", "public/lib/js/TF/Rpc/Plots/SubleasedPlotsReportGrid.js", "public/lib/js/TF/Rpc/Plots/ForSubleasePlotsReportGrid.js", "public/lib/js/TF/Rpc/Plots/HypothecsPlotsReportGrid.js", "public/lib/js/TF/Rpc/Plots/ForHypothecPlotsReportGrid.js", "public/lib/js/TF/Rpc/Plots/RentedPlotsReportGrid.js", "public/lib/js/TF/Rpc/Plots/SubleasedRentedPlotsReportGrid.js", "public/lib/js/TF/Rpc/Plots/ExpiringContractsReportGrid.js", "public/lib/js/TF/Rpc/Plots/PlotsInManyContractsReportGrid.js", "public/lib/js/TF/Rpc/Plots/UsedPlotsReportGrid.js", "public/lib/js/TF/Rpc/Plots/ContractsWithOwnerlessPlotsReportGrid.js", "public/lib/js/TF/Rpc/Plots/HistoricalPlotsReportGrid.js", "public/lib/js/TF/Rpc/Plots/DetailedOwnPlotsReportGrid.js", "public/lib/js/TF/Rpc/Plots/PlotsImage.js", "public/lib/js/TF/Rpc/Contracts/Contracts.js", "public/lib/js/TF/Rpc/Contracts/ContractOwnerData.js", "public/lib/js/common/add-owner-heritor.js", "public/lib/js/TF/Rpc/Contracts/ContractsOwnersGrid.js", "public/lib/js/TF/Rpc/Owners/Owners.js", "public/lib/js/TF/Rpc/Owners/OwnersTree.js", "public/lib/js/TF/Rpc/Owners/OwnersHeritorsTree.js", "public/lib/js/TF/Rpc/Users/<USER>", "public/lib/js/TF/Rpc/Users/<USER>", "public/lib/js/users/user-rights.js", "public/lib/js_external/loading-items.js", "public/lib/js_external/store.min.js", "public/lib/js/plots/plots-home.js", "public/lib/js/plots/plots-tree.js", "public/lib/js/plots/plots-map.js", "public/lib/js/plots/plots-report-map.js", "public/lib/js/plots/plots-contracts-grid.js", "public/lib/js/plots/plots-hypothecs-grid.js", "public/lib/js/plots/plots-owners-grid.js", "public/lib/js/plots/plots-farming-grid.js", "public/lib/js/plots/plots-owners-reps.js", "public/lib/js/plots/plot-reports.js", "public/lib/js/plots/declarations.js", "public/lib/js/plots/decl69.js", "public/lib/js/plots/decl70.js", "public/lib/js/plots/decl73.js", "public/lib/js/plots/declPML.js", "public/lib/js/plots/anketna-karta.js", "public/lib/js/plots/plots-history.js", "public/lib/js/plots/init-filter-components.js", "public/lib/js_external/datagrid-detailview.js", "public/lib/js/common/validators.js", "public/lib/js/common/contracts/sales-contracts-error.js"], "dest": "public/lib/js/plots/", "fileName": "plots", "taskName": "minify-plots-page", "taskDescription": "Конкатенира и минифицира JS файлове в 'Имоти'", "minify": true}, "SalesContracts": {"src": ["public/lib/js/TF/Rpc/SalesContracts/SalesContracts.js", "public/lib/js/TF/Rpc/SalesContracts/SalesContractsTree.js", "public/lib/js/TF/Rpc/SalesContracts/SalesContractsFilesGrid.js", "public/lib/js/TF/Rpc/SalesContracts/SalesContractsSubleasedPlotsGrid.js", "public/lib/js/TF/Rpc/SalesContracts/BuyersGrid.js", "public/lib/js/TF/Rpc/SalesContracts/BuyersSalesContractsRelationGrid.js", "public/lib/js/TF/Rpc/SalesContracts/ReportSalesContractsGrid.js", "public/lib/js/TF/Rpc/SalesContracts/SalesContractsExports.js", "public/lib/js/TF/Rpc/Plots/Plots.js", "public/lib/js/TF/Rpc/Plots/PlotsSalesContractsDatagrid.js", "public/lib/js_external/loading-items.js", "public/lib/js/salescontracts/salescontracts-home.js", "public/lib/js/salescontracts/salescontracts-tree.js", "public/lib/js/salescontracts/salescontracts-files-grid.js", "public/lib/js/salescontracts/salescontracts-plots-grid.js", "public/lib/js/salescontracts/salescontracts-subleased-plots-grid.js", "public/lib/js/salescontracts/salescontracts-buyers-grid.js", "public/lib/js/salescontracts/salescontracts-report-grid.js", "public/lib/js/TF/Rpc/Users/<USER>", "public/lib/js/TF/Rpc/Users/<USER>", "public/lib/js/users/user-rights.js", "public/lib/js/common/contracts/sales-contracts-error.js"], "dest": "public/lib/js/salescontracts/", "fileName": "salescontracts", "taskName": "minify-salescontracts-page", "taskDescription": "Конкатенира и минифицира JS файлове в 'Договори за продажба'", "minify": true}, "Subleases": {"src": ["public/lib/js/TF/Rpc/Subleases/Subleases.js", "public/lib/js/TF/Rpc/Subleases/SubleasesTree.js", "public/lib/js/TF/Rpc/Subleases/SubleaseContragentsGrid.js", "public/lib/js/TF/Rpc/Subleases/SubleaseContragentData.js", "public/lib/js/TF/Rpc/Subleases/SubleasePlotsGrid.js", "public/lib/js/TF/Rpc/Subleases/SubleasePlotOwnersGrid.js", "public/lib/js/TF/Rpc/Subleases/SubleasesFiles.js", "public/lib/js/TF/Rpc/Subleases/SubleasesExports.js", "public/lib/js/TF/Rpc/Contracts/Contracts.js", "public/lib/js/TF/Rpc/Contracts/ContractOwnerData.js", "public/lib/js/TF/Rpc/Contracts/ContractsOwnersGrid.js", "public/lib/js/TF/Rpc/Contracts/ContractsExports.js", "public/lib/js/common/add-owner-heritor.js", "public/lib/js/TF/Rpc/Owners/Owners.js", "public/lib/js/TF/Rpc/Owners/OwnersHeritorsTree.js", "public/lib/js/TF/Rpc/Users/<USER>", "public/lib/js/TF/Rpc/Users/<USER>", "public/lib/js/users/user-rights.js", "public/lib/js_external/loading-items.js", "public/lib/js/subleases/subleases-home.js", "public/lib/js/subleases/subleases-tree.js", "public/lib/js/subleases/subleases-files.js", "public/lib/js/subleases/subleases-plots.js", "public/lib/js/subleases/subleases-owners.js", "public/lib/js/subleases/subleases-reps.js", "public/lib/js/subleases/subleases-contragents.js", "public/lib/js/subleases/subleases-contracts.js", "public/lib/js/common/validators.js"], "dest": "public/lib/js/subleases/", "fileName": "subleases", "taskName": "minify-subleases-page", "taskDescription": "Конкатенира и минифицира JS файлове в 'Преотдадени'", "minify": true}, "SubsidiesWizard": {"src": ["public/lib/js_external/store.min.js", "public/lib/js/TF/Rpc/Map/Map.js", "public/lib/js/TF/Rpc/Map/MapTools.js", "public/lib/js/TF/Rpc/Map/MapExportLayer.js", "public/lib/js/TF/Rpc/SubsidiesWizard/SubsidiesWizard.js", "public/lib/js/TF/Rpc/SubsidiesWizard/CulturesPropertyGrid.js", "public/lib/js/TF/Rpc/SubsidiesWizard/ForIsakSubsidiesGrid.js", "public/lib/js/TF/Rpc/SubsidiesWizard/ForIsakKVSReportGrid.js", "public/lib/js/TF/Rpc/SubsidiesWizard/ForIsakPZPReportGrid.js", "public/lib/js/TF/Rpc/SubsidiesWizard/ForIsakPNDPReportGrid.js", "public/lib/js/TF/Rpc/SubsidiesWizard/ForIsakZDPReportGrid.js", "public/lib/js/TF/Rpc/SubsidiesWizard/ForIsakDiffAllowableFinalGrid.js", "public/lib/js/TF/Rpc/SubsidiesWizard/ForIsakDiffNaturaGrid.js", "public/lib/js/TF/Rpc/SubsidiesWizard/ForIsakDiffLfaGrid.js", "public/lib/js/TF/Rpc/SubsidiesWizard/ForIsakDiffVPSMainGrid.js", "public/lib/js/TF/Rpc/SubsidiesWizard/ForIsakDiffVPSDetailedGrid.js", "public/lib/js_external/loading-items.js", "public/lib/js/subsidieswizard/home.js", "public/lib/js/subsidieswizard/layers-tree.js", "public/lib/js/subsidieswizard/for-isak-grid.js", "public/lib/js/subsidieswizard/controls.js", "public/lib/js/subsidieswizard/for-isak-diff-lfa-report.js", "public/lib/js/subsidieswizard/for-isak-diff-natura-report.js", "public/lib/js/subsidieswizard/report-ZDP-grid.js", "public/lib/js/subsidieswizard/report-PZP-grid.js", "public/lib/js/subsidieswizard/map-layers-tree.js", "public/lib/js/subsidieswizard/plot-propertygrid.js", "public/lib/js/subsidieswizard/for-isak-kvs-grid.js", "public/lib/js/subsidieswizard/cultures-propertygrid.js", "public/lib/js/subsidieswizard/map-tools.js", "public/lib/js/TF/Rpc/Users/<USER>", "public/lib/js/TF/Rpc/Users/<USER>", "public/lib/js/users/user-rights.js"], "dest": "public/lib/js/subsidieswizard/", "fileName": "subsidieswizard", "taskName": "minify-subsidieswizard-page", "taskDescription": "Конкатенира и минифицира JS файлове в 'Съветник за субсидии'", "minify": true}, "ThematicMaps": {"src": ["public/lib/js_external/loading-items.js", "public/lib/js/thematic_maps/map-home.js", "public/lib/js/plots/init-filter-components.js", "public/lib/js/TF/Rpc/ThematicMaps/ThematicMaps.js", "public/lib/js/TF/Rpc/ThematicMaps/ThematicMapsResultsGrid.js", "public/lib/js/TF/Rpc/ThematicMaps/ThematicMapsLayers.js", "public/lib/js/TF/Rpc/Users/<USER>", "public/lib/js/TF/Rpc/Users/<USER>", "public/lib/js/users/user-rights.js", "public/lib/js/common/spectrum.js"], "dest": "public/lib/js/thematic_maps/", "fileName": "thematicmaps", "taskName": "minify-thematicmaps-page", "taskDescription": "Конкатенира и минифицира JS файлове в 'Тематични карти'", "minify": true}, "Users": {"src": ["public/lib/js_external/loading-items.js", "public/lib/js/TF/Rpc/Users/<USER>", "public/lib/js/TF/Rpc/Users/<USER>", "public/lib/js/users/users.js", "public/lib/js/users/users-consts.js", "public/lib/js/users/users-subscription-usage.js", "public/lib/js/users/users-maingrid.js", "public/lib/js/users/users-search.js", "public/lib/js/users/user-devises.js", "public/lib/js/users/user-rights.js"], "dest": "public/lib/js/users/", "fileName": "users", "taskName": "minify-users-page", "taskDescription": "Конкатенира и минифицира JS файлове в 'Потребители'", "minify": true}, "ZPlots": {"src": ["public/lib/js_external/store.min.js", "public/lib/js_external/loading-items.js", "public/lib/js/zplots/zp-home.js", "public/lib/js/zplots/datagrids.js", "public/lib/js/zplots/info-propertygrid.js", "public/lib/js/zplots/kvs-info.js", "public/lib/js/zplots/reports.js", "public/lib/js/zplots/layers-tree.js", "public/lib/js/zplots/maingrid.js", "public/lib/js/TF/Rpc/Common/Common.js", "public/lib/js/TF/Rpc/Common/InfoRequests.js", "public/lib/js/TF/Rpc/ZPlots/ZPlots.js", "public/lib/js/TF/Rpc/ZPlots/ZPMapInfo.js", "public/lib/js/TF/Rpc/ZPlots/ZPlotsMaingrid.js", "public/lib/js/TF/Rpc/ZPlots/ZPlotsExport.js", "public/lib/js/TF/Rpc/ZPlots/KVSZpMapInfo.js", "public/lib/js/TF/Rpc/Users/<USER>", "public/lib/js/TF/Rpc/Users/<USER>", "public/lib/js/users/user-rights.js"], "dest": "public/lib/js/zplots/", "fileName": "zplots", "taskName": "minify-zplots-page", "taskDescription": "Конкатенира и минифицира JS файлове в 'Имоти Справки'", "minify": true}, "Reports": {"src": ["public/lib/js/TF/Rpc/Plots/Plots.js", "public/lib/js/TF/Rpc/Plots/OwnPlotsReportGrid.js", "public/lib/js/TF/Rpc/Plots/SubleasedPlotsReportGrid.js", "public/lib/js/TF/Rpc/Plots/ForSubleasePlotsReportGrid.js", "public/lib/js/TF/Rpc/Plots/HypothecsPlotsReportGrid.js", "public/lib/js/TF/Rpc/Plots/ForHypothecPlotsReportGrid.js", "public/lib/js/TF/Rpc/Plots/RentedPlotsReportGrid.js", "public/lib/js/TF/Rpc/Plots/SubleasedRentedPlotsReportGrid.js", "public/lib/js/TF/Rpc/Plots/ExpiringContractsReportGrid.js", "public/lib/js/TF/Rpc/Plots/PlotsInManyContractsReportGrid.js", "public/lib/js/TF/Rpc/Plots/UsedPlotsReportGrid.js", "public/lib/js/TF/Rpc/Plots/ContractsWithOwnerlessPlotsReportGrid.js", "public/lib/js/TF/Rpc/Plots/HistoricalPlotsReportGrid.js", "public/lib/js/TF/Rpc/Plots/DetailedOwnPlotsReportGrid.js", "public/lib/js/TF/Rpc/Plots/PlotMap.js", "public/lib/js/TF/Rpc/Plots/PlotReports.js", "public/lib/js/TF/Rpc/Plots/ReportsShowMap.js", "public/lib/js/TF/Rpc/Diary/Diary.js", "public/lib/js/TF/Rpc/Diary/DiaryReportsGrid.js", "public/lib/js/TF/Rpc/Payments/PaymentsBankPaymentReport.js", "public/lib/js/TF/Rpc/Payments/PaymentsBankAndNaturaPaymentReport.js", "public/lib/js/TF/Rpc/Payments/PersonalUseReport.js", "public/lib/js/TF/Rpc/Payments/UnpaidReantaReportGrid.js", "public/lib/js/TF/Rpc/Payroll/Payroll.js", "public/lib/js/TF/Rpc/Payroll/LightPayrollGrid.js", "public/lib/js_external/loading-items.js", "public/lib/js/plots/plots-map.js", "public/lib/js/plots/plots-report-map.js", "public/lib/js/plots/plot-reports.js", "public/lib/js/diary/diary-reports.js", "public/lib/js/payments/payments-reports.js", "public/lib/js/reports/reports-home.js", "public/lib/js/plots/init-filter-components.js", "public/lib/js_external/datagrid-detailview.js", "public/lib/js/TF/Rpc/Users/<USER>", "public/lib/js/TF/Rpc/Users/<USER>", "public/lib/js/users/user-rights.js"], "dest": "public/lib/js/reports/", "fileName": "reports", "taskName": "minify-reports-page", "taskDescription": "Конкатенира и минифицира JS файлове в 'Земеделски парцели'", "minify": true}, "Navigation": {"src": ["public/lib/js_external/store.min.js", "public/lib/js_external/loading-items.js", "public/lib/js/navigation/navigation-home.js", "public/lib/js/navigation/zp-tree.js", "public/lib/js/navigation/map.js", "public/lib/js/navigation/map-layers-tree.js", "public/lib/js/navigation/zplot-events-grid.js", "public/lib/js/navigation/filter-kvs.js", "public/lib/js/TF/Rpc/Diary/Diary.js", "public/lib/js/TF/Rpc/Diary/DiaryAuxiliaryItems.js", "public/lib/js/TF/Rpc/Diary/DiaryExpenses.js", "public/lib/js/TF/Rpc/Diary/ZPlotEventsGrid.js", "public/lib/js/TF/Rpc/Diary/ZPTree.js", "public/lib/js/TF/Rpc/Diary/DiaryMap.js", "public/lib/js/TF/Rpc/Diary/DiaryReportsGrid.js", "public/lib/js/TF/Rpc/Diary/WialonActions.js", "public/lib/js/TF/Rpc/Users/<USER>", "public/lib/js/TF/Rpc/Users/<USER>", "public/lib/js/TF/Rpc/Map/Map.js", "public/lib/js/TF/Rpc/Map/MapExportLayer.js", "public/lib/js/TF/Rpc/Map/NavigationLayerData.js", "public/lib/js/users/user-rights.js", "public/lib/js/navigation/ab-lines.js", "public/lib/js/navigation/navigation-preview.js"], "dest": "public/lib/js/navigation/", "fileName": "navigation", "taskName": "minify-navigation-page", "taskDescription": "Конкатенира и минифицира JS файлове в 'Навигации'", "minify": true}, "GlobalNotifications": {"src": ["public/lib/js_external/store.min.js", "public/lib/js_external/loading-items.js", "public/lib/js/notifications/global-notifications-admin-grid.js", "public/lib/js/TF/Rpc/GlobalNotifications/GlobalNotifications.js", "public/lib/js/TF/Rpc/GlobalNotifications/GlobalNotificationsMaingrid.js", "public/lib/js/users/user-rights.js"], "dest": "public/lib/js/global-notifications/", "fileName": "global-notifications", "taskName": "minify-global-notifications-page", "taskDescription": "Конкатенира и минифицира JS файлове в 'Настройки-Нотификации'", "minify": true}}