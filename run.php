<?php

use TF\Engine\Kernel\Sentry\Sentry;

if (!$loader = include 'vendor/autoload.php') {
    die('You must set up the project dependencies.');
}

if (!$migrationLoader = include 'migration_scripts/autoload_migration_scripts.php') {
    die('Technofarm commands autoloader failed.');
}

$dotenv = new Dotenv\Dotenv(__DIR__);
$dotenv->load();

require_once 'config/global.config.php';

// Config files
require_once SITE_PATH . '/engine/Plugins/Core/Farming/conf.php';
require_once SITE_PATH . '/protected/Common/Config.php';

$psr4loader = new TF\Psr4AutoloaderClass();
// register the autoloader
$psr4loader->register();
$psr4loader->addNamespace('TF\Commands', SITE_PATH . 'migration_scripts/Commands/');
$psr4loader->addNamespace('TF\Crons', SITE_PATH . 'crons/');
require_once SITE_PATH . 'crons/shell.php';

Sentry::init(getenv('SENTRY_DSN'), getenv('APPLICATION_MODE'));
$app = new Cilex\Application('Cilex');

$payrollExportCommand = new TF\Crons\CronPayrollExportCommand();
$payrollExportCommand->setPradoApp($application);
$app->command($payrollExportCommand);

$updateUsersProductUsageCommand = new TF\Crons\CronUpdateUsersProductUsageCommand();
$updateUsersProductUsageCommand->setPradoApp($application);
$app->command($updateUsersProductUsageCommand);

$regenerateMapFilesCommand = new TF\Commands\Common\RegenerateMapFilesCommand();
$regenerateMapFilesCommand->setPradoApp($application);
$app->command($regenerateMapFilesCommand);

// crons
$app->command(new TF\Crons\CronCoverageProcessingCommand());
$app->command(new TF\Crons\CronOszKvsProcessingCommand());
$app->command(new TF\Crons\CronKvsProcessingCommand());
$app->command(new TF\Crons\CronLayerProcessingCommand());
$app->command(new TF\Crons\CronAgreementsProcessingCommand());
$app->command(new TF\Crons\CronWorkLayerProcessingCommand());
$app->command(new TF\Crons\CronCopyProcessingCommand());
$app->command(new TF\Crons\CronCroplayerProcessingCommand());
$app->command(new TF\Crons\CronDeleteDownloadedFilesCommand());
$app->command(new TF\Crons\CronDeletePlotImagesCommand());
$app->command(new TF\Crons\CronNewYearCommand());
$app->command(new TF\Crons\CronOverlapsProcessingCommand());
$app->command(new TF\Crons\CronUpdatePlotInfoCommand());
$app->command(new TF\Crons\CronDeletePublicFilesUploadsFilesCommand());
$app->command(new TF\Crons\CronRegenerateLoginHashCommand());
$app->command(new TF\Crons\CronImportExcelKvsCommand());
$app->command(new TF\Crons\CronCsdProcessingCommand());
$app->command(new TF\Crons\CheckDBIntegrityCommand());

// migrate to login3 multiscript
$app->command(new TF\Commands\MigrateToLogin3Command());
$app->command(new TF\Commands\Common\FixSystemLayerColorsCommand());
$app->command(new TF\Commands\Common\InitFolderStructureCommand());
$app->command(new TF\Commands\Common\AddMissingLayersCommand());
$app->command(new TF\Commands\Common\AddMissingNotificationAlertsCommand());
$app->command(new TF\Commands\Common\DisplayScriptsLogCommand());
$app->command(new TF\Commands\Common\FixOwnersPercentForImportedContracts());
$app->command(new TF\Commands\Common\CheckLayerGeometryTypesCommand());
$app->command(new TF\Commands\Common\CheckDatabasesIntegrityCommand());
$app->command(new TF\Commands\Common\MigrateDiaryProductsOptionsCommand());
$app->command(new TF\Commands\Common\MigrateDiarySubTypesOptionsCommand());
$app->command(new TF\Commands\Common\ClearWialonDataCommand());
$app->command(new TF\Commands\Common\CheckLayersTablesSchemaCommand());
$app->command(new TF\Commands\Common\ImportContractsCommand());
$app->command(new TF\Commands\Common\CreateContractsTableCommand());
$app->command(new TF\Commands\Common\UpdateFrom2014To2015Command());
$app->command(new TF\Commands\Common\UpdateFrom2015To2016Command());
$app->command(new TF\Commands\Common\UpdateFrom2014To2016Command());
$app->command(new TF\Commands\Common\UpdateUserTo2017SupportCommand());
$app->command(new TF\Commands\Common\UpdateFrom2016To2017Command());
$app->command(new TF\Commands\Common\UpdateUserTo2018SupportCommand());
$app->command(new TF\Commands\Common\UpdateFrom2017To2018Command());
$app->command(new TF\Commands\Common\UpdateUserTo2019SupportCommand());
$app->command(new TF\Commands\Common\UpdateFrom2018To2019Command());
$app->command(new TF\Commands\Common\InitPayrollCommand());
$app->command(new TF\Commands\Common\MigrateFarmingsIbansColumnCommand());
$app->command(new TF\Commands\Common\RemoveSeppViewsCommand());
$app->command(new TF\Commands\Common\MigrateRKOCommand());
$app->command(new TF\Commands\Common\UpdateSubleasesViewCommand());
$app->command(new TF\Commands\Common\CollectPlotsCommand());
$app->command(new TF\Commands\Common\RecreateMatViewsUsedDbLinkCommand());
$app->command(new TF\Commands\Common\MigrateLayersFromAO());
$app->command(new TF\Commands\Common\CreateAOIdsCommand());
$app->command(new TF\Commands\Common\CopyUserAccountCommand());
$app->command(new TF\Commands\Common\RemoveViewsAndFunctionsUsedDbLinkCommand());
$app->command(new TF\Commands\Common\CreateViewsAndFunctionsWithDblinkCommand());
$app->command(new TF\Commands\Common\CheckAndDropPgNaturalSortOrderCommand());
$app->command(new TF\Commands\Common\InitUserRequestedEkattesCommand());
$app->command(new TF\Commands\Common\ReplaceKvsContractsUpdateViewCommand());
$app->command(new TF\Commands\Common\DeleteUnusedTablesFromUserDBCommand());
$app->command(new TF\Commands\Common\GetUsersWithPersonalUseCommand());
$app->command(new TF\Commands\Common\ReplaceAOpropertyContractsNumbers());
$app->command(new TF\Commands\Common\DeleteAllPersonalUseData());
$app->command(new TF\Commands\Common\RemoveKVSplotsWithoutEKTTECommand());
$app->command(new TF\Commands\Common\GetUsersWithRentPerPlotCommand());
$app->command(new TF\Commands\Common\CreateMissingLayerColumnsByDefinitionsCommand());
$app->command(new TF\Commands\Common\CreateMissingLayerDefinitionsByTableColumnsCommand());
$app->command(new TF\Commands\Common\GetUsersWithDuplicatedRentaNatCommand());
$app->command(new TF\Commands\Common\GetUsersWithModuleMortgageCommand());
$app->command(new TF\Commands\Common\GenerateStatisticOfUsingOwnersFieldsCommand());
$app->command(new TF\Commands\Common\GenerateStatisticOfUsingPaymentMethodsCommand());
$app->command(new TF\Commands\Common\GenerateStatisticOfPaidWrongRoundedRents());
$app->command(new TF\Commands\Common\GenerateStatisticOfContractsNotValidFarmingYearDatesCommand());
$app->command(new TF\Commands\Common\FixSubleaseContractsDates());
$app->command(new TF\Commands\Common\GenerateStatisticOfInvalidPlotAreasCommand());

$app->command(new TF\Commands\Common\InitConsolidatationUserLayersCommand());
$app->command(new TF\Commands\Common\RecreateConsolidatationViewsLayersCommand());

$app->command(new TF\Commands\Common\UpdateAllowableAreaInKVS());
$app->command(new TF\Commands\Common\FixAllowableAreaAfterIntersection());
$app->command(new TF\Commands\Common\ClearDataOfWrongDeletedPlots());
$app->command(new TF\Commands\Common\GetOrganizationsWithSubleasedPlotsFromInactiveContractsCommand());
$app->command(new TF\Commands\Common\SyncLayerDefinitionsWithPhysicalTableCommand());
$app->command(new TF\Commands\Common\GetCountOfContractsWithDifferentPeriodsCommand());

$accountsReportCommand = new TF\Commands\Common\AccountsReportCommand();
$accountsReportCommand->setPradoApp($application);
$app->command($accountsReportCommand);

$getUsersEkattesCommand = new TF\Commands\Common\GetUsersEkattesCommand();
$getUsersEkattesCommand->setPradoApp($application);
$app->command($getUsersEkattesCommand);

$addUserFarmingsPermissions = new TF\Commands\Common\AddUserFarmingsPermissionsCommand();
$addUserFarmingsPermissions->setPradoApp($application);
$app->command($addUserFarmingsPermissions);

$addUserFarmingsPermissions = new TF\Commands\Common\UpdateUserPermissionsCommand();
$addUserFarmingsPermissions->setPradoApp($application);
$app->command($addUserFarmingsPermissions);

$addUserFarmingsPermissions = new TF\Commands\Common\GrantWarehouseWriteRightToAdminUsers();
$addUserFarmingsPermissions->setPradoApp($application);
$app->command($addUserFarmingsPermissions);

// version 4.0
$app->command(new TF\Commands\v4_0\TS6035_1Command());
$app->command(new TF\Commands\v4_0\TS6035_2Command());
$app->command(new TF\Commands\v4_0\TS6035_3Command());
$app->command(new TF\Commands\v4_0\TS6035_4Command());
$app->command(new TF\Commands\v4_0\TS6089Command());
$app->command(new TF\Commands\v4_0\TS6192_1Command());
$app->command(new TF\Commands\v4_0\TS6192_2Command());
$app->command(new TF\Commands\v4_0\TS6196Command());
$app->command(new TF\Commands\v4_0\TS6400_1Command());
$app->command(new TF\Commands\v4_0\TS6400_2Command());
$app->command(new TF\Commands\v4_0\TS6452Command());
$app->command(new TF\Commands\v4_0\TS6514Command());
$app->command(new TF\Commands\v4_0\TS6685Command());
$app->command(new TF\Commands\v4_0\TS6690Command());
$app->command(new TF\Commands\v4_0\TS6719_1Command());
$app->command(new TF\Commands\v4_0\TS6719_2Command());

$app->command(new TF\Commands\v4_0\TS4553Command());
$app->command(new TF\Commands\v4_0\TS3721Command());
$app->command(new TF\Commands\v4_0\GPS2180Command());

$app->command(new TF\Commands\v4_0\GPS2440Command());

$app->command(new TF\Commands\v4_0\TS6781Command());
$app->command(new TF\Commands\v4_0\TS6751Command());
$app->command(new TF\Commands\v4_1\GPS2582Command());
$app->command(new TF\Commands\v4_1\GPS2618Command());

$app->command(new TF\Commands\v4_0\UpdateToVersion4_0());

// version 4.1
$app->command(new TF\Commands\v4_1\GPS2453Command());
$app->command(new TF\Commands\v4_1\TS6585_1Command());
$app->command(new TF\Commands\v4_1\GPS2818Command());
$app->command(new TF\Commands\v4_1\GPS2964Command());
$app->command(new TF\Commands\v4_1\GPS2964_1Command());
$app->command(new TF\Commands\v4_1\GPS2942_1Command());
$app->command(new TF\Commands\v4_1\GPS2942_2Command());
$app->command(new TF\Commands\v4_1\GPS2942_3Command());
$app->command(new TF\Commands\v4_1\GPS2942_4Command());
$app->command(new TF\Commands\v4_1\GPS2942_5Command());
$app->command(new TF\Commands\v4_1\TS6711Command());
$app->command(new TF\Commands\v4_1\TS6711_1Command());
$app->command(new TF\Commands\v4_1\GPS3169Command());
$app->command(new TF\Commands\v4_1\GPS3169_1Command());
$app->command(new TF\Commands\v4_1\GPS3227Command());
$app->command(new TF\Commands\v4_1\GPS3255_1Command());
$app->command(new TF\Commands\v4_1\GPS3255_2Command());
$app->command(new TF\Commands\v4_1\GPS3255_3Command());
$app->command(new TF\Commands\v4_1\GPS3255_4Command());
$app->command(new TF\Commands\v4_1\GPS3520Command());
$app->command(new TF\Commands\v4_1\GPS3401Command());
$app->command(new TF\Commands\v4_1\GPS3481_1Command());
$app->command(new TF\Commands\v4_1\GPS3481_2Command());
$app->command(new TF\Commands\v4_1\GPS3481_3Command());
$app->command(new TF\Commands\v4_1\GPS3481_4Command());
$app->command(new TF\Commands\v4_1\GPS3481_5Command());
$app->command(new TF\Commands\v4_1\GPS3481_6Command());
$app->command(new TF\Commands\v4_1\GPS3481_7Command());
$app->command(new TF\Commands\v4_1\GPS3481_8Command());
$app->command(new TF\Commands\v4_1\GPS3481_9Command());
$app->command(new TF\Commands\v4_1\GPS3481_10Command());
$app->command(new TF\Commands\v4_1\GPS3481_11Command());
$app->command(new TF\Commands\v4_1\GPS3437Command());
$app->command(new TF\Commands\v4_1\GPS3519Command());
$app->command(new TF\Commands\v4_1\GPS3254_1Command());
$app->command(new TF\Commands\v4_1\GPS3254_2Command());
$app->command(new TF\Commands\v4_1\GPS3254_3Command());
$app->command(new TF\Commands\v4_1\GPS3540Command());
$app->command(new TF\Commands\v4_1\GPS3540_1Command());
$app->command(new TF\Commands\v4_1\GPS3540_2Command());
$app->command(new TF\Commands\v4_1\GPS3549Command());

$app->command(new TF\Commands\v4_1\UpdateToVersion4_1());

// version 4.2
$app->command(new TF\Commands\v4_2\GPS3523Command());
$app->command(new TF\Commands\v4_2\GPS3523_1Command());
$app->command(new TF\Commands\v4_2\GPS3523_2Command());
$app->command(new TF\Commands\v4_2\GPS3573Command());
$app->command(new TF\Commands\v4_2\GPS3573_1Command());
$app->command(new TF\Commands\v4_2\GPS3573_2Command());
$app->command(new TF\Commands\v4_2\GPS3573_3Command());
$app->command(new TF\Commands\v4_2\GPS3573_4Command());
$app->command(new TF\Commands\v4_2\GPS3573_5Command());
$app->command(new TF\Commands\v4_2\GPS3573_6Command());
$app->command(new TF\Commands\v4_2\GPS3573_7Command());
$app->command(new TF\Commands\v4_2\GPS3573_8Command());
$app->command(new TF\Commands\v4_2\GPS3573_9Command());
$app->command(new TF\Commands\v4_2\GPS3573_10Command());
$app->command(new TF\Commands\v4_2\GPS3573_11Command());
$app->command(new TF\Commands\v4_2\GPS3573_12Command());
$app->command(new TF\Commands\v4_2\GPS3573_13Command());
$app->command(new TF\Commands\v4_2\GPS3573_14Command());
$app->command(new TF\Commands\v4_2\GPS3573_15Command());
$app->command(new TF\Commands\v4_2\GPS3573_16Command());
$app->command(new TF\Commands\v4_2\GPS3573_17Command());
$app->command(new TF\Commands\v4_2\GPS3573_18Command());
$app->command(new TF\Commands\v4_2\GPS3573_19Command());
$app->command(new TF\Commands\v4_2\GPS3636Command());
$app->command(new TF\Commands\v4_2\GPS3675Command());
$app->command(new TF\Commands\v4_2\GPS3675_1Command());
$app->command(new TF\Commands\v4_2\GPS3675_2Command());
$app->command(new TF\Commands\v4_2\GPS3675_3Command());
$app->command(new TF\Commands\v4_2\GPS3675_4Command());
$app->command(new TF\Commands\v4_2\GPS3675_5Command());
$app->command(new TF\Commands\v4_2\GPS3702_1Command());
$app->command(new TF\Commands\v4_2\GPS3702_2Command());
$app->command(new TF\Commands\v4_2\GPS3719Command());
$app->command(new TF\Commands\v4_2\GPS3758_1Command());
$app->command(new TF\Commands\v4_2\GPS3684_1Command());
$app->command(new TF\Commands\v4_2\GPS3684_2Command());
$app->command(new TF\Commands\v4_2\GPS3684_3Command());
$app->command(new TF\Commands\v4_2\GPS3684_4Command());
$app->command(new TF\Commands\v4_2\GPS3848Command());
$app->command(new TF\Commands\v4_2\GPS3684_5Command());
$app->command(new TF\Commands\v4_2\GPS3684_6Command());
$app->command(new TF\Commands\v4_2\GPS2633Command());
$app->command(new TF\Commands\v4_2\GPS2633_1Command());
$app->command(new TF\Commands\v4_2\GPS3881Command());
$app->command(new TF\Commands\v4_2\GPS3851Command());
$app->command(new TF\Commands\v4_2\GPS3871Command());
$app->command(new TF\Commands\v4_2\GPS3953_Command());
$app->command(new TF\Commands\v4_2\GPS3969_Command());
$app->command(new TF\Commands\v4_2\GPS3969_1Command());
$app->command(new TF\Commands\v4_2\GPS4052Command());
$app->command(new TF\Commands\v4_2\GPS3987Command());
$app->command(new TF\Commands\v4_2\GPS3869Command());
$app->command(new TF\Commands\v4_2\GPS4036Command());
$app->command(new TF\Commands\v4_2\GPS4036_1Command());
$app->command(new TF\Commands\v4_2\GPS4014Command());
$app->command(new TF\Commands\v4_2\GPS4040Command());
$app->command(new TF\Commands\v4_2\GPS2906Command());
$app->command(new TF\Commands\v4_2\GPS4123Command());
$app->command(new TF\Commands\v4_2\GPS4172_1Command());
$app->command(new TF\Commands\v4_2\GPS4172_2Command());
$app->command(new TF\Commands\v4_2\GPS4172_3Command());
$app->command(new TF\Commands\v4_2\GPS4172_4Command());
$app->command(new TF\Commands\v4_2\GPS4172_5Command());
$app->command(new TF\Commands\v4_2\GPS4173_1Command());
$app->command(new TF\Commands\v4_2\GPS4163Command());
$app->command(new TF\Commands\v4_2\GPS4163_1Command());
$app->command(new TF\Commands\v4_2\GPS4163_2Command());
$app->command(new TF\Commands\v4_2\GPS4163_3Command());
$app->command(new TF\Commands\v4_2\GPS4163_4Command());
$app->command(new TF\Commands\v4_2\GPS4179Command());
$app->command(new TF\Commands\v4_2\GPS4071Command());
$app->command(new TF\Commands\v4_2\GPS4184Command());
$app->command(new TF\Commands\v4_2\GPS4346_1Command());
$app->command(new TF\Commands\v4_2\GPS4346_2Command());
$app->command(new TF\Commands\v4_2\GPS4441Command());
$app->command(new TF\Commands\v4_2\GPS4443Command());
$app->command(new TF\Commands\v4_2\GPS4235Command());
$app->command(new TF\Commands\v4_2\GPS4460Command());
$app->command(new TF\Commands\v4_2\GPS4480Command());
$app->command(new TF\Commands\v4_2\GPS4497Command());
$app->command(new TF\Commands\v4_2\GPS4515_1Command());
$app->command(new TF\Commands\v4_2\GPS4515_2Command());
$app->command(new TF\Commands\v4_2\FixStylesCommand());
$app->command(new TF\Commands\v4_2\GPS4490Command());
$app->command(new TF\Commands\v4_2\GPS4490_1Command());

$app->command(new TF\Commands\v4_2\UpdateToVersion4_2());

// version 5
$app->command(new TF\Commands\v5\GPS4520Command());
$app->command(new TF\Commands\v5\GPS4492Command());
$app->command(new TF\Commands\v5\GPS4553Command());
$app->command(new TF\Commands\v5\GPS4565Command());
$app->command(new TF\Commands\v5\GPS4606Command());
$app->command(new TF\Commands\v5\GPS4713Command());
$app->command(new TF\Commands\v5\GPS4716Command());
$app->command(new TF\Commands\v5\GPS4664Command());
$app->command(new TF\Commands\v5\GPS4670Command());
$app->command(new TF\Commands\v5\GPS4742_1Command());
$app->command(new TF\Commands\v5\GPS4742_2Command());
$app->command(new TF\Commands\v5\GPS4742_3Command());
$app->command(new TF\Commands\v5\GPS4742_4Command());
$app->command(new TF\Commands\v5\GPS4742_5Command());
$app->command(new TF\Commands\v5\GPS4521Command());
$app->command(new TF\Commands\v5\GPS4808_1Command());
$app->command(new TF\Commands\v5\GPS4808_2Command());
$app->command(new TF\Commands\v5\GPS4878_1Command());
$app->command(new TF\Commands\v5\GPS4878_2Command());
$app->command(new TF\Commands\v5\GPS4878_3Command());
$app->command(new TF\Commands\v5\GPS4896_1Command());
$app->command(new TF\Commands\v5\GPS4896_2Command());
$app->command(new TF\Commands\v5\GPS4896_3Command());
$app->command(new TF\Commands\v5\GPS4896_4Command());
$app->command(new TF\Commands\v5\GPS4918Command());
$app->command(new TF\Commands\v5\GPS4934Command());
$app->command(new TF\Commands\v5\GPS4952Command());
$app->command(new TF\Commands\v5\GPS4959Command());
$app->command(new TF\Commands\v5\GPS4950Command());
$app->command(new TF\Commands\v5\GPS4958Command());
$app->command(new TF\Commands\v5\GPS4936Command());
$app->command(new TF\Commands\v5\GPS4926_1Command());
$app->command(new TF\Commands\v5\GPS4926_2Command());
$app->command(new TF\Commands\v5\GPS4926_3Command());
$app->command(new TF\Commands\v5\GPS4926_4Command());
$app->command(new TF\Commands\v5\GPS4926_5Command());
$app->command(new TF\Commands\v5\GPS4926_6Command());
$app->command(new TF\Commands\v5\GPS4926_7Command());
$app->command(new TF\Commands\v5\GPS4926_8Command());
$app->command(new TF\Commands\v5\GPS4926_9Command());
$app->command(new TF\Commands\v5\GPS4926_10Command());
$app->command(new TF\Commands\v5\GPS5006Command());
$app->command(new TF\Commands\v5\GPS5039Command());
$app->command(new TF\Commands\v5\GPS5064Command());
$app->command(new TF\Commands\v5\GPS4862Command());
$app->command(new TF\Commands\v5\GPS5084Command());
$app->command(new TF\Commands\v5\GPS5127Command());
$app->command(new TF\Commands\v5\GPS5161Command());
$app->command(new TF\Commands\v5\GPS5080Command());

$app->command(new TF\Commands\v5\UpdateToVersion5());
$app->command(new TF\Commands\v5\SpecificRentsMigrations());
$app->command(new TF\Commands\v5\RevertSpecificRentsMigrations());

// version 5.1
$app->command(new TF\Commands\v5_1\UpdateToVersion5_1());

try {
    $app->run(null, new TF\Commands\Common\Custom\TFConsoleOutput());
} catch (Throwable $th) {
    Sentry::logException($th);
}
